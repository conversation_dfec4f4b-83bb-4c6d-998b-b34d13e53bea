# Backing services are created using Github Actions service containers
# https://docs.github.com/en/actions/use-cases-and-examples/using-containerized-services/about-service-containers
# Using docker containers alongside service containers is poorly documented
# This discussion explains how the networking is set up: https://github.com/orgs/community/discussions/26094

name: E2E Tests

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  e2e-test:
    runs-on: ubuntu-latest

    services:
      db:
        image: pgvector/pgvector:pg17
        env:
          POSTGRES_USER: root
          POSTGRES_PASSWORD: strongPASS
      redis:
        image: redis:latest

    steps:
      - uses: actions/checkout@v3
      - uses: docker/setup-buildx-action@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "22"
          cache: "npm"

      - name: Prepare env
        run: cp api/.env.example api/.env

      - name: Build  Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: docker/Dockerfile
          tags: app:latest
          push: false
          load: true
          cache-from: type=gha,scope=app-image
          cache-to: type=gha,mode=max,scope=app-image

      - name: Run Docker container
        run: >
          docker run -d \
              --env-file ./api/.env \
              --env DATABASE_URL=********************************** \
              --env REDIS_HOST=redis \
              --name app \
              --network=${{ job.container.network }} \
              app:latest
      - name: Add seed data for testing
        run: docker exec app npm run prisma:seed:test

      - name: Build playwright Docker image
        uses: docker/build-push-action@v5
        with:
          context: test
          file: test/docker/Dockerfile
          tags: e2e-test:latest
          push: false
          load: true
          cache-from: type=gha,scope=test-image
          cache-to: type=gha,mode=max,scope=test-image

      - name: Run playwright Docker container
        run: >
          docker run \
            --env BASE_URL=http://app:3000 \
            --name e2e-test-container \
            --network=${{ job.container.network }} \
            e2e-test:latest
                  # Show Docker container logs if tests fail

      - name: Show Docker container logs if tests fail
        if: failure()
        run: |
          echo "Tests failed, showing Docker logs..."
          docker logs app
