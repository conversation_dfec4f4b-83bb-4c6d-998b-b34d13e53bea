# Backing services are created using Github Actions service containers
# https://docs.github.com/en/actions/use-cases-and-examples/using-containerized-services/about-service-containers
name: API checks

on:
    push:
        branches: [main, staging, development]
        paths: api/**
    pull_request:
        branches: [main, staging, development]
        paths: api/**

jobs:
    checks:
        runs-on: ubuntu-latest
        defaults:
          run:
            working-directory: api

        services:
          # https://docs.github.com/en/actions/use-cases-and-examples/using-containerized-services/creating-postgresql-service-containers#running-jobs-directly-on-the-runner-machine
          db:
            image: pgvector/pgvector:pg17
            env:
              POSTGRES_USER: root
              POSTGRES_PASSWORD: strongPASS
            options: >-
              --health-cmd pg_isready
              --health-interval 10s
              --health-timeout 5s
              --health-retries 5
            ports:
              - 5432:5432

          # https://docs.github.com/en/actions/use-cases-and-examples/using-containerized-services/creating-redis-service-containers#running-jobs-directly-on-the-runner-machine
          redis:
            image: redis:latest
            options: >-
              --health-cmd "redis-cli ping"
              --health-interval 10s
              --health-timeout 5s
              --health-retries 5
            ports:
              - 6379:6379

        steps:
            - uses: actions/checkout@v3

            - name: Set up Node.js
              uses: actions/setup-node@v3
              with:
                  node-version: "22"
                  cache: "npm"

            - name: Install dependencies
              run: npm ci

            - name: Check formatting
              run: npm run format

            - name: Check linting
              run: npm run lint

            - name: Run unit tests
              run: npm run test

            - name: Prepare env
              run: cp .env.example .env

            - name: Set up database
              run: npm run setup-database

            - name: Run E2E backend tests
              run: npm run test:e2e
