# Truthkeep-phone-demo

## An AI phone agent by Truthkeep.

![](./screenshots/agent-builder.png)

<hr>

### README Contents:

- [Basic setup](#basic-setup)
- [Running tests](#running-tests)
- [Viewing API documentation](#api-documentation)
- [Making commits](#making-commits)
- [Deploying the application](#deployment)
- [Architecture](#architecture)
- [External resources](#external-resources)

## Basic Setup

Start by creating a `.env` file in the `api` directory. See `api/.env.example`
for the required variables.

Ensure Docker is installed and running.

Ensure you are running at least version 18 of Node.js.

### Start up backing services

```
cd api
docker-compose up
```

### Start up NestJS server

```
cd api
npm install
npm run setup-database
npm run start:dev
```

### Start up frontend

```
cd web
npm install
npm run dev
```

You can now open the app and log in using the admin credentials in your `.env`
file.

## Running tests

### E2E tests

End-to-end tests with [<PERSON><PERSON>](https://playwright.dev/) are found in the
`test` directory.

Convenience scripts for running tests:

```
# Runs development builds of frontend and backend
# Both have live reloading enabled
# Opens Playwright UI

./test.dev.sh
```

```
# Runs production builds of both frontend and backend
# NO live reloading will be enabled
# Runs headlines tests with Playwright

./test.prod.sh
```

### Backend e2e tests

The `api` directory contains its own [Jest](https://jestjs.io/) tests for
testing the backend end-to-end (with mocked 3rd party APIs).

```
cd api
npm run test:e2e
```

### Unit tests

Run unit tests for the backend:

```
cd api
npm test
```

Run unit tests for the frontend:

```
cd web
npm run vitest
```

## API documentation

Swagger docs are visible from the following URL when running the project:

```
http://localhost:3000/api
```

You can read more about Nest and Swagger here:
https://docs.nestjs.com/openapi/introduction.

## Making commits

Linting and formatting is enforced at commit time with
[husky](https://typicode.github.io/husky/).

We also use [lint-staged](https://www.npmjs.com/package/lint-staged) in order to
speed this up.

### Backend

To fix linting errors in the `api` directory you can use the following command:

```
# inside ./api

npm run format:fix
```

### Frontend

To fix linting errors in the `web` directory you can use the following command:

```
# inside ./web

npm run prettier:write
```

## Deployment

TODO: implement automated deployments

## Architecture

### Backend

#### Database

Postgres is the database provider.

We also use Prisma to manage database migrations and running queries. To change
the database schema first edit the file `prisma/schema.prisma` then run:

```
# inside ./api
npm run migrate:dev
```

You can read more about Prisma here: https://www.prisma.io

#### Authentication

The authentication implementation uses JWTs stored as
[HTTP only](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie#httponly)
cookies. Authentication tokens have a short expiry and can be replaced using a
refresh token.


### Frontend

The frontend in this project is based on the
[Mantine Vite Template](https://github.com/mantinedev/vite-template).

#### Storybook

This project includes [Storybook](https://storybook.js.org/) to make it easier
to develop beautiful components.

To run Storybook:

```
# inside ./web
npm run storybook
```

To build a production storybook bundle to `storybook-static`:

```
# inside ./web
npm run storybook:build
```

## External resources:

This project can be run locally or deployed on Amazon Web Services.

The following resources are required to successfully run the project with its
full capabilities.

* Twilio
* OpenAI