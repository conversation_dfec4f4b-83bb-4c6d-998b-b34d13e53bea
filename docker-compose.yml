# this is meant to mirror how github actions will spin up services
version: "3.8"

services:
  app:
    build:
      context: .
      dockerfile: docker/Dockerfile
    env_file:
      - api/.env
    environment:
      DATABASE_URL: **********************************
      REDIS_HOST: redis
    ports:
      - "3000:3000"
    depends_on:
      - db
    stop_grace_period: 0s

  db:
    image: pgvector/pgvector:pg17
    container_name: postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: strongPASS

  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    command: ["redis-server", "--appendonly", "yes"]
