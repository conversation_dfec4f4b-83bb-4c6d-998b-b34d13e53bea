import { randomUUID } from "crypto";
import { test, expect } from "@playwright/test";
import { getNextPhoneNumber, login, waitFor } from "./util";

test.skip("Create agent", async ({ page }) => {
  await login(page);

  await page.goto("/agent-builder");

  const agentName = `Test Agent ${randomUUID()}`;
  await page.getByRole("button", { name: "Create New Agent" }).click();

  const createAgentDialog = await page.getByRole("dialog", {
    name: "Create New Agent",
  });

  await createAgentDialog.getByRole("radio", { name: "Phone Agent" }).check();

  await createAgentDialog
    .getByRole("textbox", { name: "Agent Name" })
    .fill(agentName);

  await createAgentDialog
    .getByRole("textbox", { name: "Description" })
    .fill("Test description");

  await createAgentDialog.getByRole("button", { name: "Create Agent" }).click();

  // Check that it is added to the list
  const agentCard = page.getByRole("region", { name: agent<PERSON><PERSON> });
  await expect(agentCard).toBeVisible();
  await expect(agentCard.getByText("Test description")).toBeVisible();
});

test.skip("Edit agent", async ({ page }) => {
  await login(page);

  await page.goto("/agent-builder");

  // Check the original phone number
  const originalPhoneNumber = await page.getByRole("region", {
    name: "Sales Inquiry Phone Agent",
  }).getByText(/\+1 \d\d\d \d\d\d \d\d\d\d/).textContent()
  const newPhoneNumber = originalPhoneNumber ? getNextPhoneNumber(originalPhoneNumber) : ""
  const cleanNewPhoneNumber = newPhoneNumber.replaceAll(" ", "")

  // Fake that the next phone number will always be available
  await page.route("**/phone-numbers", async (route) => {
    await route.fulfill({
      status: 200,
      contentType: "application/json",
      body: JSON.stringify({
        items: [
          {
            phoneNumber: cleanNewPhoneNumber,
            isInUse: false
          }
        ]
      })
    });
  });

  // Open edit agent modal
  await page
    .getByRole("region", { name: "Sales Inquiry Phone Agent" })
    .getByRole("button", { name: "edit" })
    .click();

  const editAgentDialog = await page.getByRole("dialog", {
    name: "Edit Agent",
  });

  // Change the phone number
  await editAgentDialog
    .getByRole("combobox")
    .selectOption(newPhoneNumber)

  // Open "Add Event" modal
  await editAgentDialog
    .getByRole("button", { name: "Add Event" })
    .click();

  const eventDialog = await page.getByRole("dialog", { name: "Add New Event" });

  // Create a new event
  const eventName = `Test Event ${randomUUID()}`;
  await eventDialog
    .getByRole("textbox", { name: "Event Name" })
    .fill(eventName);

  await eventDialog
    .getByRole("textbox", { name: "Description" })
    .fill("Test description");

  await eventDialog.getByRole("checkbox", { name: "create ticket" }).check();

  await eventDialog.getByRole("button", { name: "Add Event" }).click();

  // Check that the event is visible in the list of events
  await waitFor(async () => {
    expect(page.getByRole("region", { name: "Events" })).toBeVisible();
    const eventListItem = await editAgentDialog
      .getByRole("region", { name: "Events" })
      .getByRole("listitem", { name: eventName });
    expect(eventListItem).toBeVisible();
    return true;
  });

  await expect(
    page.getByRole("button", { name: "Save Changes" })
  ).toBeVisible();

  // Check that the changes are persisted
  await page.getByRole("button", { name: "Save Changes" }).click();

  await expect(editAgentDialog).toBeHidden();

  const agentRegion = await page.getByRole("region", {
    name: "Sales Inquiry Phone Agent",
  });
  await expect(agentRegion).toBeVisible();
  await expect(agentRegion.getByText(newPhoneNumber)).toBeVisible()

  const eventItem = agentRegion.getByRole("listitem", { name: eventName });
  await expect(eventItem).toBeVisible();

  // Delete the event from the agent list view
  await eventItem.getByRole("button", { name: "Delete event" }).click();

  const deleteConfirmationDialog = await page.getByRole("dialog", {
    name: `Delete ${eventName}`,
  });
  await expect(deleteConfirmationDialog).toBeVisible();

  await deleteConfirmationDialog
    .getByRole("button", { name: "Delete" })
    .click();

  await expect(deleteConfirmationDialog).toBeHidden();

  await expect(eventItem).toBeHidden();
});

test.skip("Add event to agent", async ({ page }) => {
  await login(page);

  await page.goto("/agent-builder");

  // Open edit agent modal
  await page
    .getByRole("region", { name: "Sales Inquiry Phone Agent" })
    .getByRole("button", { name: "Add Event" })
    .click();

  const addEventDialog = await page.getByRole("dialog", {
    name: "Add New Event",
  });

  await expect(addEventDialog).toBeVisible();

  // Create a new event
  const eventName = `Test Event ${randomUUID()}`;
  await addEventDialog
    .getByRole("textbox", { name: "Event Name" })
    .fill(eventName);

  await addEventDialog
    .getByRole("textbox", { name: "Description" })
    .fill("Test description");

  await addEventDialog.getByRole("checkbox", { name: "forward call" }).check();

  await addEventDialog
    .getByRole("textbox", { name: "Forward to" })
    .fill("+14162223323")

  await addEventDialog.getByRole("button", { name: "Add Event" }).click();

  await expect(addEventDialog).toBeHidden();

  const newEventItem = page
    .getByRole("region", { name: "Sales Inquiry Phone Agent" })
    .getByRole("listitem", { name: eventName });

  await expect(newEventItem).toBeVisible();
  await expect(newEventItem.getByText(/forward.*\+14162223323/i)).toBeVisible()
});

test.skip("Delete Agent", async ({ page }) => {
  await login(page);

  // Agent builder test
  await page.goto("/agent-builder");

  // Create a new agent
  const agentName = `Test Agent ${randomUUID()}`;
  await page.getByRole("button", { name: "Create New Agent" }).click();

  const createAgentDialog = await page.getByRole("dialog", {
    name: "Create New Agent",
  });

  await createAgentDialog.getByRole("radio", { name: "Phone Agent" }).check();

  await createAgentDialog
    .getByRole("textbox", { name: "Agent Name" })
    .fill(agentName);

  await createAgentDialog.getByRole("button", { name: "Create Agent" }).click();

  // Delete the agent
  await page
    .getByRole("region", { name: agentName })
    .getByRole("button", { name: "delete" })
    .click();

  const deleteConfirmationDialog = await page.getByRole("dialog", {
    name: `Delete ${agentName}`,
  });

  expect(deleteConfirmationDialog.getByText("Are you sure")).toBeVisible();

  await deleteConfirmationDialog
    .getByRole("button", { name: "Delete" })
    .click();

  // Check that the agent is no longer present
  await expect(page.getByRole("region", { name: agentName })).toBeHidden();
});

test.skip("Phone agent UI", async ({ page }) => {
  await login(page);

  // Fake that some number is always available
  await page.route("**/phone-numbers", async (route) => {
    await route.fulfill({
      status: 200,
      contentType: "application/json",
      body: JSON.stringify({
        items: [
          {
            phoneNumber: "+16475550123",
            isInUse: false
          }
        ]
      })
    });
  });

  // Open the Create Agent modal
  await page.goto("/agent-builder");
  await page.getByRole("button", { name: "Create New Agent" }).click();
  const createAgentDialog = await page.getByRole("dialog", {
    name: "Create New Agent",
  });

  // Initially no phone number UI is displayed
  await expect(createAgentDialog.getByText("Phone Number")).toBeHidden()

  // After selecting "Phone Agent" type, the phone number UI is displayed
  await createAgentDialog.getByRole("radio", { name: "Phone Agent" }).check();
  const phoneNumberSelect = createAgentDialog.getByRole("combobox", { name: "Phone Number" })
  await expect(phoneNumberSelect).toBeVisible()

  // The number ****** 555 0123 is selected by default 
  await expect(phoneNumberSelect).toHaveValue("+16475550123")

  // Fill out the other required fields
  const agentName = `phone agent ${randomUUID()}`
  await createAgentDialog
    .getByRole("textbox", { name: "Agent name" })
    .fill(agentName);

  // Submit the form
  await createAgentDialog.getByRole("button", { name: "Create Agent" }).click();

  // Check that it is added to the list with the number ****** 555 0123 set
  const agentCard = page.getByRole("region", { name: agentName });
  await expect(agentCard).toBeVisible();
  await expect(agentCard.getByText("****** 555 0123")).toBeVisible()

  // Delete the agent to free up ****** 555 0123 again
  await page
    .getByRole("region", { name: agentName })
    .getByRole("button", { name: "delete" })
    .click();

  await page
    .getByRole("dialog")
    .getByRole("button", { name: "Delete" })
    .click();
});