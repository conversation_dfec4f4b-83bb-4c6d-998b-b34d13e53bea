import { test, expect } from "@playwright/test";
import { randomUUID } from "crypto";
import { login } from "./util";

test("Failed login", async ({ page }) => {
  const email = `user-${randomUUID()}@example.com`;
  const strongPassword = "StrongPassword123!";

  await page.goto("/login");

  await page.getByRole("textbox", { name: "Username" }).fill(email);
  await page.getByRole("textbox", { name: "Password" }).fill(strongPassword);
  await page.getByRole("button", { name: "Login" }).click();

  await expect(
    page.getByText("Invalid email or password").first()
  ).toBeVisible();
});

test("Visiting login page while already being authenticated brings you to home page", async ({
  page,
}) => {
  await login(page);

  await page.goto("/login");
  await page.waitForURL((url) => url.pathname !== "/login");
  await expect(page.getByText("Agent Builder")).toBeVisible();
});
