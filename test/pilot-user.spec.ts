import { test, expect } from "@playwright/test";
import { loginAsChatPilotUser } from "./util";

test("Redirects to pilot page", async ({ page }) => {
  await loginAsChatPilotUser(page);

  // Internal chat agent exists
  {
    const launchChatButton = await page
      .getByRole("region", { name: "Internal Chat Agent" })
      .getByRole("button", { name: "Launch live chat" });
    await expect(launchChatButton).toBeVisible();
  }

  // External chat agent exists
  {
    const launchChatButton = await page
      .getByRole("region", { name: "External Chat Agent" })
      .getByRole("button", { name: "Launch live chat" });
    await expect(launchChatButton).toBeVisible();
  }

  // Main nav is hidden
  const nav = await page.getByRole("navigation", { name: "Main navigation" });
  await expect(nav).not.toBeVisible();
});

test(`"Create new agent" button is hidden`, async ({ page }) => {
  await loginAsChatPilotUser(page);

  const createNewAgentButton = await page.getByRole("button", {
    name: "Create New Agent",
  });
  await expect(createNewAgentButton).not.toBeVisible();

  const deleteAgentButton = await page.getByRole("button", {
    name: "delete",
  });
  await expect(deleteAgentButton).not.toBeVisible();
});

test(`Agent delete button is hidden`, async ({ page }) => {
  await loginAsChatPilotUser(page);

  const deleteAgentButton = await page.getByRole("button", {
    name: "delete",
  });
  await expect(deleteAgentButton).not.toBeVisible();
});

test("Logout", async ({ page }) => {
  await loginAsChatPilotUser(page);

  await page.getByRole("button", { name: "<EMAIL>" }).click();
  await page.getByRole("button", { name: "Logout" }).click();

  await expect(page.getByRole("button", { name: "Login" })).toBeVisible();
});
