import { Page } from "@playwright/test";

export async function login(page: Page) {
  await page.goto("/login");

  await page
    .getByRole("textbox", { name: "Username" })
    .fill("<EMAIL>");
  await page
    .getByRole("textbox", { name: "Password" })
    .fill("strongPASSWORD9@!");
  await page.getByRole("button", { name: "Login" }).click();
  await page.waitForURL((url) => url.pathname !== "/login");
}

export async function loginAsChatPilotUser(page: Page) {
  await page.goto("/login");

  await page
    .getByRole("textbox", { name: "Username" })
    .fill("<EMAIL>");
  await page.getByRole("textbox", { name: "Password" }).fill("changeME1@!");
  await page.getByRole("button", { name: "Login" }).click();
  await page.waitForURL("/pilot");
}

/**
 * This allows you to wait for something (like an email to be available).
 *
 * It calls the callback every 50ms until it returns a value (and does not throw
 * an error). After the timeout, it will throw the last error that was thrown
 */
export async function waitFor<ReturnValue>(
  cb: () => ReturnValue | Promise<ReturnValue>,
  timeout = 5000
) {
  const endTime = Date.now() + timeout;
  let lastError;
  while (Date.now() < endTime) {
    try {
      const response = await cb();
      if (response) return response;
    } catch (e: unknown) {
      lastError = e;
    }
    await new Promise((r) => setTimeout(r, 100));
  }
  throw lastError;
}

export function getNextPhoneNumber(phoneNumber: string) {
  const trimmedPhoneNumber = phoneNumber.trim();
  const lastDigit = Number(trimmedPhoneNumber.slice(-1));
  const nextLastDigit = (1 + lastDigit) % 10;
  return `${trimmedPhoneNumber.slice(0, -1)}${nextLastDigit}`;
}
