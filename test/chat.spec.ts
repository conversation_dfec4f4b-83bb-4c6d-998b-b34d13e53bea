import test, { expect } from "@playwright/test";
import { login } from "./util";

test.skip("Chat", async ({ page }) => {
  await login(page);

  await page.goto("/agent-builder");

  const chatAgentRegion = await page.getByRole("region", {
    name: "Live Chat Support",
  });

  // Open chat modal
  const launchChatButton = chatAgentRegion.getByRole("button", {
    name: "Launch Live Chat",
  });
  await expect(launchChatButton).toBeVisible();
  launchChatButton.click();

  // Check chat modal is open
  const chatModal = await page.getByRole("dialog", {
    name: "Live Chat Support",
  });
  await expect(chatModal).toBeVisible();

  const input = chatModal.getByRole("textbox");
  input.fill("Hello");
  input.press("Enter");

  await expect(input).toBeEmpty();
  await expect(chatModal).toContainText("Hello");

  // Close chat modal
  await chatModal.getByRole("button", { name: "Close" }).click();
  await expect(chatModal).toBeHidden();
});
