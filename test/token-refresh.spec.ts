import { test, expect } from "@playwright/test";
import { waitFor } from "./util";
import { randomUUID } from "crypto";

const email = `user-${randomUUID()}@example.com`;

test("Token refresh should trigger when a request fails with 401", async ({
  page,
}) => {
  const networkCalls: string[] = [];
  let hasRefreshed = false;

  page.on("request", (request) => {
    networkCalls.push(request.url());
  });
  await page.route("**/refresh", async (route) => {
    hasRefreshed = true;
    await route.fulfill({
      status: 204,
    });
  });

  await page.route("**/users/me", async (route) => {
    if (!hasRefreshed) {
      await route.fulfill({
        status: 401,
      });
    } else {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify({
          id: randomUUID(),
          email: email,
          username: email,
        }),
      });
    }
  });

  await page.route("**/agents", async (route) => {
    if (!hasRefreshed) {
      await route.fulfill({
        status: 401,
      });
    } else {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify({
          items: [],
        }),
      });
    }
  });

  await page.goto("/agent-builder");

  await waitFor(() => networkCalls.find((url) => url.includes("/refresh")));

  await expect(page.getByText("Agent Builder")).toBeVisible();
});
