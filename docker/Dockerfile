# Frontend Builder
FROM node:22 AS frontend-builder
WORKDIR /usr/src/web
COPY web/package*.json ./
RUN npm install
COPY web ./
RUN npm run build

# Dependencies builder
FROM node:22 AS node_modules-builder
WORKDIR /usr/src/app
COPY api/package*.json ./
RUN npm install
COPY api/prisma prisma
ENV PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING=1
RUN npm run prisma:generate

# Builder
FROM node:22 AS builder
WORKDIR /usr/src/app
COPY --from=frontend-builder /usr/src/web/dist ./static
COPY --from=node_modules-builder /usr/src/app/node_modules ./node_modules
COPY api .
RUN npm run build

# Runner
FROM node:22-slim

RUN apt-get update
RUN apt-get install curl -y

WORKDIR /usr/src/app

COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/prisma ./prisma
COPY --from=builder /usr/src/app/tsconfig.json ./tsconfig.json
COPY --from=builder /usr/src/app/package*.json ./

COPY --from=frontend-builder /usr/src/web/dist ./static

COPY docker/entrypoint.sh docker/entrypoint.sh

EXPOSE 3000

ENTRYPOINT [ "./docker/entrypoint.sh" ]
