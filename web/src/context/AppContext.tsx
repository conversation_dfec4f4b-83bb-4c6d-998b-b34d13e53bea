import { createContext, useContext, useEffect, useState } from 'react';
import { authService } from '../api/auth/authService';
import { userService } from '../api/user/userService';

type AppProviderProps = {
  children: React.ReactNode;
};

type UserInfoProps = {
  email: string;
  id: string;
  username: string;
  isSifivePilotUser: boolean;
};

const AppContext = createContext<
  | {
      initialized: boolean;
      userInfo: UserInfoProps | null;
      initialize: () => void;
    }
  | undefined
>(undefined);

export const AppProvider = ({ children }: AppProviderProps) => {
  const [initialized, setInitialized] = useState(false);
  const [userInfo, setUserInfo] = useState<UserInfoProps | null>(null);

  const initialize = async () => {
    setInitialized(false);
    try {
      await authService.refreshToken();
      const response = await userService.fetchUserProfile();
      setUserInfo(response.data);
    } catch (error) {
      setUserInfo(null);
    }
    setInitialized(true);
  };

  useEffect(() => {
    initialize();
  }, []);

  return (
    <AppContext.Provider value={{ initialized, userInfo, initialize }}>
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
