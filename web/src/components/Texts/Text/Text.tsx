type TextProps = {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  bold?: boolean;
  className?: string;
  style?: React.CSSProperties;
};

const sizeClasses = {
  sm: 'text-[12px] leading-[14px] tracking-[0%]',
  md: 'text-[14px] leading-[16px] tracking-[0%]',
  lg: 'text-[16px] leading-[18px] tracking-[0%]',
  xl: 'text-[20px] leading-[24px] tracking-[0%]',
};

export function Text({
  children,
  size = 'md',
  bold = false,
  className = '',
  style = {},
}: TextProps) {
  const combinedClass = `${bold ? 'font-bold' : ''} ${sizeClasses[size]} ${className}`.trim();

  return (
    <p className={combinedClass} style={style}>
      {children}
    </p>
  );
}
