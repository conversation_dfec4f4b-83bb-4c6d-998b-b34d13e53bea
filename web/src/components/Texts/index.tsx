import { ReactNode } from 'react';

type TextProps = {
  children: ReactNode;
  className?: string;
};

type InputLabelProps = {
  children: ReactNode;
  htmlFor?: string;
  required?: boolean;
  className?: string;
};

export function Heading({ children, className }: TextProps) {
  return <h1 className={className}>{children}</h1>;
}

export function Subheading({ children, className }: TextProps) {
  return <h2 className={className}>{children}</h2>;
}

export function InputLabel({ children, htmlFor, required, className }: InputLabelProps) {
  return (
    <label htmlFor={htmlFor} className={className}>
      {children} {required && <span className="text-red-500">*</span>}
    </label>
  );
}
