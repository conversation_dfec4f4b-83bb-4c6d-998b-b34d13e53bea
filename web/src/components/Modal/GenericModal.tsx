import React from 'react';
import { Modal as MantineModal } from '@mantine/core';

type GenericModalProps = {
  children: React.ReactNode;
  title: string;
  onClose?: () => void;
  opened: boolean;
  onExitTransitionEnd?: () => void;
};

const GenericModal = ({
  children,
  title,
  onClose = () => {},
  opened,
  onExitTransitionEnd,
}: GenericModalProps) => {
  return (
    <MantineModal
      opened={opened}
      onClose={onClose}
      title={title}
      styles={{
        title: {
          fontWeight: 700,
          fontSize: '16px',
          lineHeight: '18px',
        },
        header: {
          borderBottom: '1px solid var(--color-gray-200)',
          marginBottom: '1rem',
        },
      }}
      onExitTransitionEnd={onExitTransitionEnd}
    >
      {children}
    </MantineModal>
  );
};

export default GenericModal;
