import React from 'react';
import Button from '../../Button/Button';
import { Text } from '../../Texts/Text/Text';
import GenericModal from '../GenericModal';

type AreYouSureModal = {
  title: string;
  opened: boolean;
  onClose: () => void;
  onConfirm: () => void;
  onExitTransitionEnd: () => void;

  heading?: string;
  targetObject?: React.ReactNode;
  description?: string;
  confirmButtonText?: string;

  children?: React.ReactNode;
};

const AreYouSureModal = ({
  title,
  opened,
  onClose,
  onConfirm,
  onExitTransitionEnd,

  heading,
  targetObject = null,
  description,
  confirmButtonText = 'Delete',

  children,
}: AreYouSureModal) => {
  return (
    <GenericModal
      title={title}
      opened={opened}
      onClose={onClose}
      onExitTransitionEnd={onExitTransitionEnd}
    >
      {children ? (
        children
      ) : (
        <div className="w-full h-full flex flex-col items-center justify-center px-[20%] text-center gap-[24px]">
          {heading && <Text size="xl">{heading}</Text>}
          {targetObject}
          {description && <Text size="sm">{description}</Text>}
        </div>
      )}
      <div className="flex flex-row gap-[8px] w-full mt-[24px]">
        <Button onClick={onClose} variant="secondary">
          Cancel
        </Button>
        <Button onClick={onConfirm} variant="danger">
          {confirmButtonText}
        </Button>
      </div>
    </GenericModal>
  );
};

export default AreYouSureModal;
