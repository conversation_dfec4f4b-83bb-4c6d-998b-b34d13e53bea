import { Meta, StoryObj } from '@storybook/react';
import AreYouSureModal from './AreYouSureModal';

const meta: Meta<typeof AreYouSureModal> = {
  title: 'Components/AreYouSureModal',
  component: AreYouSureModal,
  argTypes: {
    title: { control: 'text' },
    opened: { control: 'boolean' },
    onClose: { action: 'closed' },
    onConfirm: { action: 'confirmed' },
    heading: { control: 'text' },
    targetObject: { control: 'object' },
    description: { control: 'text' },
    confirmButtonText: { control: 'text' },
    children: { control: 'object' },
  },
  args: {
    title: 'Delete Confirmation',
    opened: true,
    heading: 'Are you sure?',
    description: 'This action cannot be undone.',
    confirmButtonText: 'Delete',
  },
};

export default meta;
type Story = StoryObj<typeof AreYouSureModal>;

export const Default: Story = {
  args: {},
};

export const WithCustomContent: Story = {
  args: {
    children: (
      <div className="p-[20px] text-center">
        <h2>Custom Are you Sure Content</h2>
        <p>This custom content replaces the default layout.</p>
      </div>
    ),
  },
};
