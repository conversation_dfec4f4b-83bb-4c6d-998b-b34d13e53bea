import { ButtonHTMLAttributes, ReactNode } from 'react';
import {
  But<PERSON> as MantineButton,
  ButtonProps as MantineButtonProps,
  useMantineTheme,
} from '@mantine/core';

type CustomVariant = 'primary' | 'secondary' | 'danger' | 'secondaryDanger';
type CustomSize = 'full' | 'auto';

interface CustomButtonProps
  extends Omit<MantineButtonProps, 'variant' | 'color' | 'unstyled'>,
    Pick<ButtonHTMLAttributes<HTMLButtonElement>, 'type'> {
  children: ReactNode;
  variant?: CustomVariant;
  size?: CustomSize;
  className?: string;
  onClick?: () => void;
}

const baseStyle: React.CSSProperties = {
  fontWeight: 700,
  fontSize: '16px',
};

const Button = ({
  children,
  variant = 'primary',
  size = 'full',
  className,
  onClick,
  ...props
}: CustomButtonProps) => {
  const buttonTheme = useMantineTheme().other.button;
  const isCustom = !!className;

  const combinedStyle = !isCustom
    ? {
        ...(size === 'full' ? { width: '100%' } : {}),
        ...baseStyle,
        ...buttonTheme[variant],
      }
    : {};

  return (
    <MantineButton
      unstyled={isCustom}
      className={className}
      style={combinedStyle}
      onClick={onClick}
      {...props}
    >
      {children}
    </MantineButton>
  );
};

export default Button;
