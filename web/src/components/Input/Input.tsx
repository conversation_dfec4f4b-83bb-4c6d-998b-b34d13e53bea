import { ReactNode } from 'react';
import { Input as MantineInput, NumberInput, Select, TextInput } from '@mantine/core';

type InputProps = {
  type: 'text' | 'number' | 'dropdown' | 'email';
  onChange: (value: string | number | boolean | null) => void;

  label?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  description?: string;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  data?: string[];
  radius?: number;

  leftSection?: ReactNode;
  rightSection?: ReactNode;
};

export function Input({ type, onChange, ...props }: InputProps) {
  const handleChange = (value: any) => {
    if (type === 'text' || type === 'email') {
      onChange(value.currentTarget.value);
    } else {
      onChange(value);
    }
  };

  if (type === 'dropdown') {
    return <Select {...props} data={props.data} onChange={handleChange} />;
  } else if (type === 'number') {
    return <NumberInput {...props} onChange={handleChange} />;
  } else if (type === 'email') {
    return (
      <MantineInput.Wrapper {...props}>
        <MantineInput {...props} type="email" onChange={handleChange} />
      </MantineInput.Wrapper>
    );
  }
  return (
    <TextInput {...props} type="text" onChange={(value) => onChange(value.currentTarget.value)} />
  );
}
