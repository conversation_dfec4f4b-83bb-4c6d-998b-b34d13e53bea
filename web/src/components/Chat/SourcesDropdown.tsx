import { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Collapse } from '@mantine/core';

type Source = {
  fileName: string;
  pageNumber?: number;
};

type SourcesDropdownProps = {
  sources: Source[];
  primaryColor?: string;
};

export default function SourcesDropdown({
  sources,
  primaryColor = '#0ea5e9',
}: SourcesDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const sourceCount = sources.length;

  if (sourceCount === 0) return null;

  return (
    <div className="mt-1">
      <div
        className="flex items-center gap-2 cursor-pointer hover:opacity-80 transition-opacity text-sm"
        onClick={() => setIsOpen(!isOpen)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setIsOpen(!isOpen);
          }
        }}
        role="button"
        tabIndex={0}
        style={{ color: primaryColor }}
      >
        <div className="flex items-center justify-center w-4 h-4">
          {isOpen ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
        </div>
        <span className="font-bold" style={{ color: primaryColor }}>
          Sources ({sourceCount})
        </span>
      </div>

      <Collapse in={isOpen}>
        <div className="ml-6 mt-2 space-y-1">
          {sources.map((source, index) => (
            <div key={index} className="flex items-center gap-2">
              <span className="text-gray-500 text-xs">•</span>
              <span
                className="text-xs hover:underline cursor-pointer"
                style={{ color: primaryColor }}
              >
                {source.fileName}
                {source.pageNumber && ` (page ${source.pageNumber})`}
              </span>
            </div>
          ))}
        </div>
      </Collapse>
    </div>
  );
}
