import clsx from 'clsx';
import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Box, BoxProps } from '@mantine/core';
import SourcesDropdown from './SourcesDropdown';
import { Message } from './types';

type ChatMessageProps = {
  message: Message;
  primaryColor?: string;
};

export default function ChatMessage({ message, primaryColor = '#0ea5e9' }: ChatMessageProps) {
  if (message.role === 'assistant') {
    return (
      <AssistantMessage>
        <MarkdownText text={message.text} primaryColor={primaryColor} />
      </AssistantMessage>
    );
  }
  if (message.role === 'user') {
    return (
      <UserMessage primaryColor={primaryColor}>
        <MarkdownText text={message.text} primaryColor={primaryColor} />
      </UserMessage>
    );
  }
}

function MarkdownText({ text, primaryColor = '#0ea5e9' }: { text: string; primaryColor?: string }) {
  // Parse sources from the text
  const { processedText, sources } = parseSourcesFromText(text);

  return (
    <>
      <Markdown
        remarkPlugins={[remarkGfm]}
        components={{
          a: ({ href, children }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="underline break-words"
              style={{
                color: primaryColor,
                display: 'inline',
                verticalAlign: 'baseline',
                maxWidth: '100%',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '0.8';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1';
              }}
              title={typeof children === 'string' ? children : href}
            >
              {children}
            </a>
          ),
          blockquote: ({ children }) => (
            <blockquote className="mx-4 my-2 p-2 bg-white/50 rounded-md">{children}</blockquote>
          ),
          table: ({ children }) => (
            <div className="overflow-x-auto my-4">
              <table className="min-w-full border-collapse border border-gray-300 bg-white rounded-lg shadow-sm">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => <thead className="bg-gray-50">{children}</thead>,
          tbody: ({ children }) => <tbody className="divide-y divide-gray-200">{children}</tbody>,
          tr: ({ children }) => <tr className="hover:bg-gray-50">{children}</tr>,
          th: ({ children }) => (
            <th className="px-4 py-2 text-left text-sm font-medium text-gray-900 border-b border-gray-300">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-4 py-2 text-sm text-gray-700 border-b border-gray-200">{children}</td>
          ),
        }}
      >
        {processedText}
      </Markdown>
      {sources.length > 0 && <SourcesDropdown sources={sources} primaryColor={primaryColor} />}
    </>
  );
}

function parseSourcesFromText(text: string): {
  processedText: string;
  sources: Array<{ fileName: string; pageNumber?: number }>;
} {
  // Look for the pattern: Analyzing:\n\n**[fileName](#)  \n[fileName](#)**
  const analyzingPattern = /Analyzing:\n\n\*\*(.*?)\*\*/s;
  const match = text.match(analyzingPattern);

  if (!match) {
    return { processedText: text, sources: [] };
  }

  const sourcesSection = match[1];
  const processedText = text.replace(analyzingPattern, '');

  // Extract file names from markdown links
  const linkPattern = /\[([^\]]+)\]\(#\)/g;
  const sources: Array<{ fileName: string; pageNumber?: number }> = [];
  let linkMatch;

  while ((linkMatch = linkPattern.exec(sourcesSection)) !== null) {
    const fileName = linkMatch[1];
    // Check if fileName contains page number info (e.g., "document.pdf (page 5)")
    const pageMatch = fileName.match(/^(.+?)\s*\(page\s*(\d+)\)$/);

    if (pageMatch) {
      sources.push({
        fileName: pageMatch[1],
        pageNumber: parseInt(pageMatch[2], 10),
      });
    } else {
      sources.push({ fileName });
    }
  }

  return { processedText, sources };
}

function AssistantMessage({ children }: { children: React.ReactNode }) {
  return <BaseContainer className="bg-gray-200 self-start">{children}</BaseContainer>;
}

function UserMessage({
  children,
  primaryColor = '#0ea5e9',
}: {
  children: React.ReactNode;
  primaryColor?: string;
}) {
  return (
    <BaseContainer c="white" className="self-end" style={{ backgroundColor: primaryColor }}>
      {children}
    </BaseContainer>
  );
}

export function PendingAssistantMessage() {
  return (
    <AssistantMessage>
      <div className="flex gap-1 text-gray-700 leading-none">
        <span className="animate-pulse [animation-delay:-0.2s]">•</span>
        <span className="animate-pulse [animation-delay:0s]">•</span>
        <span className="animate-pulse [animation-delay:0.2s]">•</span>
      </div>
    </AssistantMessage>
  );
}

function BaseContainer({
  className,
  children,
  ...props
}: BoxProps & { children: React.ReactNode }) {
  return (
    <Box px={8} py={6} className={clsx(className, 'rounded-lg')} maw="75%" {...props}>
      {children}
    </Box>
  );
}
