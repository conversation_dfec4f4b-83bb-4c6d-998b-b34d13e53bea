import { useEffect, useRef } from 'react';
import { Button, Flex, Text, Textarea } from '@mantine/core';
import { useForm } from '@mantine/form';
import { Agent } from '@/types/agents';
import ChatMessage, { PendingAssistantMessage } from './ChatMessage';
import ErrorMessage from './ErrorMessage';
import type { Chat } from './types';

type ChatProps = {
  agent: Agent;
  chat: Chat;
  primaryColor?: string;
  placeholder?: string;
};

export default function Chat({
  chat,
  agent,
  primaryColor = '#0ea5e9',
  placeholder = 'Ask me anything',
}: ChatProps) {
  const form = useForm({
    initialValues: {
      message: '',
    },
  });
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const handleSubmit = (values: typeof form.values) => {
    chat.handleSubmit(values.message);
    form.reset();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(form.values);
    }
  };

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [chat.messages]);

  // Inject custom CSS to override Mantine focus styles
  useEffect(() => {
    const styleId = 'chat-input-focus-override';
    const existingStyle = document.getElementById(styleId);

    if (existingStyle) {
      existingStyle.remove();
    }

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      .mantine-Textarea-input:focus,
      .mantine-Textarea-input:focus-visible,
      .mantine-Textarea-input:focus-within,
      .mantine-Textarea-wrapper:focus-within .mantine-Textarea-input,
      .mantine-Textarea-wrapper:focus .mantine-Textarea-input {
        border-color: ${primaryColor} !important;
        box-shadow: 0 0 0 0.5px ${primaryColor} !important;
        outline: none !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      const styleElement = document.getElementById(styleId);
      if (styleElement) {
        styleElement.remove();
      }
    };
  }, [primaryColor]);

  return (
    <div className="h-full flex flex-col">
      {/* Scrollable Messages Area */}
      <div ref={scrollAreaRef} className="flex-1 overflow-y-auto overflow-x-hidden">
        <Flex
          direction="column"
          justify="flex-start"
          align="center"
          w="100%"
          gap="xs"
          px={24}
          py={16}
        >
          <Text hidden={chat.messages.length > 0} px={16} lineClamp={3} c="gray" className="mb-4">
            {agent.description}
          </Text>
          {chat.messages.map((message, i) => (
            <ChatMessage key={i} message={message} primaryColor={primaryColor} />
          ))}
          {chat.status === 'PENDING' && <PendingAssistantMessage />}
          {(chat.status === 'DISCONNECTED' || chat.status === 'ERROR') && <ErrorMessage />}
        </Flex>
      </div>

      {/* Fixed Input Area */}
      <div className="flex-shrink-0 border-t border-gray-200 bg-white">
        <form className="w-full px-6 py-4" onSubmit={form.onSubmit(handleSubmit)}>
          <Flex w="100%" gap="xs" justify="space-between" align="flex-end">
            <Textarea
              {...form.getInputProps('message')}
              required
              className="flex-1"
              placeholder={placeholder}
              autoComplete="off"
              autosize
              minRows={1}
              maxRows={4}
              onKeyDown={handleKeyDown}
              disabled={chat.status === 'DISCONNECTED'}
              styles={{
                input: {
                  '&:focus': {
                    borderColor: `${primaryColor} !important`,
                    boxShadow: `0 0 0 0.5px ${primaryColor} !important`,
                    outline: `none !important`,
                  },
                  '&:focus-visible': {
                    borderColor: `${primaryColor} !important`,
                    boxShadow: `0 0 0 0.5px ${primaryColor} !important`,
                    outline: `none !important`,
                  },
                  '&:focus-within': {
                    borderColor: `${primaryColor} !important`,
                    boxShadow: `0 0 0 0.5px ${primaryColor} !important`,
                    outline: `none !important`,
                  },
                },
                wrapper: {
                  '&:focus-within': {
                    '& .mantine-Input-input': {
                      borderColor: `${primaryColor} !important`,
                      boxShadow: `0 0 0 0.5px ${primaryColor} !important`,
                      outline: `none !important`,
                    },
                  },
                  '&:focus': {
                    '& .mantine-Input-input': {
                      borderColor: `${primaryColor} !important`,
                      boxShadow: `0 0 0 0.5px ${primaryColor} !important`,
                      outline: `none !important`,
                    },
                  },
                },
              }}
            />
            <Button
              type="submit"
              disabled={chat.status !== 'IDLE' || !form.values.message}
              variant="filled"
              styles={{
                root: {
                  backgroundColor: !form.values.message ? '#e5e7eb' : primaryColor,
                  color: !form.values.message ? '#9ca3af' : '#fff',
                  '&:hover:not(:disabled)': {
                    backgroundColor: primaryColor,
                    opacity: 0.9,
                  },
                  '&:disabled': {
                    backgroundColor: '#e5e7eb !important',
                    color: '#9ca3af !important',
                    opacity: '1 !important',
                  },
                },
              }}
            >
              Send
            </Button>
          </Flex>
        </form>
      </div>
    </div>
  );
}
