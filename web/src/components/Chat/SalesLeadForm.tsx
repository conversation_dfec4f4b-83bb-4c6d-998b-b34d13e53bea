import { useState } from 'react';
import { Button, Flex, TextInput } from '@mantine/core';
import { useForm } from '@mantine/form';

type SalesLeadFormProps = {
  primaryColor?: string;
  onSubmit: (formData: Record<string, string>) => void;
  didResolve?: boolean;
};

export default function SalesLeadForm({
  primaryColor = '#0ea5e9',
  onSubmit,
  didResolve,
}: SalesLeadFormProps) {
  const [didSubmit, setDidSubmit] = useState(false);

  const form = useForm({
    initialValues: {
      name: '',
      email: '',
      company: '',
      phone: '',
    },
    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : 'Invalid email'),
      name: (value) => (value.trim() ? null : 'Name is required'),
    },
  });

  const handleSubmit = async (_values: typeof form.values) => {
    await onSubmit(_values);
    setDidSubmit(true);
  };

  return (
    <form onSubmit={form.onSubmit(handleSubmit)} className="w-full" inert={didSubmit}>
      <Flex
        direction="column"
        gap="md"
        className="p-4 bg-gray-50 rounded-lg border border-gray-200"
      >
        <TextInput
          label="Name"
          placeholder="Your name"
          required
          disabled={didSubmit}
          {...form.getInputProps('name')}
          styles={{
            input: {
              '&:focus': {
                borderColor: `${primaryColor} !important`,
                boxShadow: `0 0 0 0.5px ${primaryColor} !important`,
                outline: `none !important`,
              },
            },
          }}
        />
        <TextInput
          label="Email"
          type="email"
          placeholder="<EMAIL>"
          required
          disabled={didSubmit}
          {...form.getInputProps('email')}
          styles={{
            input: {
              '&:focus': {
                borderColor: `${primaryColor} !important`,
                boxShadow: `0 0 0 0.5px ${primaryColor} !important`,
                outline: `none !important`,
              },
            },
          }}
        />
        <TextInput
          label="Company"
          placeholder="Your company name"
          disabled={didSubmit}
          {...form.getInputProps('company')}
          styles={{
            input: {
              '&:focus': {
                borderColor: `${primaryColor} !important`,
                boxShadow: `0 0 0 0.5px ${primaryColor} !important`,
                outline: `none !important`,
              },
            },
          }}
        />
        <TextInput
          label="Phone"
          type="tel"
          placeholder="Your phone number"
          disabled={didSubmit}
          {...form.getInputProps('phone')}
          styles={{
            input: {
              '&:focus': {
                borderColor: `${primaryColor} !important`,
                boxShadow: `0 0 0 0.5px ${primaryColor} !important`,
                outline: `none !important`,
              },
            },
          }}
        />
        <Button
          type="submit"
          variant="filled"
          hidden={didResolve}
          styles={{
            root: {
              backgroundColor: primaryColor,
              color: '#fff',
            },
          }}
          loading={didSubmit && !didResolve}
        >
          Submit
        </Button>
      </Flex>
    </form>
  );
}
