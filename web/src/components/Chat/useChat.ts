import { useEffect, useState } from 'react';
import { Chat, ChatStatus, Message } from './types';

type ChatState = {
  messages: Message[];
  status: ChatStatus;
};

export default function useChat(websocketUrl?: string): Chat {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [state, setState] = useState<ChatState>({ messages: [], status: 'IDLE' });

  useEffect(() => {
    if (!websocketUrl) {
      setSocket(null);
      setState({ messages: [], status: 'IDLE' });
      return;
    }

    // Reset state when URL changes
    setState({ messages: [], status: 'IDLE' });
    setSocket(null);

    const ws = new WebSocket(websocketUrl);

    ws.onopen = () => {
      setSocket(ws);
      setState((prev) => ({ ...prev, status: 'IDLE' }));
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        setState((prev) => {
          const lastMessage = prev.messages[prev.messages.length - 1];
          const isNewMessage = !lastMessage?.isStreaming;

          if (isNewMessage) {
            return {
              status: 'STREAMING',
              messages: [
                ...prev.messages,
                {
                  role: 'assistant',
                  text: data.text,
                  isStreaming: true,
                },
              ],
            };
          }

          const isStopSequenceMessage = data.text === 'END';
          if (isStopSequenceMessage) {
            return {
              status: 'IDLE',
              messages: [
                ...prev.messages.slice(0, -1),
                {
                  ...lastMessage,
                  isStreaming: false,
                },
              ],
            };
          }

          if (data.overwrite) {
            return {
              status: 'STREAMING',
              messages: [
                ...prev.messages.slice(0, -1),
                {
                  role: 'assistant',
                  text: data.text,
                  isStreaming: true,
                },
              ],
            };
          }

          // default case is message streaming
          return {
            status: 'STREAMING',
            messages: [
              ...prev.messages.slice(0, -1),
              {
                role: 'assistant',
                text: `${lastMessage.text}${data.text}`,
                isStreaming: true,
              },
            ],
          };
        });
      } catch (error) {
        // Silently handle parsing errors
      }
    };

    ws.onerror = (_error) => {
      setState((prev) => ({ ...prev, status: 'ERROR' }));
    };

    ws.onclose = (_event) => {
      setSocket(null);
      setState((prev) => {
        if (prev.status === 'ERROR') return prev;
        return { ...prev, status: 'DISCONNECTED' };
      });
    };

    // Cleanup on unmount
    return () => {
      if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
        ws.close();
      }
    };
  }, [websocketUrl]);

  const handleSubmit = (text: string) => {
    if (!socket) return;
    if (socket.readyState !== 1) return; // WebSocket.OPEN = 1

    const messages = [
      {
        role: 'user' as const,
        text,
      },
    ];

    setState({
      status: 'PENDING',
      messages: [...state.messages, ...messages],
    });

    // Send message through WebSocket
    socket.send(
      JSON.stringify({
        messages,
      })
    );
  };

  const handleFormSubmit = (formData: Record<string, string>) => {
    if (!socket) return;
    if (socket.readyState !== 1) return; // WebSocket.OPEN = 1

    setState((prev) => ({
      ...prev,
      status: 'PENDING',
    }));

    socket.send(
      JSON.stringify({
        messages: [
          {
            role: 'system' as const,
            data: formData,
          },
        ],
      })
    );
  };

  // Keep socket alive
  // TODO - consider switching to socket.io and removing this
  useEffect(() => {
    const interval = setInterval(() => {
      if (socket?.readyState !== 1) return; // WebSocket.OPEN = 1
      socket.send(JSON.stringify({ type: 'ping' }));
    }, 30000);
    return () => clearInterval(interval);
  }, [socket]);

  // Cleanup
  useEffect(() => {
    return () => {
      setState({
        status: 'IDLE',
        messages: [],
      });
    };
  }, [websocketUrl]);

  return {
    ...state,
    handleSubmit,
    handleFormSubmit,
  };
}
