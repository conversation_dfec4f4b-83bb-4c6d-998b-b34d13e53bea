import React, { useCallback, useState } from 'react';
import { Check, Trash2, Upload, X } from 'lucide-react';
import { Badge, Button, Checkbox, Group, Progress, Stack, Text } from '@mantine/core';
import { fileService, PresignedUrlRequest } from '../../api/files/fileService';

export interface FileUploadProps {
  onUploadComplete?: (fileKeys: string[]) => void;
  onUploadError?: (error: string) => void;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  className?: string;
  maxFiles?: number; // maximum number of files
}

interface FileItem {
  id: string;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  fileKey?: string;
  isPublic: boolean;
}

interface UploadState {
  files: FileItem[];
  isUploading: boolean;
  overallProgress: number;
}

export function FileUpload({
  onUploadComplete,
  onUploadError,
  maxFileSize = 10, // 10MB default
  acceptedTypes = ['image/*', 'application/pdf', 'text/*'],
  className,
  maxFiles = 10,
}: FileUploadProps) {
  const [uploadState, setUploadState] = useState<UploadState>({
    files: [],
    isUploading: false,
    overallProgress: 0,
  });

  const validateFile = useCallback(
    (file: File): string | null => {
      // Validate file size
      if (file.size > maxFileSize * 1024 * 1024) {
        return `File size must be less than ${maxFileSize}MB`;
      }

      // Validate file type
      const isValidType = acceptedTypes.some((type) => {
        if (type.endsWith('/*')) {
          return file.type.startsWith(type.slice(0, -1));
        }
        return file.type === type;
      });

      if (!isValidType) {
        return `File type ${file.type} is not supported`;
      }

      return null;
    },
    [maxFileSize, acceptedTypes]
  );

  const handleFileSelect = useCallback(
    (selectedFiles: FileList | null) => {
      if (!selectedFiles) return;

      const newFiles: FileItem[] = [];
      const errors: string[] = [];

      // Check if adding these files would exceed maxFiles limit
      if (uploadState.files.length + selectedFiles.length > maxFiles) {
        onUploadError?.(`Maximum ${maxFiles} files allowed`);
        return;
      }

      Array.from(selectedFiles).forEach((file) => {
        const error = validateFile(file);
        if (error) {
          errors.push(`${file.name}: ${error}`);
        } else {
          newFiles.push({
            id: `${Date.now()}-${Math.random()}`,
            file,
            status: 'pending',
            progress: 0,
            isPublic: false, // Default to private
          });
        }
      });

      if (errors.length > 0) {
        onUploadError?.(errors.join(', '));
      }

      if (newFiles.length > 0) {
        setUploadState((prev) => ({
          ...prev,
          files: [...prev.files, ...newFiles],
        }));
      }
    },
    [uploadState.files.length, maxFiles, validateFile, onUploadError]
  );

  const removeFile = useCallback((fileId: string) => {
    setUploadState((prev) => ({
      ...prev,
      files: prev.files.filter((f) => f.id !== fileId),
    }));
  }, []);

  const toggleFilePublic = useCallback((fileId: string) => {
    setUploadState((prev) => ({
      ...prev,
      files: prev.files.map((f) => (f.id === fileId ? { ...f, isPublic: !f.isPublic } : f)),
    }));
  }, []);

  const uploadFile = useCallback(
    async (fileItem: FileItem): Promise<void> => {
      const { file } = fileItem;

      try {
        // Update file status to uploading
        setUploadState((prev) => ({
          ...prev,
          files: prev.files.map((f) =>
            f.id === fileItem.id ? { ...f, status: 'uploading', progress: 0 } : f
          ),
        }));

        const fileName = `${file.name}`;

        // Get presigned URL from backend
        const presignedRequest: PresignedUrlRequest = {
          fileName,
          contentType: file.type,
          expiresIn: 3600, // 1 hour
          public: fileItem.isPublic,
        };

        const presignedResponse = await fileService.getPresignedUrl(presignedRequest);

        // Upload file to S3 using presigned URL
        await fileService.uploadFileToS3(file, presignedResponse.url);

        // Update file status to success
        setUploadState((prev) => ({
          ...prev,
          files: prev.files.map((f) =>
            f.id === fileItem.id
              ? { ...f, status: 'success', progress: 100, fileKey: presignedResponse.key }
              : f
          ),
        }));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed';

        // Update file status to error
        setUploadState((prev) => ({
          ...prev,
          files: prev.files.map((f) =>
            f.id === fileItem.id ? { ...f, status: 'error', error: errorMessage } : f
          ),
        }));

        onUploadError?.(`${file.name}: ${errorMessage}`);
      }
    },
    [onUploadError]
  );

  const uploadAllFiles = useCallback(async () => {
    const pendingFiles = uploadState.files.filter((f) => f.status === 'pending');

    if (pendingFiles.length === 0) return;

    setUploadState((prev) => ({ ...prev, isUploading: true }));

    try {
      // Upload files sequentially to avoid overwhelming the server
      for (const fileItem of pendingFiles) {
        await uploadFile(fileItem);

        // Update overall progress
        const completedFiles = uploadState.files.filter((f) => f.status === 'success').length + 1;
        const totalFiles = uploadState.files.length;
        const progress = (completedFiles / totalFiles) * 100;

        setUploadState((prev) => ({ ...prev, overallProgress: progress }));
      }

      // Get all successful file keys
      const successfulFiles = uploadState.files.filter((f) => f.status === 'success');
      const fileKeys = successfulFiles.map((f) => f.fileKey!).filter(Boolean);

      if (fileKeys.length > 0) {
        onUploadComplete?.(fileKeys);
      }
    } finally {
      setUploadState((prev) => ({ ...prev, isUploading: false }));
    }
  }, [uploadState.files, uploadFile, onUploadComplete]);

  const resetUpload = useCallback(() => {
    setUploadState({
      files: [],
      isUploading: false,
      overallProgress: 0,
    });
  }, []);

  const handleFileInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      handleFileSelect(event.target.files);
      // Reset input value to allow selecting the same files again
      event.target.value = '';
    },
    [handleFileSelect]
  );

  const renderFileItem = (fileItem: FileItem) => {
    const getStatusColor = () => {
      switch (fileItem.status) {
        case 'success':
          return 'green';
        case 'error':
          return 'red';
        case 'uploading':
          return 'blue';
        default:
          return 'gray';
      }
    };

    const getStatusIcon = () => {
      switch (fileItem.status) {
        case 'success':
          return <Check size={14} />;
        case 'error':
          return <X size={14} />;
        case 'uploading':
          return <Upload size={14} />;
        default:
          return null;
      }
    };

    return (
      <Group
        key={fileItem.id}
        justify="space-between"
        p="sm"
        style={{ border: '1px solid #e9ecef', borderRadius: '8px' }}
      >
        <Group>
          {getStatusIcon()}
          <Text size="sm">{fileItem.file.name}</Text>
          <Badge color={getStatusColor()} size="sm">
            {fileItem.status}
          </Badge>
          <Text size="xs" c="dimmed">
            {(fileItem.file.size / 1024 / 1024).toFixed(2)} MB
          </Text>
        </Group>

        <Group>
          {fileItem.status === 'pending' && (
            <>
              <Checkbox
                label="Public"
                checked={fileItem.isPublic}
                onChange={() => toggleFilePublic(fileItem.id)}
                size="xs"
              />
              <Button
                size="xs"
                variant="subtle"
                color="red"
                leftSection={<Trash2 size={12} />}
                onClick={() => removeFile(fileItem.id)}
              >
                Remove
              </Button>
            </>
          )}

          {fileItem.status === 'uploading' && (
            <Progress value={fileItem.progress} size="sm" style={{ width: '100px' }} />
          )}

          {fileItem.status === 'success' && (
            <Button
              size="xs"
              variant="subtle"
              color="dark"
              leftSection={<X size={12} />}
              onClick={() => removeFile(fileItem.id)}
            >
              Dismiss
            </Button>
          )}

          {fileItem.status === 'error' && (
            <Text size="xs" c="red">
              {fileItem.error}
            </Text>
          )}
        </Group>
      </Group>
    );
  };

  return (
    <div className={className}>
      <Stack gap="md">
        <Group>
          <Button
            leftSection={<Upload size={16} />}
            disabled={uploadState.isUploading}
            component="label"
            htmlFor="file-upload"
          >
            Choose Files
          </Button>

          <input
            id="file-upload"
            type="file"
            multiple
            accept={acceptedTypes.join(',')}
            onChange={handleFileInputChange}
            style={{ display: 'none' }}
            disabled={uploadState.isUploading}
          />

          {uploadState.files.length > 0 && (
            <Button
              onClick={uploadAllFiles}
              disabled={
                uploadState.isUploading ||
                uploadState.files.filter((f) => f.status === 'pending').length === 0
              }
              loading={uploadState.isUploading}
            >
              {uploadState.isUploading ? 'Uploading...' : 'Upload All Files'}
            </Button>
          )}

          {uploadState.files.length > 0 && (
            <Button variant="subtle" onClick={resetUpload} disabled={uploadState.isUploading}>
              Clear All
            </Button>
          )}
        </Group>

        {uploadState.files.length > 0 && (
          <Stack gap="sm">
            <Text size="sm" fw={500}>
              Selected Files ({uploadState.files.length}/{maxFiles})
            </Text>
            {uploadState.files.map(renderFileItem)}
          </Stack>
        )}

        {uploadState.isUploading && (
          <Stack gap="sm">
            <Text size="sm">Overall Progress</Text>
            <Progress value={uploadState.overallProgress} animated />
          </Stack>
        )}

        <Text size="xs" c="dimmed">
          Max file size: {maxFileSize}MB • Max files: {maxFiles}
        </Text>
      </Stack>
    </div>
  );
}
