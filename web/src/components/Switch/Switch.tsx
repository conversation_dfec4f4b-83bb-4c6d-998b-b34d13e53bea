import { Switch as MantineSwitch, useMantineTheme } from '@mantine/core';

type SwitchProps = {
  label?: string;
  color?: string;
  labelPosition?: 'left' | 'right';
  switchSize?: 'sm' | 'md' | 'lg';
  onChange: (value: boolean) => void;
};

const Switch = ({ label, labelPosition = 'left', switchSize = 'md', onChange }: SwitchProps) => {
  const switchTheme = useMantineTheme().other.switch;
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange(event.currentTarget.checked);
  };

  return (
    <MantineSwitch
      label={label}
      color={switchTheme.backgroundColor}
      labelPosition={labelPosition}
      onChange={handleChange}
      styles={{
        label: {
          fontWeight: 700,
          textSize: '14px',
          leading: '16px',
        },
      }}
      size={switchSize}
    />
  );
};

export default Switch;
