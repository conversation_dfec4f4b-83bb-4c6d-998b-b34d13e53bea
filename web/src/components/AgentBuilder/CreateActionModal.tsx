import { Textarea, TextInput } from '@mantine/core';
import { useForm } from '@mantine/form';
import GenericModal from '../Modal/GenericModal';

type CreateActionModalProps = {
  opened: boolean;
  onCancel: () => void;
  onConfirm: ({ webhookUrl, webhookBody }: { webhookUrl: string; webhookBody: string }) => void;
};

export default function CreateActionModal({ opened, onCancel, onConfirm }: CreateActionModalProps) {
  const form = useForm({
    initialValues: {
      webhookUrl: '',
      webhookBody: '',
    },
  });

  return (
    <GenericModal opened={opened} title="Add Custom Action" onExitTransitionEnd={form.reset}>
      <form className="space-y-4" onSubmit={form.onSubmit(onConfirm)}>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Webhook URL</label>
          <TextInput
            key={form.key('webhookUrl')}
            placeholder="https://api.example.com/webhook"
            {...form.getInputProps('webhookUrl')}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Request Body (JSON)
          </label>
          <Textarea
            key={form.key('webhookBody')}
            {...form.getInputProps('webhookBody')}
            rows={6}
            placeholder='{
  "event": "customer_inquiry",
  "data": {
    "customer_id": "{{customer_id}}",
    "message": "{{message}}"
  }
}'
          />
        </div>

        <div className="flex justify-end gap-3 mt-6">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg cursor-pointer"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 cursor-pointer"
          >
            Add Action
          </button>
        </div>
      </form>
    </GenericModal>
  );
}
