import React, { useCallback, useEffect, useState } from 'react';
import { Plus } from 'lucide-react';
import { notifications } from '@mantine/notifications';
import { agentService } from '../../api/agent/agentService';
import { ActionType, Agent, AgentType } from '../../types/agents';
import { AgentList } from './AgentList';
import ChatModal from './ChatModal';
import { CreateAgentModal } from './CreateAgentModal';
import CreateEventModal from './CreateEventModal';
import DeleteAgentModal from './DeleteAgentModal';
import DeleteEventModal from './DeleteEventModal';
import { EditAgentModal } from './EditAgentModal';

export function AgentBuilder() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDeleteConfirmationModalOpen, setIsDeleteConfirmationModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteEventModalOpen, setIsDeleteEventModalOpen] = useState(false);
  const [isCreateEventModalOpen, setIsCreateEventModalOpen] = useState(false);
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);

  const [selectedAgentId, setSelectedAgentId] = useState<string | undefined>();
  const [selectedEventId, setSelectedEventId] = useState<string | undefined>();

  const selectedAgent = selectedAgentId ? agents.find((a) => a.id === selectedAgentId) : undefined;

  const loadAgents = useCallback(() => {
    agentService.list().then((response) => setAgents(response.data.items));
  }, [setAgents]);

  useEffect(loadAgents, []);

  const handleCreateAgent = useCallback(
    async ({
      type,
      name,
      description,
      phoneNumber,
    }: {
      type: AgentType;
      name: string;
      description: string;
      phoneNumber?: string;
    }) => {
      await agentService.create({ type, name, description, phoneNumber });
      loadAgents();
      setIsCreateModalOpen(false);
      notifications.show({
        color: 'green',
        title: 'Success',
        message: `Created agent "${name}"`,
      });
      return { success: true };
    },
    [loadAgents]
  );

  const handleEditAgent = useCallback(
    async ({
      id,
      name,
      description,
      phoneNumber,
      events,
    }: {
      id: string;
      name: string;
      description: string;
      phoneNumber?: string;
      events: { name: string; description: string; actions: { type: ActionType }[] }[];
    }) => {
      try {
        await agentService.edit({ id, name, description, phoneNumber, events });
      } catch (error: any) {
        const errorMessage = error?.message ? `HTTP ${error.message}` : 'unknown error';
        notifications.show({
          color: 'red',
          title: 'Error',
          message: `Failed to update agent (${errorMessage})`,
        });
        return;
      }

      loadAgents();
      setIsEditModalOpen(false);
      notifications.show({
        color: 'green',
        title: 'Success',
        message: `Updated agent "${name}"`,
      });
    },
    [loadAgents]
  );

  const handleDeleteAgent = (agentId: string) => {
    setSelectedAgentId(agentId);
    setIsDeleteConfirmationModalOpen(true);
  };

  const handleDeleteAgentConfirmation = async (agentId: string) => {
    await agentService.delete(agentId);

    loadAgents();
    setIsDeleteConfirmationModalOpen(false);
    notifications.show({
      color: 'green',
      title: 'Success',
      message: `Deleted agent "${selectedAgent?.name}"`,
    });
  };

  const handleDeleteEvent = (agentId: string, eventId: string) => {
    setSelectedAgentId(agentId);
    setSelectedEventId(eventId);
    setIsDeleteEventModalOpen(true);
  };

  const handleDeleteEventConfirmation = async (agentId?: string, eventId?: string) => {
    await agentService.deleteEvent(agentId, eventId);
    loadAgents();

    setIsDeleteEventModalOpen(false);
    notifications.show({
      color: 'green',
      title: 'Success',
      message: 'Deleted event',
    });
  };

  const handleCreateEvent = async (agentId: string) => {
    setSelectedAgentId(agentId);
    setIsCreateEventModalOpen(true);
  };

  const handleCreateEventConfirmation = async (
    agentId?: string,
    event?: {
      name: string;
      description: string;
      actions: { type: ActionType; forwardToNumber?: string }[];
    }
  ) => {
    const { name, description, actions } = event ?? {};
    await agentService.createEvent({ agentId, name, description, actions });

    loadAgents();
    setIsCreateEventModalOpen(false);

    notifications.show({
      color: 'green',
      title: 'Success',
      message: `Added event "${name}"`,
    });
  };

  const handleLaunchChat = (agentId: string) => {
    setSelectedAgentId(agentId);
    setIsChatModalOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatModalOpen(false);
    setSelectedAgentId(undefined);
  };

  return (
    <main className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Agent Builder</h1>
          <p className="text-gray-600">Create and manage custom AI support agents</p>
        </div>
        <button
          id="create-new-agent-button"
          type="button"
          onClick={() => setIsCreateModalOpen(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors cursor-pointer"
        >
          <Plus size={20} />
          Create New Agent
        </button>
      </div>

      <AgentList
        agents={agents}
        handleEditAgent={(agentId) => {
          setSelectedAgentId(agentId);
          setIsEditModalOpen(true);
        }}
        handleDeleteAgent={handleDeleteAgent}
        handleDeleteEvent={handleDeleteEvent}
        handleCreateEvent={handleCreateEvent}
        handleLaunchChat={handleLaunchChat}
      />

      <CreateAgentModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreateAgent={handleCreateAgent}
      />

      <EditAgentModal
        isOpen={isEditModalOpen}
        agent={selectedAgent}
        onCancel={() => {
          setSelectedAgentId(undefined);
          setIsEditModalOpen(false);
        }}
        onConfirm={handleEditAgent}
      />

      <DeleteAgentModal
        opened={isDeleteConfirmationModalOpen}
        agent={selectedAgent}
        onConfirm={() => selectedAgentId && handleDeleteAgentConfirmation(selectedAgentId)}
        onCancel={() => setIsDeleteConfirmationModalOpen(false)}
      />

      <DeleteEventModal
        opened={isDeleteEventModalOpen}
        agent={selectedAgent}
        eventId={selectedEventId}
        onConfirm={() => handleDeleteEventConfirmation(selectedAgent?.id, selectedEventId)}
        onCancel={() => setIsDeleteEventModalOpen(false)}
      />

      <CreateEventModal
        opened={isCreateEventModalOpen}
        agentType={selectedAgent?.type}
        onConfirm={(event) => handleCreateEventConfirmation(selectedAgentId, event)}
        onCancel={() => setIsCreateEventModalOpen(false)}
      />

      <ChatModal opened={isChatModalOpen} onClose={handleCloseChat} agent={selectedAgent} />
    </main>
  );
}
