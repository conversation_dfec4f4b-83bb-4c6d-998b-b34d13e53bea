import React from 'react';
import { Mail, MessageSquare, Phone, type LucideProps } from 'lucide-react';
import { AgentType } from '../../types/agents';

interface AgentIconProps extends LucideProps {
  agentType: AgentType;
}

export default function AgentIcon({ agentType, ...props }: AgentIconProps) {
  switch (agentType) {
    case 'EMAIL':
      return <Mail size={20} className="text-blue-500" {...props} />;
    case 'PHONE':
      return <Phone size={20} className="text-green-500" {...props} />;
    case 'CHAT':
      return <MessageSquare size={20} className="text-purple-500" {...props} />;
  }
}
