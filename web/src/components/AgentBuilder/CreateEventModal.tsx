import { IMaskInput } from 'react-imask';
import { Checkbox, Input, InputLabel, TextInput } from '@mantine/core';
import { useForm } from '@mantine/form';
import { ActionType, AgentType } from '../../types/agents';
import GenericModal from '../Modal/GenericModal';

type CreateEventModalProps = {
  agentType?: AgentType;
  opened: boolean;
  onConfirm: ({
    name,
    description,
    actions,
  }: {
    name: string;
    description: string;
    actions: { type: ActionType }[];
  }) => void;
  onCancel: () => void;
};

function getAvailableActions(agentType?: string): ActionType[] {
  switch (agentType) {
    case 'EMAIL':
      return ['FORWARD_EMAIL', 'CREATE_TICKET', 'EDIT_TICKET'];
    case 'PHONE':
      return ['FORWARD_CALL', 'CREATE_TICKET', 'EDIT_TICKET'];
    case 'CHAT':
      return ['FORWARD_TO_HUMAN', 'CREATE_TICKET', 'EDIT_TICKET'];
    default:
      return [];
  }
}

export default function CreateEventModal({
  agentType,
  opened,
  onCancel,
  onConfirm,
}: CreateEventModalProps) {
  const form = useForm({
    initialValues: {
      name: '',
      description: '',
      FORWARD_EMAIL: false,
      CREATE_TICKET: false,
      EDIT_TICKET: false,
      FORWARD_CALL: false,
      FORWARD_TO_HUMAN: false,
      forwardToNumber: undefined as string | undefined,
    },
    transformValues: (values) => ({
      ...values,
      forwardToNumber: values.forwardToNumber?.replace(/[ ()-]/g, ''),
    }),
  });

  const availableActions = getAvailableActions(agentType);

  const handleSubmit = (values: typeof form.values) => {
    const { name, description, forwardToNumber, ...actionFields } = values;

    const actions = Object.entries(actionFields)
      .filter(([_key, value]) => !!value)
      .map(([key]) => key as ActionType)
      .map((actionType) => ({ type: actionType, forwardToNumber }));

    onConfirm({ name, description, actions });
  };

  return (
    <GenericModal
      opened={opened}
      title="Add New Event"
      onClose={onCancel}
      onExitTransitionEnd={form.reset}
    >
      <form onSubmit={form.onSubmit(handleSubmit)} className="space-y-4">
        <div>
          <TextInput
            key={form.key('name')}
            {...form.getInputProps('name')}
            label="Event Name"
            placeholder="Enter event name"
            required
          />
        </div>
        <div>
          <TextInput
            key={form.key('description')}
            {...form.getInputProps('description')}
            label="Description"
            placeholder="Describe when this event should trigger"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Select Actions</label>
          <div className="space-y-2">
            {availableActions.map((action) => (
              <Checkbox
                key={action}
                {...form.getInputProps(action, { type: 'checkbox' })}
                label={action.replaceAll('_', ' ')}
                className="lowercase"
              />
            ))}
          </div>
        </div>
        <div hidden={!form.values.FORWARD_CALL}>
          <InputLabel htmlFor="forward-to">Forward to</InputLabel>
          <Input
            id="forward-to"
            {...form.getInputProps('forwardToNumber')}
            type="tel"
            component={IMaskInput}
            mask="+****************"
            placeholder="+1 (XXX) XXX-XXXX"
            label="Forward to"
            onAccept={(value: string) => form.setFieldValue('forwardToNumber', value)}
          />
        </div>
        <div className="flex justify-end gap-3 mt-6">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg cursor-pointer"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 cursor-pointer"
          >
            Add Event
          </button>
        </div>
      </form>
    </GenericModal>
  );
}
