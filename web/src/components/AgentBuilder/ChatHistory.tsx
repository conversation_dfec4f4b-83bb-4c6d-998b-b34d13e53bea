import React, { useEffect, useState } from 'react';
import { Calendar, ChevronLeft, ChevronRight, ExternalLink, MessageCircle } from 'lucide-react';
import { ChatService, ChatSession } from '@/api/chat/chatService';
import { Agent } from '@/types/agents';
import ChatModal from './ChatModal';

export function ChatHistory() {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalSessions, setTotalSessions] = useState(0);
  const [sessionsPerPage, setSessionsPerPage] = useState(5);

  useEffect(() => {
    const loadChatSessions = async () => {
      try {
        setLoading(true);
        const sessions = await ChatService.getConversationSessions(undefined, 1000); // Get all sessions for pagination
        setChatSessions(sessions);
        setTotalSessions(sessions.length);
      } catch (err) {
        setError('Failed to load chat sessions');
      } finally {
        setLoading(false);
      }
    };

    loadChatSessions();
  }, []);

  // Reset to first page when page size changes
  useEffect(() => {
    setCurrentPage(1);
  }, [sessionsPerPage]);

  const handleOpenChat = (session: ChatSession) => {
    setSelectedSession(session);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedSession(null);
  };

  const truncateMessage = (message: string, maxLength: number = 80) => {
    if (message.length <= maxLength) return message;
    return `${message.substring(0, maxLength)}...`;
  };

  // Calculate pagination
  const totalPages = Math.ceil(totalSessions / sessionsPerPage);
  const startIndex = (currentPage - 1) * sessionsPerPage;
  const endIndex = startIndex + sessionsPerPage;
  const currentSessions = chatSessions.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSessionsPerPage(Number(event.target.value));
  };

  // Create agent data for the modal
  const agent: Agent = {
    id: selectedSession?.agentId || '',
    name: selectedSession?.agentName || '',
    description: 'Support agent for customer inquiries',
    // Add other required properties as needed
  } as Agent;

  return (
    <div className="bg-white rounded-lg shadow-sm mt-6">
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold">Chat History</h3>
        <p className="text-sm text-gray-600 mt-1">
          View and manage your conversation history with AI agents
        </p>
      </div>

      <div className="p-6">
        {/* Pagination */}
        {!loading && !error && (
          <div className="flex items-center justify-between mb-4 pb-4 border-b border-gray-200">
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                Showing {startIndex + 1} to {Math.min(endIndex, totalSessions)} of {totalSessions}{' '}
                sessions
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Show:</span>
                <select
                  value={sessionsPerPage}
                  onChange={handlePageSizeChange}
                  className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={5}>5</option>
                  <option value={10}>10</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                <span className="text-sm text-gray-600">per page</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:text-gray-400 disabled:cursor-not-allowed min-w-[80px] justify-center"
              >
                <ChevronLeft size={16} />
                Previous
              </button>

              <div className="flex items-center gap-1 min-w-[200px] justify-center">
                {(() => {
                  // Always show exactly 5 page buttons
                  const pages = [];

                  if (totalPages <= 5) {
                    // Show all pages if 5 or fewer
                    for (let i = 1; i <= totalPages; i++) {
                      pages.push(i);
                    }
                    // Fill remaining slots with empty space
                    for (let i = totalPages; i < 5; i++) {
                      pages.push('');
                    }
                  } else {
                    // Always show exactly 5 buttons: first, last, current ± 1, and ellipsis as needed
                    pages.push(1); // First page

                    if (currentPage <= 3) {
                      // Near the beginning: 1, 2, 3, 4, ...
                      pages.push(2);
                      pages.push(3);
                      pages.push(4);
                      pages.push('...');
                    } else if (currentPage >= totalPages - 2) {
                      // Near the end: ..., n-3, n-2, n-1, n
                      pages.push('...');
                      pages.push(totalPages - 3);
                      pages.push(totalPages - 2);
                      pages.push(totalPages - 1);
                    } else {
                      // In the middle: ..., current-1, current, current+1, ...
                      pages.push('...');
                      pages.push(currentPage - 1);
                      pages.push(currentPage);
                      pages.push(currentPage + 1);
                    }

                    pages.push(totalPages); // Last page
                  }

                  return pages.map((page, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => typeof page === 'number' && handlePageChange(page)}
                      disabled={
                        typeof page !== 'number' || (typeof page === 'string' && page === '')
                      }
                      className={`px-3 py-1 text-sm rounded min-w-[32px] text-center ${
                        typeof page === 'number'
                          ? currentPage === page
                            ? 'bg-blue-500 text-white'
                            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                          : page === '...'
                            ? 'text-gray-400 cursor-default'
                            : 'invisible'
                      }`}
                    >
                      {page}
                    </button>
                  ));
                })()}
              </div>

              <button
                type="button"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:text-gray-400 disabled:cursor-not-allowed min-w-[60px] justify-center"
              >
                Next
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-sm text-gray-500 border-b border-gray-200">
                <th className="pb-3 font-medium">Date</th>
                <th className="pb-3 font-medium">Agent</th>
                <th className="pb-3 font-medium">Last Message</th>
                <th className="pb-3 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentSessions.map((session) => (
                <tr key={session.id} className="text-sm border-y border-gray-200">
                  <td className="py-3">
                    <div className="flex items-center gap-2">
                      <Calendar size={14} className="text-gray-400" />
                      {new Date(session.date).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="py-3 text-gray-600">{session.agentName}</td>
                  <td className="py-3 text-gray-600 max-w-md">
                    <div className="flex items-center gap-2">
                      <MessageCircle size={14} className="text-gray-400 flex-shrink-0" />
                      <span className="truncate">{truncateMessage(session.lastMessage)}</span>
                    </div>
                  </td>

                  <td className="py-3">
                    <button
                      type="button"
                      onClick={() => handleOpenChat(session)}
                      className="flex items-center gap-1 text-blue-500 hover:text-blue-600"
                    >
                      <ExternalLink size={14} />
                      <span>View</span>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {loading && (
          <div className="text-center py-8 text-gray-500">
            <MessageCircle size={32} className="mx-auto mb-2 text-gray-300" />
            <p>Loading chat sessions...</p>
          </div>
        )}

        {error && (
          <div className="text-center py-8 text-red-500">
            <MessageCircle size={32} className="mx-auto mb-2 text-red-300" />
            <p>{error}</p>
          </div>
        )}

        {!loading && !error && chatSessions.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <MessageCircle size={32} className="mx-auto mb-2 text-gray-300" />
            <p>No chat sessions found</p>
          </div>
        )}
      </div>

      {selectedSession && (
        <ChatModal
          opened={isModalOpen}
          onClose={handleCloseModal}
          agent={agent}
          readOnly
          sessionId={selectedSession.id}
        />
      )}
    </div>
  );
}
