/* eslint-disable react/button-has-type */
import React, { useState } from 'react';
import { CheckCircle, Clock, FileText, Trash2, Upload } from 'lucide-react';

interface Document {
  id: string;
  name: string;
  type: string;
  status: 'processing' | 'completed';
  tokens: number;
  uploadedAt: string;
  lastUpdated: string;
}

const initialDocuments: Document[] = [
  {
    id: '1',
    name: 'ViewLift Support FAQ.pdf',
    type: 'FAQ',
    status: 'completed',
    tokens: 15420,
    uploadedAt: '2024-03-15T10:00:00Z',
    lastUpdated: '2024-03-15T10:05:00Z',
  },
  {
    id: '2',
    name: 'Customer Service Handbook 2024.pdf',
    type: 'Handbook',
    status: 'completed',
    tokens: 45230,
    uploadedAt: '2024-03-14T15:30:00Z',
    lastUpdated: '2024-03-14T15:40:00Z',
  },
  {
    id: '3',
    name: 'Streaming Technical Guide.pdf',
    type: 'Technical Documentation',
    status: 'completed',
    tokens: 28150,
    uploadedAt: '2024-03-14T09:20:00Z',
    lastUpdated: '2024-03-14T09:30:00Z',
  },
  {
    id: '4',
    name: 'Sports Streaming Best Practices.pdf',
    type: 'Guidelines',
    status: 'processing',
    tokens: 0,
    uploadedAt: '2024-03-15T11:55:00Z',
    lastUpdated: '2024-03-15T11:55:00Z',
  },
];

export function VectorKnowledge() {
  const [isDragging, setIsDragging] = useState(false);
  const [documents, setDocuments] = useState<Document[]>(initialDocuments);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    // In a real implementation, this would handle file uploads
  };

  const handleDelete = (id: string) => {
    setDocuments(documents.filter((doc) => doc.id !== id));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm mt-6">
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold">Vector Knowledge Base</h3>
        <p className="text-sm text-gray-600 mt-1">
          Upload documents to train your AI agent with domain-specific knowledge
        </p>
      </div>

      <div className="p-6">
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center">
            <Upload
              size={32}
              className={`${isDragging ? 'text-blue-500' : 'text-gray-400'} mb-4`}
            />
            <p className="text-sm text-gray-600 mb-2">
              Drag and drop files here, or{' '}
              <button className="text-blue-500 hover:text-blue-600">browse</button>
            </p>
            <p className="text-xs text-gray-500">
              Supports PDF, DOCX, TXT, and Markdown files up to 10MB
            </p>
          </div>
        </div>

        <div className="mt-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-500 border-b border-gray-200">
                  <th className="pb-3 font-medium">Document</th>
                  <th className="pb-3 font-medium">Type</th>
                  <th className="pb-3 font-medium">Status</th>
                  <th className="pb-3 font-medium">Tokens</th>
                  <th className="pb-3 font-medium">Last Updated</th>
                  <th className="pb-3 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {documents.map((doc) => (
                  <tr key={doc.id} className="text-sm border-y border-gray-200">
                    <td className="py-3">
                      <div className="flex items-center gap-2">
                        <FileText size={16} className="text-gray-400" />
                        {doc.name}
                      </div>
                    </td>
                    <td className="py-3 text-gray-600">{doc.type}</td>
                    <td className="py-3">
                      <div className="flex items-center gap-1">
                        {doc.status === 'completed' ? (
                          <>
                            <CheckCircle size={14} className="text-green-500" />
                            <span className="text-green-600">Completed</span>
                          </>
                        ) : (
                          <>
                            <Clock size={14} className="text-yellow-500" />
                            <span className="text-yellow-600">Processing</span>
                          </>
                        )}
                      </div>
                    </td>
                    <td className="py-3 text-gray-600">{doc.tokens.toLocaleString()}</td>
                    <td className="py-3 text-gray-600">
                      {new Date(doc.lastUpdated).toLocaleDateString()}
                    </td>
                    <td className="py-3">
                      <button
                        onClick={() => handleDelete(doc.id)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <Trash2 size={16} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
