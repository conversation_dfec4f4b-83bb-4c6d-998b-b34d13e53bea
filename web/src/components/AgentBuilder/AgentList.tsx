import React from 'react';
import { Agent } from '../../types/agents';
import AgentListItem from './AgentListItem';

type AgentListProps = {
  agents: Agent[];
  handleEditAgent: (id: string) => void;
  handleDeleteAgent: (id: string) => void;
  handleDeleteEvent: (agentId: string, eventId: string) => void;
  handleCreateEvent: (agentId: string) => void;
  handleLaunchChat: (agentId: string) => void;
};

export function AgentList({
  agents,
  handleEditAgent,
  handleDeleteAgent,
  handleDeleteEvent,
  handleCreateEvent,
  handleLaunchChat,
}: AgentListProps) {
  return (
    <>
      <ul className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {agents.map((agent) => (
          <AgentListItem
            key={agent.id}
            agent={agent}
            onEdit={() => handleEditAgent(agent.id)}
            onDelete={() => handleDeleteAgent(agent.id)}
            onAddEvent={() => handleCreateEvent(agent.id)}
            onDeleteEvent={(eventId: string) => handleDeleteEvent(agent.id, eventId)}
            onLaunchChat={() => handleLaunchChat(agent.id)}
          />
        ))}
      </ul>
    </>
  );
}
