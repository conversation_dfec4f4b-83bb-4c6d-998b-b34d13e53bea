import { useEffect, useState } from 'react';
import { Agent } from '../../types/agents';
import AreYouSureModal from '../Modal/AreYouSureModal/AreYouSureModal';

type DeleteAgentModalProps = {
  opened: boolean;
  agent?: Agent;
  onConfirm: () => void;
  onCancel: () => void;
};

function DeleteAgentModal({ opened, agent, onCancel, onConfirm }: DeleteAgentModalProps) {
  // State here is not redundant - it keeps the agent visible through the exit transition
  const [visibleAgent, setVisibleAgent] = useState<Agent | undefined>();
  useEffect(() => {
    if (agent) setVisibleAgent(agent);
  }, [agent]);

  return (
    <AreYouSureModal
      opened={opened}
      title={`Delete ${visibleAgent?.name}`}
      onExitTransitionEnd={() => setVisibleAgent(agent)}
      onClose={onCancel}
      onConfirm={onConfirm}
    >
      <p>
        Are you sure you want to delete <strong>{visibleAgent?.name}</strong>?
      </p>
      <p>This agent will be permanently deleted</p>
    </AreYouSureModal>
  );
}

export default DeleteAgentModal;
