import React, { useEffect, useState } from 'react';
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Eye,
  EyeOff,
  FileText,
} from 'lucide-react';
import apiClient from '../../api/apiClient';

interface SearchableEntity {
  file_name: string;
  pages: number;
  public: boolean;
  document_reference_count: number;
}

type SortField = 'file_name' | 'pages' | 'public' | 'document_reference_count';
type SortDirection = 'asc' | 'desc';

export function DocList() {
  const [documents, setDocuments] = useState<SearchableEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalDocuments, setTotalDocuments] = useState(0);
  const [documentsPerPage, setDocumentsPerPage] = useState(5);
  const [sortField, setSortField] = useState<SortField>('file_name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          sortField,
          sortDirection,
        });
        const response = await apiClient.get(`/agents/searchable-entities?${params}`);
        setDocuments(response.data);
        setTotalDocuments(response.data.length);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [sortField, sortDirection]);

  // Reset to first page when page size changes
  useEffect(() => {
    setCurrentPage(1);
  }, [documentsPerPage]);

  // Calculate pagination
  const totalPages = Math.ceil(totalDocuments / documentsPerPage);
  const startIndex = (currentPage - 1) * documentsPerPage;
  const endIndex = startIndex + documentsPerPage;
  const currentDocuments = documents.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setDocumentsPerPage(Number(event.target.value));
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field with descending direction (changed from ascending)
      setSortField(field);
      setSortDirection('desc');
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ChevronUp size={14} className="text-gray-300" />;
    }
    return sortDirection === 'asc' ? (
      <ChevronUp size={14} className="text-blue-500" />
    ) : (
      <ChevronDown size={14} className="text-blue-500" />
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Available Documents</h3>
          <p className="text-sm text-gray-600 mt-1">Documents available for agent knowledge base</p>
        </div>
        <div className="p-6">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Available Documents</h3>
          <p className="text-sm text-gray-600 mt-1">Documents available for agent knowledge base</p>
        </div>
        <div className="p-6">
          <div className="text-center text-red-500">
            <p>Error loading documents: {error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-lg font-semibold">Available Documents</h3>
        <p className="text-sm text-gray-600 mt-1">Documents available for agent knowledge base</p>
      </div>

      <div className="p-6">
        {/* Pagination */}
        {!loading && !error && (
          <div className="flex items-center justify-between mb-4 pb-4 border-b border-gray-200">
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                Showing {startIndex + 1} to {Math.min(endIndex, totalDocuments)} of {totalDocuments}{' '}
                documents
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Show:</span>
                <select
                  value={documentsPerPage}
                  onChange={handlePageSizeChange}
                  className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={5}>5</option>
                  <option value={10}>10</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
                <span className="text-sm text-gray-600">per page</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:text-gray-400 disabled:cursor-not-allowed min-w-[80px] justify-center"
              >
                <ChevronLeft size={16} />
                Previous
              </button>

              <div className="flex items-center gap-1 min-w-[200px] justify-center">
                {(() => {
                  // Always show exactly 5 page buttons
                  const pages = [];

                  if (totalPages <= 5) {
                    // Show all pages if 5 or fewer
                    for (let i = 1; i <= totalPages; i++) {
                      pages.push(i);
                    }
                    // Fill remaining slots with empty space
                    for (let i = totalPages; i < 5; i++) {
                      pages.push('');
                    }
                  } else {
                    // Always show exactly 5 buttons: first, last, current ± 1, and ellipsis as needed
                    pages.push(1); // First page

                    if (currentPage <= 3) {
                      // Near the beginning: 1, 2, 3, 4, ...
                      pages.push(2);
                      pages.push(3);
                      pages.push(4);
                      pages.push('...');
                    } else if (currentPage >= totalPages - 2) {
                      // Near the end: ..., n-3, n-2, n-1, n
                      pages.push('...');
                      pages.push(totalPages - 3);
                      pages.push(totalPages - 2);
                      pages.push(totalPages - 1);
                    } else {
                      // In the middle: ..., current-1, current, current+1, ...
                      pages.push('...');
                      pages.push(currentPage - 1);
                      pages.push(currentPage);
                      pages.push(currentPage + 1);
                    }

                    pages.push(totalPages); // Last page
                  }

                  return pages.map((page, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => typeof page === 'number' && handlePageChange(page)}
                      disabled={
                        typeof page !== 'number' || (typeof page === 'string' && page === '')
                      }
                      className={`px-3 py-1 text-sm rounded min-w-[32px] text-center ${
                        typeof page === 'number'
                          ? currentPage === page
                            ? 'bg-blue-500 text-white'
                            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                          : page === '...'
                            ? 'text-gray-400 cursor-default'
                            : 'invisible'
                      }`}
                    >
                      {page}
                    </button>
                  ));
                })()}
              </div>

              <button
                type="button"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:text-gray-400 disabled:cursor-not-allowed min-w-[60px] justify-center"
              >
                Next
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        )}

        {documents.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <FileText size={48} className="mx-auto mb-4 text-gray-300" />
            <p>No documents available</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-sm text-gray-500 border-b border-gray-200">
                  <th className="pb-3 font-medium">
                    <button
                      type="button"
                      onClick={() => handleSort('file_name')}
                      className="flex items-center gap-1 hover:text-gray-700 transition-colors"
                    >
                      Document
                      {getSortIcon('file_name')}
                    </button>
                  </th>
                  <th className="pb-3 font-medium">
                    <button
                      type="button"
                      onClick={() => handleSort('pages')}
                      className="flex items-center gap-1 hover:text-gray-700 transition-colors"
                    >
                      Pages
                      {getSortIcon('pages')}
                    </button>
                  </th>
                  <th className="pb-3 font-medium">
                    <button
                      type="button"
                      onClick={() => handleSort('public')}
                      className="flex items-center gap-1 hover:text-gray-700 transition-colors"
                    >
                      Visibility
                      {getSortIcon('public')}
                    </button>
                  </th>
                  <th className="pb-3 font-medium">
                    <button
                      type="button"
                      onClick={() => handleSort('document_reference_count')}
                      className="flex items-center gap-1 hover:text-gray-700 transition-colors"
                    >
                      References
                      {getSortIcon('document_reference_count')}
                    </button>
                  </th>
                </tr>
              </thead>
              <tbody>
                {currentDocuments.map((doc) => (
                  <tr key={doc.file_name} className="text-sm border-y border-gray-200">
                    <td className="py-3">
                      <div className="flex items-center gap-2">
                        <FileText size={16} className="text-gray-400" />
                        {doc.file_name}
                      </div>
                    </td>
                    <td className="py-3 text-gray-600">{doc.pages}</td>
                    <td className="py-3">
                      <div className="flex items-center gap-1">
                        {doc.public ? (
                          <>
                            <Eye size={14} className="text-gray-500" />
                            <span className="text-gray-600">Public</span>
                          </>
                        ) : (
                          <>
                            <EyeOff size={14} className="text-gray-500" />
                            <span className="text-gray-600">Private</span>
                          </>
                        )}
                      </div>
                    </td>
                    <td className="py-3 text-gray-600">{doc.document_reference_count}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
