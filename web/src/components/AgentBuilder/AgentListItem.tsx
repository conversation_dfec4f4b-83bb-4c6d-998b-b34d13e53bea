import * as libphonenumber from 'libphonenumber-js';
import { Edit, Mail, Plus, Trash2 } from 'lucide-react';
import { Agent } from '../../types/agents';
import AgentIcon from './AgentIcon';

interface AgentListItemProps extends React.ComponentProps<typeof Mail> {
  agent: Agent;
  onEdit: () => void;
  onDelete: () => void;
  onAddEvent: () => void;
  onDeleteEvent: (eventId: string) => void;
  onLaunchChat: () => void;
}

export default function AgentListItem({
  agent,
  onEdit,
  onDelete,
  onDeleteEvent,
  onAddEvent,
  onLaunchChat,
}: AgentListItemProps) {
  return (
    <section
      className="bg-white rounded-lg shadow-sm p-6 flex flex-col"
      aria-labelledby={`card-title-${agent.id}`}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gray-100 rounded-lg">
            <AgentIcon agentType={agent.type} />
          </div>
          <div>
            <h3 id={`card-title-${agent.id}`} className="font-medium text-gray-900">
              {agent.name}
            </h3>
            <p className="text-sm text-gray-500 capitalize">{agent.type.toLowerCase()} Agent</p>
            <p className="text-sm text-gray-500">
              <span hidden={!agent.phoneNumber}>
                {libphonenumber.format(agent.phoneNumber ?? '', 'NA', 'INTERNATIONAL')}
              </span>
              <button
                type="button"
                hidden={agent.type !== 'CHAT'}
                className="underline cursor-pointer"
                onClick={onLaunchChat}
              >
                Launch live chat
              </button>
              &nbsp;
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <button
            type="button"
            onClick={onEdit}
            className="p-1 text-gray-400 hover:text-gray-600 cursor-pointer"
            aria-label="edit"
          >
            <Edit size={16} />
          </button>
          <button
            type="button"
            className="p-1 text-gray-400 hover:text-red-600 cursor-pointer"
            aria-label="delete"
            onClick={onDelete}
          >
            <Trash2 size={16} />
          </button>
        </div>
      </div>

      <p className="text-sm text-gray-600 mb-4 h-full line-clamp-4">{agent.description}</p>

      <section className="space-y-3 h-full flex flex-col justify-between">
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Events</h4>
          <div>
            <ul className="space-y-3">
              {agent.events?.slice(0, 2).map((event) => (
                <li key={event.id} className="bg-gray-50 rounded-lg p-3" aria-label={event.name}>
                  <div className="flex items-center justify-between mb-1">
                    <h5 className="text-sm font-medium text-gray-800">{event.name}</h5>
                    <button
                      type="button"
                      onClick={() => onDeleteEvent(event.id)}
                      className="text-gray-400 hover:text-red-500 cursor-pointer"
                      aria-label="Delete event"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{event.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {event.actions.map((action, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs lowercase"
                      >
                        {action.type.replaceAll('_', ' ')}
                        <span hidden={action.type !== 'FORWARD_CALL'}>
                          &nbsp;{action.forwardToNumber}
                        </span>
                      </span>
                    ))}
                  </div>
                </li>
              ))}
            </ul>
            {agent.events?.length > 2 && (
              <p className="text-right">+ {agent.events?.length - 2} more</p>
            )}
          </div>
        </div>
        <button
          type="button"
          onClick={onAddEvent}
          className="w-full flex items-center justify-center gap-2 px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-600 hover:bg-gray-50 cursor-pointer"
        >
          <Plus size={16} />
          Add Event
        </button>
      </section>
    </section>
  );
}
