import React, { useEffect } from 'react';
import { Mail, MessageSquare, Phone } from 'lucide-react';
import { NativeSelect, Radio } from '@mantine/core';
import { useForm } from '@mantine/form';
import usePhoneNumbers from '../../hooks/usePhoneNumbers';
import { AgentType } from '../../types/agents';
import GenericModal from '../Modal/GenericModal';
import classes from './index.module.css';

interface CreateAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateAgent: ({
    type,
    name,
    description,
    phoneNumber,
  }: {
    type: AgentType;
    name: string;
    description: string;
    phoneNumber?: string;
  }) => Promise<{ success: boolean }>;
}

export function CreateAgentModal({ isOpen, onClose, onCreateAgent }: CreateAgentModalProps) {
  const form = useForm({
    mode: 'controlled',
    initialValues: {
      type: null,
      name: '',
      description: '',
      phoneNumber: '',
    },
    validate: {
      type: (type) => (type ? null : 'Please select an agent type'),
    },
    transformValues: ({ phoneNumber, ...values }) => ({
      ...values,
      phoneNumber: phoneNumber || undefined,
    }),
  });

  const {
    phoneNumbers,
    firstAvailablePhoneNumber,
    isLoading: isPhoneNumberlistLoading,
  } = usePhoneNumbers(!isOpen);
  useEffect(() => {
    form.setFieldValue('phoneNumber', firstAvailablePhoneNumber ?? '');
  }, [firstAvailablePhoneNumber]);

  const handleSubmit = async (values: ReturnType<typeof form.getTransformedValues>) => {
    const { type, name, description, phoneNumber } = values;
    const result = await onCreateAgent({
      type: type as unknown as AgentType,
      name,
      description,
      phoneNumber,
    });

    if (!result.success) return;

    // Delay by 100ms to give time for the modal to animate out
    setTimeout(form.reset, 100);
  };

  const handleCancel = () => {
    onClose();
    // Delay by 100ms to give time for the modal to animate out
    setTimeout(form.reset, 100);
  };

  return (
    <GenericModal opened={isOpen} onClose={handleCancel} title="Create New Agent">
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <div className="">
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Agent Type
            </label>
            <Radio.Group key={form.key('type')} {...form.getInputProps('type')} required>
              <div className="grid grid-cols-3 gap-4">
                <AgentTypeCard value="EMAIL">
                  <Mail size={20} />
                  <span>Email Agent</span>
                </AgentTypeCard>
                <AgentTypeCard value="PHONE">
                  <Phone size={20} />
                  <span>Phone Agent</span>
                </AgentTypeCard>
                <AgentTypeCard value="CHAT">
                  <MessageSquare size={20} />
                  <span>Chat Agent</span>
                </AgentTypeCard>
              </div>
            </Radio.Group>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Agent Name</label>
              <input
                key={form.key('name')}
                {...form.getInputProps('name')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter agent name"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                aria-label="Description"
                key={form.key('description')}
                {...form.getInputProps('description')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Describe the agent's purpose"
              />
            </div>
            <div hidden={form.values.type !== 'PHONE'}>
              <label
                id="phone-number-input-label"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Phone Number
              </label>
              <NativeSelect
                aria-labelledby="phone-number-input-label"
                data={phoneNumbers}
                {...form.getInputProps('phoneNumber')}
                key={form.key('phoneNumber')}
                disabled={isPhoneNumberlistLoading}
              />
            </div>
          </div>
        </div>

        <div className="pt-4 flex justify-end gap-3">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg cursor-pointer"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 cursor-pointer"
          >
            Create Agent
          </button>
        </div>
      </form>
    </GenericModal>
  );
}

interface AgentTypeCardProps extends React.PropsWithChildren {
  value: 'EMAIL' | 'PHONE' | 'CHAT';
}

const AgentTypeCard = ({ value, children }: AgentTypeCardProps) => (
  <Radio.Card value={value} className={classes.RadioCard} radius="md">
    <div className="p-4 flex flex-col items-center gap-2 text-sm font-medium">{children}</div>
  </Radio.Card>
);
