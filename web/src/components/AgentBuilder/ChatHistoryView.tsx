import { useEffect, useRef, useState } from 'react';
import { Flex, Text } from '@mantine/core';
import { ChatMessage as ChatMessageType, ChatService } from '@/api/chat/chatService';
import { Agent } from '@/types/agents';
import ChatMessage from '../Chat/ChatMessage';
import type { Chat } from '../Chat/types';

interface ChatHistoryViewProps {
  agent: Agent;
  chat: Chat;
  sessionId?: string;
}

export default function ChatHistoryView({ agent, sessionId }: ChatHistoryViewProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    if (sessionId) {
      const loadMessages = async () => {
        try {
          setLoading(true);
          const sessionData = await ChatService.getSessionMessages(sessionId);
          setMessages(sessionData.messages);
        } catch (err) {
          setError('Failed to load chat messages');
        } finally {
          setLoading(false);
        }
      };

      loadMessages();
    }
  }, [sessionId]);

  return (
    <Flex direction="column" w="100%" align="center">
      <div
        ref={scrollAreaRef}
        className="w-full max-h-[480px] overflow-y-scroll overflow-x-hidden mb-4"
      >
        <Flex
          direction="column"
          justify="center"
          align="center"
          w="100%"
          mih="120px"
          gap="xs"
          px={24}
          className="flex-1"
        >
          {loading && (
            <Text px={16} lineClamp={3} c="gray">
              Loading messages...
            </Text>
          )}

          {error && (
            <Text px={16} lineClamp={3} c="red">
              {error}
            </Text>
          )}

          {!loading && !error && messages.length === 0 && (
            <Text px={16} lineClamp={3} c="gray">
              {agent.description}
            </Text>
          )}

          {messages.map((message) => (
            <ChatMessage
              key={message.id}
              message={{
                role: message.role,
                text: message.text,
              }}
            />
          ))}
        </Flex>
      </div>
    </Flex>
  );
}
