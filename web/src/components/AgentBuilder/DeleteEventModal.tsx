import { useEffect, useState } from 'react';
import { Agent } from '../../types/agents';
import AreYouSureModal from '../Modal/AreYouSureModal/AreYouSureModal';

type DeleteEventModalProps = {
  opened: boolean;
  agent?: Agent;
  eventId?: string;
  onConfirm: () => void;
  onCancel: () => void;
};

function DeleteEventModal({ opened, agent, eventId, onCancel, onConfirm }: DeleteEventModalProps) {
  // State here is not redundant - it keeps the event name visible through the exit transition
  const [visibleAgent, setVisibleAgent] = useState<Agent | undefined>();

  const event = visibleAgent?.events.find(({ id }) => id === eventId);
  useEffect(() => {
    if (agent) setVisibleAgent(agent);
  }, [agent]);

  return (
    <AreYouSureModal
      opened={opened}
      title={`Delete ${event?.name}`}
      onExitTransitionEnd={() => setVisibleAgent(agent)}
      onClose={onCancel}
      onConfirm={onConfirm}
    >
      <p>
        Are you sure you want to delete <strong>"{event?.name}"</strong>?
      </p>
      <p>This event will be permanently deleted</p>
    </AreYouSureModal>
  );
}

export default DeleteEventModal;
