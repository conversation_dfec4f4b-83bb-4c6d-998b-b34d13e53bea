import { <PERSON><PERSON> } from '@mantine/core';
import { getWebsocketUrl } from '@/util/chat';
import { Agent } from '../../types/agents';
import Chat from '../Chat/Chat';
import useChat from '../Chat/useChat';
import ChatHistoryView from './ChatHistoryView';

type ChatModalProps = {
  opened: boolean;
  onClose: () => void;
  agent?: Agent;
  readOnly?: boolean;
  sessionId?: string;
};

export default function ChatModal({
  opened,
  onClose,
  agent,
  readOnly = false,
  sessionId,
}: ChatModalProps) {
  const chat = useChat(readOnly ? undefined : getWebsocketUrl(agent));

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={agent?.name || 'Chat'}
      styles={{
        title: {
          fontWeight: 700,
          fontSize: '16px',
          lineHeight: '18px',
        },
        header: {
          borderBottom: '1px solid var(--color-gray-200)',
          marginBottom: '1rem',
        },
        body: {
          display: 'flex',
        },
      }}
      size="xl"
      closeButtonProps={{
        'aria-label': 'Close',
      }}
    >
      {agent &&
        (readOnly ? (
          <ChatHistoryView agent={agent} chat={chat} sessionId={sessionId} />
        ) : (
          <Chat agent={agent} chat={chat} />
        ))}
    </Modal>
  );
}
