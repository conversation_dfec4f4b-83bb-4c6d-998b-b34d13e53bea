import React, { useEffect, useState } from 'react';
import { Plus, Trash2 } from 'lucide-react';
import { NativeSelect, UnstyledButton } from '@mantine/core';
import { useForm } from '@mantine/form';
import usePhoneNumbers from '../../hooks/usePhoneNumbers';
import { ActionType, type Agent } from '../../types/agents';
import GenericModal from '../Modal/GenericModal';
import CreateActionModal from './CreateActionModal';
import CreateEventModal from './CreateEventModal';

interface Event {
  id: string;
  name: string;
  description: string;
  actions: { type: ActionType; forwardToNumber?: string }[];
}

interface EditAgentModalProps {
  onCancel: () => void;
  onConfirm: ({
    id,
    name,
    description,
    phoneNumber,
    events,
  }: {
    id: string;
    name: string;
    description: string;
    phoneNumber?: string;
    events: { name: string; description: string; actions: { type: ActionType }[] }[];
  }) => void;
  isOpen: boolean;
  agent: Agent | undefined;
}

export function EditAgentModal({ isOpen, onCancel, onConfirm, agent }: EditAgentModalProps) {
  const { phoneNumbers, isLoading: isPhoneNumberlistLoading } = usePhoneNumbers(!isOpen);

  const form = useForm({
    mode: 'controlled',
    initialValues: {
      id: agent?.id || '',
      name: agent?.name || '',
      description: agent?.description || '',
      phoneNumber: agent?.phoneNumber || '',
      events: [] as Event[],
    },
    transformValues: ({ phoneNumber, ...values }) => ({
      ...values,
      phoneNumber: phoneNumber || undefined,
    }),
  });

  const [showNewEventForm, setShowNewEventForm] = useState(false);
  const [isActionModalOpen, setIsActionModalOpen] = useState(false);
  const [selectedEventId, setSelectedEventId] = useState<string>();

  useEffect(() => {
    // agent is only cleared after exit animation
    if (!agent) return;

    form.reset();
    const { id, name, description } = agent;
    form.setValues({ id, name, description });
    agent.events.forEach((event) => form.insertListItem('events', event));
  }, [isOpen, agent]);

  const handleAddEvent = (newEvent: {
    name: string;
    description: string;
    actions: { type: ActionType; forwardToNumber?: string }[];
  }) => {
    const fakeEventId = form.values.events.length + 1;
    form.insertListItem('events', { id: fakeEventId, ...newEvent });
    setShowNewEventForm(false);
  };

  const handleDeleteEvent = (index: number) => {
    form.removeListItem('events', index);
  };

  const handleAddCustomAction = ({ eventId }: { eventId?: string }) => {
    setSelectedEventId(eventId);
    setIsActionModalOpen(true);
  };

  const handleAddCustomActionConfirmation = ({
    eventId,
    webhookUrl,
    webhookBody,
  }: {
    eventId?: string;
    webhookUrl: string;
    webhookBody: string;
  }) => {
    const eventIndex = form.values.events.findIndex(({ id }) => id === eventId);
    const oldEvent = form.values.events[eventIndex];
    const newEvent = {
      ...oldEvent,
      actions: [
        {
          type: 'CUSTOM',
          webhookUrl,
          webhookBody,
        },
        ...oldEvent.actions,
      ],
    };
    form.replaceListItem('events', eventIndex, newEvent);

    setIsActionModalOpen(false);
  };

  useEffect(() => {
    if (isPhoneNumberlistLoading) return;
    form.setFieldValue('phoneNumber', agent?.phoneNumber || '');
  }, [isPhoneNumberlistLoading]);

  return (
    <GenericModal
      opened={isOpen}
      title="Edit Agent"
      onClose={() => {
        setShowNewEventForm(false);
        setIsActionModalOpen(false);
        onCancel();
      }}
      onExitTransitionEnd={form.reset}
    >
      <form onSubmit={form.onSubmit(onConfirm)}>
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Agent Name</label>
            <input
              key={form.key('name')}
              {...form.getInputProps('name')}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
            <textarea
              key={form.key('description')}
              {...form.getInputProps('description')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div hidden={agent?.type !== 'PHONE'}>
            <label
              id="phone-number-input-label-2"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Phone Number
            </label>
            <NativeSelect
              aria-labelledby="phone-number-input-label-2"
              data={phoneNumbers}
              {...form.getInputProps('phoneNumber')}
              key={form.key('phoneNumber')}
              disabled={isPhoneNumberlistLoading}
            />
          </div>

          <section aria-label="Events">
            <div className="flex justify-between items-center mb-4">
              <h3 id="edit-agent-modal-event-list" className="text-lg font-medium">
                Events
              </h3>
              <button
                type="button"
                onClick={() => setShowNewEventForm(true)}
                className="flex items-center gap-2 px-3 py-2 text-sm text-blue-500 hover:bg-blue-50 rounded-lg cursor-pointer"
              >
                <Plus size={16} />
                Add Event
              </button>
            </div>

            <div>
              <ul className="space-y-4">
                {form.values.events.map((event, index) => (
                  <li
                    key={`edit-agent-events-${event.id}`}
                    aria-labelledby={`edit-agent-modal-event-${event.id}`}
                    className="border border-gray-200 rounded-lg p-4"
                  >
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4
                          className="font-medium text-gray-900"
                          id={`edit-agent-modal-event-${event.id}`}
                        >
                          {event.name}
                        </h4>
                        <p className="text-sm text-gray-500">{event.description}</p>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleDeleteEvent(index)}
                        className="text-gray-400 hover:text-red-500 cursor-pointer"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>

                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Actions</h5>
                      <div className="flex flex-wrap gap-2 items-center">
                        {event.actions?.map((action, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs lowercase"
                          >
                            {action.type.replaceAll('_', ' ')}
                          </span>
                        ))}
                        <UnstyledButton
                          onClick={() => handleAddCustomAction({ eventId: event.id })}
                        >
                          <div className="px-2 py-1 border border-gray-300 text-gray-600 rounded-full text-xs hover:bg-gray-50 flex items-center gap-1">
                            <Plus size={12} />
                            Add Action
                          </div>
                        </UnstyledButton>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </section>
        </div>
        <div className="pt-4 flex justify-end gap-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg cursor-pointer"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 cursor-pointer"
          >
            Save Changes
          </button>
        </div>
      </form>

      <CreateEventModal
        opened={showNewEventForm}
        agentType={agent?.type}
        onCancel={() => setShowNewEventForm(false)}
        onConfirm={handleAddEvent}
      />

      <CreateActionModal
        opened={isActionModalOpen}
        onCancel={() => setIsActionModalOpen(false)}
        onConfirm={(values) =>
          handleAddCustomActionConfirmation({ eventId: selectedEventId, ...values })
        }
      />
    </GenericModal>
  );
}
