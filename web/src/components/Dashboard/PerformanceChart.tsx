/* eslint-disable react/self-closing-comp */
import React from 'react';
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  <PERSON>Axis,
  <PERSON>Axis,
} from 'recharts';

interface DataPoint {
  name: string;
  aiHandled: number;
  humanHandled: number;
}

const data: DataPoint[] = [
  { name: 'Mon', aiHandled: 24, humanHandled: 36 },
  { name: '<PERSON><PERSON>', aiHandled: 20, humanHandled: 30 },
  { name: 'Wed', aiHandled: 28, humanHandled: 42 },
  { name: 'Thu', aiHandled: 22, humanHandled: 33 },
  { name: 'Fri', aiHandled: 26, humanHandled: 39 },
  { name: 'Sat', aiHandled: 18, humanHandled: 27 },
  { name: 'Sun', aiHandled: 14, humanHandled: 21 },
];

export function PerformanceChart() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-6">Weekly Performance</h3>
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="aiHandled" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.2} />
                <stop offset="95%" stopColor="#3B82F6" stopOpacity={0} />
              </linearGradient>
              <linearGradient id="humanHandled" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10B981" stopOpacity={0.2} />
                <stop offset="95%" stopColor="#10B981" stopOpacity={0} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Area
              type="monotone"
              dataKey="aiHandled"
              stroke="#3B82F6"
              fillOpacity={1}
              fill="url(#aiHandled)"
              name="AI Handled"
            />
            <Area
              type="monotone"
              dataKey="humanHandled"
              stroke="#10B981"
              fillOpacity={1}
              fill="url(#humanHandled)"
              name="Human Handled"
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
      <div className="flex justify-center gap-6 mt-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
          <span className="text-sm text-gray-600">AI Handled</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
          <span className="text-sm text-gray-600">Human Handled</span>
        </div>
      </div>
    </div>
  );
}
