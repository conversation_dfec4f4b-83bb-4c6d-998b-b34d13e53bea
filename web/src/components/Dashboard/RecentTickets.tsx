import React from 'react';
import { AlertCircle, CheckCircle, Clock } from 'lucide-react';

const tickets = [
  {
    id: 1,
    customer: 'Industrial Motors Ltd',
    subject: 'Connection Instability in Factory Sensors',
    status: 'pending',
    time: '10 min ago',
    priority: 'high',
  },
  {
    id: 2,
    customer: 'SmartFleet Solutions',
    subject: 'SIM Card Activation for New Fleet',
    status: 'open',
    time: '25 min ago',
    priority: 'medium',
  },
  {
    id: 3,
    customer: 'AgriTech Monitoring',
    subject: 'Data Usage Exceeded in Field Sensors',
    status: 'open',
    time: '45 min ago',
    priority: 'medium',
  },
  {
    id: 4,
    customer: 'CityGrid Networks',
    subject: 'International Roaming Setup',
    status: 'resolved',
    time: '1 hour ago',
    priority: 'low',
  },
];

export function RecentTickets() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold">Recent Tickets</h3>
        <a href="/tickets" className="text-blue-500 text-sm hover:underline">
          View all
        </a>
      </div>
      <div className="space-y-4">
        {tickets.map((ticket) => (
          <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-start gap-3">
              <div
                className={`p-2 rounded-lg ${
                  ticket.priority === 'high'
                    ? 'bg-red-100'
                    : ticket.priority === 'medium'
                      ? 'bg-yellow-100'
                      : 'bg-green-100'
                }`}
              >
                {ticket.status === 'pending' ? (
                  <Clock size={20} className="text-yellow-600" />
                ) : ticket.status === 'open' ? (
                  <AlertCircle size={20} className="text-red-600" />
                ) : (
                  <CheckCircle size={20} className="text-green-600" />
                )}
              </div>
              <div>
                <h4 className="font-medium text-gray-900">{ticket.subject}</h4>
                <p className="text-sm text-gray-500">{ticket.customer}</p>
              </div>
            </div>
            <div className="text-sm text-gray-500">{ticket.time}</div>
          </div>
        ))}
      </div>
    </div>
  );
}
