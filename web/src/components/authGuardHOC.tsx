import React from 'react';
import { Navigate } from 'react-router-dom';
import { LoadingOverlay } from '@mantine/core';
import { useAppContext } from '@/context/AppContext';

export function authGuardHOC<P extends object>(Component: React.ComponentType<P>): React.FC<P> {
  return (props: P) => {
    const { userInfo } = useAppContext();

    // Redirect all users to pilot page
    if (userInfo) {
      return <Navigate to="/pilot" replace />;
    }

    return loginGuardHOC(Component)(props);
  };
}

export function pilotPageAuthGuardHOC<P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> {
  // Allow all users
  return loginGuardHOC(Component);
}

function loginGuardHOC<P extends object>(Component: React.ComponentType<P>): React.FC<P> {
  return (props: P) => {
    const { initialized, userInfo } = useAppContext();

    if (!initialized) {
      return <LoadingOverlay visible />;
    }

    if (!userInfo) {
      return <Navigate to="/login" replace />;
    }
    return <Component {...props} />;
  };
}
