import React, { useEffect, useState } from 'react';
import { Area, AreaChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import apiClient from '@/api/apiClient';

interface DailyStat {
  date: string;
  sessions: number;
  messages: number;
  avgMsgs: number;
  resolved: number;
  duration: number;
}

interface Metric {
  label: string;
  value: number | string;
  change: number;
  key: string;
}

interface AnalyticsData {
  dailyStats: DailyStat[];
  metrics: Metric[];
}

export function Analytics() {
  const [selectedMetric, setSelectedMetric] = useState<Metric | null>(null);
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [timeRange, setTimeRange] = useState(28);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await apiClient.get(`/analytics?days=${timeRange}`);
        setData(response.data);
        if (!selectedMetric && response.data.metrics.length > 0) {
          setSelectedMetric(response.data.metrics[0]);
        }
      } catch (err) {
        setError('Failed to fetch analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);

  if (loading || !data || !selectedMetric) {
    return (
      <main className="p-6">
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600" />
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="p-6">
        <div className="flex items-center justify-center h-96 text-red-500">{error}</div>
      </main>
    );
  }

  return (
    <main className="p-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
        <div className="flex items-center gap-3">
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
        </div>
        <div className="flex items-center gap-2">
          <select
            className="border border-gray-200 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-100"
            value={timeRange}
            onChange={(e) => setTimeRange(Number(e.target.value))}
          >
            <option value={28}>Last 28 days</option>
            <option value={7}>Last 7 days</option>
            <option value={1}>Last 24 hours</option>
          </select>
        </div>
      </div>

      {/* Metrics and Chart Unified Container */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        {/* Metrics Bar */}
        <div className="flex flex-col md:flex-row md:space-x-8 space-y-2 md:space-y-0 mb-6">
          {data.metrics.map((m) => (
            <button
              key={m.label}
              className="flex-1 flex flex-col items-start md:items-center md:justify-center focus:outline-none"
              onClick={() => setSelectedMetric(m)}
              type="button"
            >
              <div
                className={`text-xs uppercase tracking-wide mb-1 ${
                  selectedMetric.label === m.label
                    ? 'text-indigo-600 underline font-bold'
                    : 'text-gray-500'
                }`}
              >
                {m.label}
              </div>
              <div className="flex items-end">
                <span className="text-2xl font-bold text-gray-900">{m.value}</span>
              </div>
            </button>
          ))}
        </div>

        {/* Chart */}
        <div className="h-[320px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data.dailyStats} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <defs>
                <linearGradient id="colorMetric" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#6366F1" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#6366F1" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis
                dataKey="date"
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={60}
                dy={10}
              />
              <YAxis tick={{ fontSize: 12 }} width={32} />
              <Tooltip />
              <Area
                type="monotone"
                dataKey={selectedMetric.key}
                stroke="#6366F1"
                fillOpacity={1}
                fill="url(#colorMetric)"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 5 }}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>
    </main>
  );
}
