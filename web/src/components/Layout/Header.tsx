import { Bell, Search } from 'lucide-react';
import { useAppContext } from '@/context/AppContext';
import useLogout from '@/hooks/useLogout';
import UserDropdownMenu from './UserDropdownMenu';

function Header() {
  const { userInfo } = useAppContext();
  const logout = useLogout();

  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6">
      <div className="flex items-center flex-1">
        <div className="relative w-96">
          <input
            type="text"
            placeholder="Search tickets, customers..."
            className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-blue-500"
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={20} />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <button type="button" className="relative p-2 hover:bg-gray-100 rounded-full">
          <Bell size={20} />
          <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full" />
        </button>
        <UserDropdownMenu userInfo={userInfo} handleLogout={logout} />
      </div>
    </header>
  );
}

export default Header;
