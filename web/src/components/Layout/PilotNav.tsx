import { useAppContext } from '@/context/AppContext';
import useLogout from '@/hooks/useLogout';
import UserDropdownMenu from './UserDropdownMenu';

export default function PilotNav() {
  const { userInfo } = useAppContext();
  const logout = useLogout();

  return (
    <nav className="flex justify-center border-b border-gray-200">
      <div className="w-full max-w-screen-lg py-2 px-6 flex justify-end">
        <UserDropdownMenu userInfo={userInfo} handleLogout={logout} />
      </div>
    </nav>
  );
}
