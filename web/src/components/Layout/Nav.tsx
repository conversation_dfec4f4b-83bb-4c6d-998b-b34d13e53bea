import React from 'react';
import { Bo<PERSON>, <PERSON>, Phone, Settings, Ticket } from 'lucide-react';
import { Link } from 'react-router-dom';

interface NavItem {
  name: string;
  icon: React.ReactNode;
  path: string;
}

const navItems: NavItem[] = [
  { name: 'AI Email Agent', icon: <Mail size={20} />, path: '/email' },
  { name: 'AI Phone Agent', icon: <Phone size={20} />, path: '/phone' },
  { name: 'Copilot', icon: <Ticket size={20} />, path: '/tickets' },
  { name: 'Agent Builder', icon: <Bot size={20} />, path: '/agent-builder' },
  { name: 'Settings', icon: <Settings size={20} />, path: '/settings' },
];

const Nav = () => {
  return (
    <div className="h-screen w-64 bg-gray-900 text-white p-4 fixed left-0 top-0">
      <div className="flex items-center gap-2 mb-8 px-2">
        <Mail className="text-blue-400" size={24} />
        <span className="text-xl font-bold">AI Support Hub</span>
      </div>
      <nav>
        {navItems.map((item, index) => (
          <Link
            key={`${index} ${item.path}`}
            to={item.path}
            className="flex items-center gap-3 px-2 py-3 rounded-lg hover:bg-gray-800 transition-colors"
          >
            {item.icon}
            <span>{item.name}</span>
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default Nav;
