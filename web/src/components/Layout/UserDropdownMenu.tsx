import { Button, Menu } from '@mantine/core';

type UserDropdownMenuProps = {
  userInfo?: { email: string } | null;
  handleLogout: (e: React.MouseEvent<HTMLButtonElement>) => void;
};

export default function UserDropdownMenu({ userInfo, handleLogout }: UserDropdownMenuProps) {
  return (
    <Menu withArrow shadow="sm" position="bottom-end" arrowPosition="center">
      <Menu.Target>
        <Button c="dark" variant="subtle">
          {userInfo?.email}
        </Button>
      </Menu.Target>
      <Menu.Dropdown className=" items-end">
        <ul className="flex flex-col gap-2">
          <li className="w-full">
            <Button
              styles={{ inner: { justifyContent: 'flex-end' } }}
              w="100%"
              c="black"
              variant="subtle"
              onClick={handleLogout}
            >
              Logout
            </Button>
          </li>
        </ul>
      </Menu.Dropdown>
    </Menu>
  );
}
