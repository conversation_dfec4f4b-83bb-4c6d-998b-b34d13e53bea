/* eslint-disable react/button-has-type */
import React from 'react';
import { Clock, Shield, UserPlus, Users } from 'lucide-react';

export function TeamSettings() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex items-center gap-2 mb-6">
        <Users className="text-blue-500" size={24} />
        <h3 className="text-lg font-semibold">Team Management</h3>
      </div>

      <div className="space-y-6">
        <div className="border border-gray-200 rounded-lg">
          {[
            {
              name: '<PERSON>',
              role: 'Admin',
              status: 'Active',
              lastActive: '2 minutes ago',
            },
            {
              name: '<PERSON>',
              role: 'Support Agent',
              status: 'Active',
              lastActive: '5 minutes ago',
            },
            {
              name: '<PERSON>',
              role: 'Support Agent',
              status: 'Away',
              lastActive: '1 hour ago',
            },
          ].map((member, index) => (
            <div key={index} className="p-4">
              <div className="flex items-center justify-between border-b border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    <Users size={20} className="text-gray-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{member.name}</h4>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Shield size={14} />
                      {member.role}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Clock size={14} />
                    {member.lastActive}
                  </div>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      member.status === 'Active'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}
                  >
                    {member.status}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <button className="w-full flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50">
          <UserPlus size={16} />
          Add Team Member
        </button>
      </div>
    </div>
  );
}
