import React from 'react';
import { Alert<PERSON>rian<PERSON>, Bell, Mail, MessageSquare } from 'lucide-react';

export function NotificationSettings() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex items-center gap-2 mb-6">
        <Bell className="text-blue-500" size={24} />
        <h3 className="text-lg font-semibold">Notification Preferences</h3>
      </div>

      <div className="space-y-6">
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Email Notifications</h4>
          <div className="space-y-3">
            {[
              { icon: <Mail size={16} />, label: 'Daily Summary Report' },
              { icon: <AlertTriangle size={16} />, label: 'Critical Issues' },
              { icon: <MessageSquare size={16} />, label: 'New Support Tickets' },
            ].map((notification, index) => (
              <label key={index} className="flex items-center">
                <input
                  type="checkbox"
                  defaultChecked
                  className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-3 flex items-center gap-2 text-sm text-gray-700">
                  {notification.icon}
                  {notification.label}
                </span>
              </label>
            ))}
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">Alert Thresholds</h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Response Time Warning (minutes)
              </label>
              <input
                type="number"
                defaultValue={15}
                min={1}
                className="w-full border-gray-300 rounded-lg shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm text-gray-700 mb-1">Ticket Queue Alert</label>
              <input
                type="number"
                defaultValue={50}
                min={1}
                className="w-full border-gray-300 rounded-lg shadow-sm focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
