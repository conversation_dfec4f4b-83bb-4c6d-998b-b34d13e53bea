/* eslint-disable react/button-has-type */
import React from 'react';
import { Database, Link2, RefreshCw, Shield } from 'lucide-react';

export function IntegrationSettings() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex items-center gap-2 mb-6">
        <Link2 className="text-blue-500" size={24} />
        <h3 className="text-lg font-semibold">System Integrations</h3>
      </div>

      <div className="space-y-6">
        <div className="border border-gray-200 rounded-lg">
          {[
            {
              icon: <Database size={20} />,
              name: 'IoT Device Management',
              status: 'Connected',
              lastSync: '5 minutes ago',
            },
            {
              icon: <Shield size={20} />,
              name: 'Security Monitoring',
              status: 'Connected',
              lastSync: '2 minutes ago',
            },
            {
              icon: <RefreshCw size={20} />,
              name: 'Data Analytics Platform',
              status: 'Connected',
              lastSync: '10 minutes ago',
            },
          ].map((integration, index) => (
            <div key={index} className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gray-100 rounded-lg">{integration.icon}</div>
                  <div>
                    <h4 className="font-medium text-gray-900">{integration.name}</h4>
                    <p className="text-sm text-gray-500">Last sync: {integration.lastSync}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {integration.status}
                  </span>
                  <button className="text-sm text-blue-500 hover:text-blue-600">Configure</button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <button className="w-full flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50">
          <Link2 size={16} />
          Add New Integration
        </button>
      </div>
    </div>
  );
}
