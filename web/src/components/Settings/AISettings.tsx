import React from 'react';
import { Bo<PERSON>, Clock, MessageSquare } from 'lucide-react';

export function AISettings() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex items-center gap-2 mb-6">
        <Bot className="text-blue-500" size={24} />
        <h3 className="text-lg font-semibold">AI Configuration</h3>
      </div>

      <div className="space-y-6">
        <div>
          <label className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">AI Response Time Threshold</span>
            <span className="text-xs text-gray-500">Current: 30 seconds</span>
          </label>
          <input
            type="range"
            min="10"
            max="60"
            defaultValue="30"
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>10s</span>
            <span>60s</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Confidence Score Threshold
          </label>
          <select className="w-full border-gray-300 rounded-lg shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="0.7">70% (Recommended)</option>
            <option value="0.8">80% (Strict)</option>
            <option value="0.6">60% (Lenient)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Language Model</label>
          <select className="w-full border-gray-300 rounded-lg shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <option value="gpt-4">GPT-4 (High Accuracy)</option>
            <option value="gpt-3.5">GPT-3.5 (Fast Response)</option>
          </select>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">Enable AI Features</label>
          <div className="space-y-3">
            {[
              { icon: <MessageSquare size={16} />, label: 'Automatic Response Generation' },
              { icon: <Clock size={16} />, label: 'Smart Response Time Prediction' },
              { icon: <Bot size={16} />, label: 'Sentiment Analysis' },
            ].map((feature, index) => (
              <label key={index} className="flex items-center">
                <input
                  type="checkbox"
                  defaultChecked
                  className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-3 flex items-center gap-2 text-sm text-gray-700">
                  {feature.icon}
                  {feature.label}
                </span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
