/* eslint-disable react/button-has-type */
import React, { useState } from 'react';
import { Filter, Search } from 'lucide-react';
import { TicketFilters } from './TicketFilters';
import { TicketList } from './TicketList';
import { TicketStats } from './TicketStats';

export function TicketManager() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');

  return (
    <main className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Copilot</h1>
        <p className="text-gray-600">AI-powered support assistance</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
        <TicketStats />
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row gap-4 justify-between">
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="Search tickets..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-blue-500"
              />
              <Search className="absolute left-3 top-2.5 text-gray-400" size={20} />
            </div>
            <div className="flex gap-4">
              <button
                onClick={() => document.getElementById('filterDrawer')?.classList.toggle('hidden')}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
              >
                <Filter size={20} />
                Filters
              </button>
            </div>
          </div>
        </div>

        <div className="flex">
          <div id="filterDrawer" className="hidden lg:block w-64 p-6 border-r border-gray-200">
            <TicketFilters
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              selectedPriority={selectedPriority}
              setSelectedPriority={setSelectedPriority}
            />
          </div>
          <div className="flex-1">
            <TicketList
              searchQuery={searchQuery}
              statusFilter={selectedStatus}
              priorityFilter={selectedPriority}
            />
          </div>
        </div>
      </div>
    </main>
  );
}
