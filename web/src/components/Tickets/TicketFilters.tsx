import React from 'react';

interface TicketFiltersProps {
  selectedStatus: string;
  setSelectedStatus: (status: string) => void;
  selectedPriority: string;
  setSelectedPriority: (priority: string) => void;
}

export function TicketFilters({
  selectedStatus,
  setSelectedStatus,
  selectedPriority,
  setSelectedPriority,
}: TicketFiltersProps) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Status</h3>
        <div className="space-y-2">
          {['all', 'open', 'pending', 'resolved'].map((status) => (
            <label key={status} className="flex items-center">
              <input
                type="radio"
                name="status"
                value={status}
                checked={selectedStatus === status}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700 capitalize">{status}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-sm font-medium text-gray-900 mb-3">Priority</h3>
        <div className="space-y-2">
          {['all', 'high', 'medium', 'low'].map((priority) => (
            <label key={priority} className="flex items-center">
              <input
                type="radio"
                name="priority"
                value={priority}
                checked={selectedPriority === priority}
                onChange={(e) => setSelectedPriority(e.target.value)}
                className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
              />
              <span className="ml-2 text-sm text-gray-700 capitalize">{priority}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
}
