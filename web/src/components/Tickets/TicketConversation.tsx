/* eslint-disable react/button-has-type */
import React, { useState } from 'react';
import { Bar<PERSON>hart2, Bo<PERSON>, Clock, Edit2, Send, ThumbsDown, ThumbsUp, User } from 'lucide-react';

interface Message {
  id: string;
  sender: 'customer' | 'agent' | 'ai';
  content: string;
  timestamp: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
}

interface ConversationMetrics {
  firstResponseTime: string;
  averageResponseTime: string;
  resolutionTime: string;
  customerSatisfaction: number;
  sentimentScore: number;
}

const mockConversation: Message[] = [
  {
    id: '1',
    sender: 'customer',
    content:
      "I'm trying to watch the LIV Golf tournament but the stream keeps buffering every few minutes. I have a fiber connection and other streaming services work fine. This is really frustrating as I'm missing crucial moments.",
    timestamp: '2024-03-15T10:00:00Z',
    sentiment: 'negative',
  },
  {
    id: '2',
    sender: 'agent',
    content:
      "I understand how frustrating it is to experience buffering during a live tournament. Let's resolve this quickly. Could you please confirm your current video quality settings and the device you're using to watch?",
    timestamp: '2024-03-15T10:05:00Z',
    sentiment: 'neutral',
  },
  {
    id: '3',
    sender: 'customer',
    content:
      "I'm watching on my MacBook Pro using Chrome, and the quality is set to Auto. My internet speed test shows 300Mbps download.",
    timestamp: '2024-03-15T10:07:00Z',
    sentiment: 'neutral',
  },
  {
    id: '4',
    sender: 'agent',
    content:
      "Thank you for those details. I've checked your account and stream analytics. It appears there might be a CDN routing issue affecting your region. I'm going to switch you to an alternate content delivery server, which should resolve the buffering.",
    timestamp: '2024-03-15T10:10:00Z',
    sentiment: 'positive',
  },
  {
    id: '5',
    sender: 'customer',
    content: 'Okay, that sounds good. How long will this take to implement?',
    timestamp: '2024-03-15T10:12:00Z',
    sentiment: 'neutral',
  },
];

const mockMetrics: ConversationMetrics = {
  firstResponseTime: '5 minutes',
  averageResponseTime: '3 minutes',
  resolutionTime: '25 minutes',
  customerSatisfaction: 85,
  sentimentScore: 0.6,
};

const mockRubric = [
  { category: 'Response Speed', score: 9, maxScore: 10 },
  { category: 'Solution Accuracy', score: 8, maxScore: 10 },
  { category: 'Communication Clarity', score: 9, maxScore: 10 },
  { category: 'Technical Knowledge', score: 8, maxScore: 10 },
  { category: 'Customer Empathy', score: 9, maxScore: 10 },
];

export function TicketConversation() {
  const [message, setMessage] = useState('');
  const [aiSuggestion, setAiSuggestion] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [showMetrics, setShowMetrics] = useState(false);

  const handleSendMessage = () => {
    // In a real app, this would send the message and update the conversation
    setMessage('');
    setAiSuggestion('');
    setIsEditing(false);
  };

  const handleGenerateAIResponse = () => {
    // Simulate AI generating a response
    setAiSuggestion(
      "I'll initiate the remote restart procedure for sensors SEN-456, SEN-457, and SEN-458 now. The restart process typically takes about 2-3 minutes. During this time, I'll monitor the sensors' status and confirm once they're back online. Would you like me to proceed with the restart?"
    );
  };

  return (
    <div className="flex h-[calc(100vh-12rem)]">
      <div className="flex-1 flex flex-col border-r border-gray-200">
        <div className="p-4 border-b bg-white border-gray-200">
          <h2 className="text-lg font-semibold">Connection Instability in Factory Sensors</h2>
          <p className="text-sm text-gray-500">Ticket #1234 - Industrial Motors Ltd</p>
        </div>

        <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
          {mockConversation.map((msg) => (
            <div
              key={msg.id}
              className={`flex ${msg.sender === 'customer' ? 'justify-start' : 'justify-end'}`}
            >
              <div
                className={`max-w-[70%] rounded-lg p-4 ${
                  msg.sender === 'customer'
                    ? 'bg-white'
                    : msg.sender === 'agent'
                      ? 'bg-blue-500 text-white'
                      : 'bg-green-500 text-white'
                }`}
              >
                <div className="flex items-center gap-2 mb-2">
                  {msg.sender === 'customer' ? (
                    <User size={16} />
                  ) : msg.sender === 'agent' ? (
                    <User size={16} />
                  ) : (
                    <Bot size={16} />
                  )}
                  <span className="text-sm font-medium">
                    {msg.sender === 'customer'
                      ? 'Customer'
                      : msg.sender === 'agent'
                        ? 'Agent'
                        : 'AI Assistant'}
                  </span>
                </div>
                <p>{msg.content}</p>
                <div className="flex justify-between items-center mt-2 text-xs opacity-70">
                  <span>{new Date(msg.timestamp).toLocaleTimeString()}</span>
                  {msg.sentiment && (
                    <span className="flex items-center gap-1">
                      {msg.sentiment === 'positive' ? (
                        <ThumbsUp size={12} />
                      ) : msg.sentiment === 'negative' ? (
                        <ThumbsDown size={12} />
                      ) : null}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="p-4 bg-white border-t border-gray-200">
          {aiSuggestion && !isEditing && (
            <div className="mb-4 p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Bot size={16} className="text-green-600" />
                  <span className="text-sm font-medium text-green-800">AI Suggested Response</span>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setIsEditing(true)}
                    className="text-sm text-green-600 hover:text-green-700 flex items-center gap-1"
                  >
                    <Edit2 size={14} />
                    Edit
                  </button>
                  <button
                    onClick={() => {
                      setMessage(aiSuggestion);
                      setAiSuggestion('');
                    }}
                    className="text-sm text-green-600 hover:text-green-700 flex items-center gap-1"
                  >
                    <Send size={14} />
                    Use
                  </button>
                </div>
              </div>
              <p className="text-sm text-green-800">{aiSuggestion}</p>
            </div>
          )}

          <div className="flex gap-2">
            <textarea
              value={isEditing ? aiSuggestion : message}
              onChange={(e) =>
                isEditing ? setAiSuggestion(e.target.value) : setMessage(e.target.value)
              }
              placeholder="Type your message..."
              className="flex-1 resize-none rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
              rows={3}
            />
            <div className="flex flex-col gap-2">
              <button
                onClick={handleGenerateAIResponse}
                className="p-2 text-gray-600 hover:text-gray-700 bg-gray-100 rounded-lg"
              >
                <Bot size={20} />
              </button>
              <button
                onClick={() => setShowMetrics(!showMetrics)}
                className="p-2 text-gray-600 hover:text-gray-700 bg-gray-100 rounded-lg"
              >
                <BarChart2 size={20} />
              </button>
              <button
                onClick={handleSendMessage}
                className="p-2 text-white bg-blue-500 hover:bg-blue-600 rounded-lg"
              >
                <Send size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {showMetrics && (
        <div className="w-80 border-l bg-white overflow-y-auto border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h3 className="font-semibold">Conversation Analytics</h3>
          </div>

          <div className="p-4 space-y-6">
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Response Metrics</h4>
              <div className="space-y-3">
                {Object.entries(mockMetrics)
                  .slice(0, 3)
                  .map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2">
                      <Clock size={16} className="text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-600 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className="text-sm font-medium">{value}</p>
                      </div>
                    </div>
                  ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Quality Score</h4>
              <div className="space-y-2">
                {mockRubric.map((item) => (
                  <div key={item.category}>
                    <div className="flex justify-between text-sm mb-1">
                      <span>{item.category}</span>
                      <span>
                        {item.score}/{item.maxScore}
                      </span>
                    </div>
                    <div className="h-2 bg-gray-200 rounded-full">
                      <div
                        className="h-full bg-blue-500 rounded-full"
                        style={{ width: `${(item.score / item.maxScore) * 100}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Sentiment Analysis</h4>
              <div className="flex items-center gap-2">
                <div className="flex-1 h-2 bg-gray-200 rounded-full">
                  <div
                    className="h-full bg-green-500 rounded-full"
                    style={{ width: `${mockMetrics.sentimentScore * 100}%` }}
                  />
                </div>
                <span className="text-sm font-medium">
                  {(mockMetrics.sentimentScore * 100).toFixed(0)}%
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
