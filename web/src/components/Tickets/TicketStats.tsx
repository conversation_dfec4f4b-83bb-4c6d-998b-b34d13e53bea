import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Clock, Zap } from 'lucide-react';
import { MetricsCard } from '../Dashboard/MetricsCard';

export function TicketStats() {
  return (
    <>
      <MetricsCard
        title="Open Tickets"
        value="42"
        change={-12.5}
        changeLabel="vs last week"
        icon={<AlertTriangle size={20} className="text-blue-500" />}
      />
      <MetricsCard
        title="Average Resolution Time"
        value="4.2h"
        change={-8.3}
        changeLabel="vs last week"
        icon={<Clock size={20} className="text-blue-500" />}
      />
      <MetricsCard
        title="Resolution Rate"
        value="92%"
        change={3.8}
        changeLabel="vs last week"
        icon={<CheckCircle size={20} className="text-blue-500" />}
      />
      <MetricsCard
        title="AI Resolution Rate"
        value="40%"
        change={2.8}
        changeLabel="vs last week"
        icon={<Zap size={20} className="text-blue-500" />}
      />
    </>
  );
}
