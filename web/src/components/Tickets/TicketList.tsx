/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react/button-has-type */
import React, { useState } from 'react';
import { AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { TicketConversation } from './TicketConversation';

interface TicketListProps {
  searchQuery: string;
  statusFilter: string;
  priorityFilter: string;
}

const tickets = [
  {
    id: 1,
    subject: 'LIV Golf Stream Buffering Issues',
    customer: '<PERSON>',
    status: 'open',
    priority: 'high',
    category: 'Streaming',
    lastUpdate: '10 minutes ago',
    description:
      'Experiencing constant buffering during LIV Golf tournament live stream. Premium subscriber with fiber internet connection.',
  },
  {
    id: 2,
    subject: 'NHL Subscription Renewal Error',
    customer: '<PERSON>',
    status: 'pending',
    priority: 'medium',
    category: 'Billing',
    lastUpdate: '1 hour ago',
    description:
      'Unable to renew NHL streaming subscription. Payment processed but access not renewed.',
  },
  {
    id: 3,
    subject: 'Multi-device Streaming Issue',
    customer: '<PERSON>',
    status: 'open',
    priority: 'high',
    category: 'Account',
    lastUpdate: '2 hours ago',
    description:
      'Cannot access NHL stream on smart TV while watching on mobile device. Premium account should allow multiple devices.',
  },
  {
    id: 4,
    subject: 'Playback Quality Settings',
    customer: 'Emma Anderson',
    status: 'resolved',
    priority: 'medium',
    category: 'Technical',
    lastUpdate: '1 day ago',
    description:
      'Need help configuring 4K streaming settings for LIV Golf tournament coverage on new Samsung TV.',
  },
];

export function TicketList({ searchQuery, statusFilter, priorityFilter }: TicketListProps) {
  const [selectedTicket, setSelectedTicket] = useState<number | null>(null);

  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearch =
      searchQuery === '' ||
      ticket.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.customer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  if (selectedTicket !== null) {
    return (
      <div>
        <div className="p-4 border-b border-gray-200">
          <button
            onClick={() => setSelectedTicket(null)}
            className="text-sm text-blue-500 hover:text-blue-600"
          >
            ← Back to ticket list
          </button>
        </div>
        <TicketConversation />
      </div>
    );
  }

  return (
    <div>
      {filteredTickets.map((ticket) => (
        <div
          key={ticket.id}
          className="p-6 hover:bg-gray-50 cursor-pointer border-b border-gray-200"
          onClick={() => setSelectedTicket(ticket.id)}
        >
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-start gap-4">
              <div
                className={`p-2 rounded-lg ${
                  ticket.status === 'open'
                    ? 'bg-red-100'
                    : ticket.status === 'pending'
                      ? 'bg-yellow-100'
                      : 'bg-green-100'
                }`}
              >
                {ticket.status === 'open' ? (
                  <AlertCircle size={20} className="text-red-600" />
                ) : ticket.status === 'pending' ? (
                  <Clock size={20} className="text-yellow-600" />
                ) : (
                  <CheckCircle size={20} className="text-green-600" />
                )}
              </div>
              <div>
                <h4 className="font-medium text-gray-900">{ticket.subject}</h4>
                <p className="text-sm text-gray-500">{ticket.customer}</p>
              </div>
            </div>
            <div className="text-right">
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  ticket.priority === 'high'
                    ? 'bg-red-100 text-red-800'
                    : ticket.priority === 'medium'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-green-100 text-green-800'
                }`}
              >
                {ticket.priority}
              </span>
              <p className="text-sm text-gray-500 mt-1">{ticket.lastUpdate}</p>
            </div>
          </div>
          <div className="ml-12">
            <div className="flex gap-4 text-sm text-gray-500 mb-2">
              <span>Category: {ticket.category}</span>
              <span>
                Status: <span className="capitalize">{ticket.status}</span>
              </span>
            </div>
            <p className="text-sm text-gray-700">{ticket.description}</p>
          </div>
        </div>
      ))}
    </div>
  );
}
