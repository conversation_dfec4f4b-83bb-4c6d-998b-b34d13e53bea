/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useState } from 'react';
import { AlertCircle, CheckCircle, Mail, MessageCircle } from 'lucide-react';
import { EmailInteractionModal } from './EmailInteractionModal';

const emails = [
  {
    id: 1,
    customer: '<PERSON>',
    subject: 'NHL Blackout Region Inquiry',
    status: 'resolved',
    time: '5 min ago',
    sentiment: 'positive',
    response:
      'Explained blackout policies and provided alternative viewing options through NHL Network.',
    messages: [
      {
        id: '1a',
        type: 'customer',
        content:
          "Hi, I'm trying to watch tonight's Bruins game but getting a blackout message. I thought my subscription included all games?",
        timestamp: '2024-03-20T10:00:00Z',
        sentiment: 'neutral',
      },
      {
        id: '1b',
        type: 'ai',
        content:
          'Hello David! I understand your concern about the game blackout. Let me explain how NHL streaming rights work in your region. While your subscription includes most games, some local broadcasts are subject to regional blackout policies. However, I can help you understand when the game will be available for replay and suggest alternative viewing options.',
        timestamp: '2024-03-20T10:02:00Z',
        sentiment: 'neutral',
      },
      {
        id: '1c',
        type: 'customer',
        content:
          'I see. When will the replay be available? And are there any other ways to watch it live?',
        timestamp: '2024-03-20T10:05:00Z',
        sentiment: 'neutral',
      },
      {
        id: '1d',
        type: 'ai',
        content:
          'The game replay will be available 48 hours after the broadcast. For live viewing, you can watch through your local sports network provider or at participating sports bars. Additionally, NHL Network offers alternative programming during blackout periods. Would you like me to send you a guide to blackout alternatives?',
        timestamp: '2024-03-20T10:07:00Z',
        sentiment: 'positive',
      },
      {
        id: '1e',
        type: 'customer',
        content: 'Yes, that would be helpful. Thanks for explaining the policy clearly.',
        timestamp: '2024-03-20T10:15:00Z',
        sentiment: 'positive',
      },
    ],
    summary:
      'Addressed NHL game blackout concerns, explained policy, and provided alternative viewing options. Customer satisfied with clear explanation and solutions.',
  },
  {
    id: 2,
    customer: 'Lisa Rodriguez',
    subject: 'LIV Golf Tournament Access',
    status: 'escalated',
    time: '15 min ago',
    sentiment: 'neutral',
    response: 'Investigating premium access activation delay for upcoming tournament.',
    messages: [
      {
        id: '2a',
        type: 'customer',
        content:
          "I upgraded to premium for the upcoming LIV Golf tournament but still can't access the pre-event coverage. Can you help?",
        timestamp: '2024-03-20T09:45:00Z',
        sentiment: 'neutral',
      },
      {
        id: '2b',
        type: 'ai',
        content:
          "Hi Lisa! I apologize for the access issues. I can see your premium upgrade was processed successfully. I'll expedite the activation of your premium features and ensure you have immediate access to all pre-event content. Would you like me to verify your access once completed?",
        timestamp: '2024-03-20T09:47:00Z',
        sentiment: 'neutral',
      },
    ],
    summary:
      'Addressing premium access delay for LIV Golf tournament, expediting feature activation for pre-event coverage.',
  },
  {
    id: 3,
    customer: 'Tom Parker',
    subject: '4K Streaming Setup',
    status: 'resolved',
    time: '30 min ago',
    sentiment: 'positive',
    response: 'Guided through 4K streaming setup for smart TV configuration.',
    messages: [
      {
        id: '3a',
        type: 'customer',
        content:
          "Need help setting up 4K streaming for golf tournaments on my new Samsung TV. The picture quality isn't as sharp as advertised.",
        timestamp: '2024-03-20T09:30:00Z',
        sentiment: 'neutral',
      },
      {
        id: '3b',
        type: 'ai',
        content:
          "I'll help you optimize your 4K streaming settings right away. First, let's verify your TV model and internet speed to ensure you're getting the best possible quality for the tournament coverage.",
        timestamp: '2024-03-20T09:32:00Z',
        sentiment: 'positive',
      },
    ],
    summary:
      'Successfully guided customer through 4K streaming setup process for optimal tournament viewing experience.',
  },
];

export function RecentEmails() {
  const [selectedEmail, setSelectedEmail] = useState<number | null>(null);

  const selectedInteraction = emails.find((email) => email.id === selectedEmail);

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Recent Email Interactions</h3>
        </div>
        <div>
          {emails.map((email) => (
            <div
              key={email.id}
              className="p-6 cursor-pointer hover:bg-gray-50 transition-colors border-b border-b-gray-200"
              onClick={() => setSelectedEmail(email.id)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div
                    className={`p-2 rounded-lg ${
                      email.status === 'resolved' ? 'bg-green-100' : 'bg-yellow-100'
                    }`}
                  >
                    {email.status === 'resolved' ? (
                      <CheckCircle size={20} className="text-green-600" />
                    ) : (
                      <AlertCircle size={20} className="text-yellow-600" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{email.subject}</h4>
                    <p className="text-sm text-gray-500">{email.customer}</p>
                  </div>
                </div>
                <span className="text-sm text-gray-500">{email.time}</span>
              </div>
              <div className="ml-12">
                <div className="flex items-center gap-2 mb-2">
                  <Mail size={16} className="text-blue-500" />
                  <span className="text-sm text-gray-600">AI Response:</span>
                </div>
                <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{email.response}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedInteraction && (
        <EmailInteractionModal
          isOpen={selectedEmail !== null}
          onClose={() => setSelectedEmail(null)}
          // @ts-ignore
          interaction={selectedInteraction}
        />
      )}
    </>
  );
}
