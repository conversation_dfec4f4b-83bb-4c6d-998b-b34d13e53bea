/* eslint-disable react/button-has-type */
import React from 'react';
import { Bot, Mail, Minus, ThumbsDown, ThumbsUp, User, X } from 'lucide-react';

interface EmailMessage {
  id: string;
  type: 'customer' | 'ai';
  content: string;
  timestamp: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
}

interface EmailInteractionModalProps {
  isOpen: boolean;
  onClose: () => void;
  interaction: {
    id: number;
    subject: string;
    customer: string;
    status: string;
    sentiment: string;
    messages: EmailMessage[];
    summary: string;
  };
}

export function EmailInteractionModal({
  isOpen,
  onClose,
  interaction,
}: EmailInteractionModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <div className="p-6 border-b flex justify-between items-start border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{interaction.subject}</h2>
            <p className="text-sm text-gray-500">{interaction.customer}</p>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-500 transition-colors">
            <X size={24} />
          </button>
        </div>

        <div className="p-6 border-b border-gray-200">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Interaction Summary</h3>
          <p className="text-sm text-gray-600">{interaction.summary}</p>
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            {interaction.messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'customer' ? 'justify-start' : 'justify-end'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-4 ${
                    message.type === 'customer' ? 'bg-gray-100' : 'bg-blue-500 text-white'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    {message.type === 'customer' ? (
                      <>
                        <User size={16} />
                        <span className="text-sm font-medium">Customer</span>
                      </>
                    ) : (
                      <>
                        <Bot size={16} />
                        <span className="text-sm font-medium">AI Assistant</span>
                      </>
                    )}
                  </div>
                  <p className="text-sm">{message.content}</p>
                  <div className="flex justify-between items-center mt-2 text-xs opacity-70">
                    <span>{new Date(message.timestamp).toLocaleString()}</span>
                    {message.sentiment && (
                      <span className="flex items-center gap-1">
                        {message.sentiment === 'positive' ? (
                          <ThumbsUp size={12} />
                        ) : message.sentiment === 'negative' ? (
                          <ThumbsDown size={12} />
                        ) : (
                          <Minus size={12} />
                        )}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="p-4 border-t border-gray-200 bg-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Mail size={16} className="text-blue-500" />
              <span className="text-sm text-gray-600">
                Status: <span className="font-medium capitalize">{interaction.status}</span>
              </span>
            </div>
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                interaction.sentiment === 'positive'
                  ? 'bg-green-100 text-green-800'
                  : interaction.sentiment === 'negative'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-800'
              }`}
            >
              {interaction.sentiment} Sentiment
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
