import React from 'react';
import { AlertTriangle, Clock, Mail, ThumbsUp } from 'lucide-react';
import { MetricsCard } from '../Dashboard/MetricsCard';

export function EmailMetrics() {
  return (
    <>
      <MetricsCard
        title="Emails Handled Today"
        value="124"
        change={15.2}
        changeLabel="vs yesterday"
        icon={<Mail size={20} className="text-blue-500" />}
      />
      <MetricsCard
        title="Average Response Time"
        value="4.5m"
        change={-12.3}
        changeLabel="vs last week"
        icon={<Clock size={20} className="text-blue-500" />}
      />
      <MetricsCard
        title="Customer Satisfaction"
        value="85%"
        change={3.8}
        changeLabel="vs last week"
        icon={<ThumbsUp size={20} className="text-blue-500" />}
      />
      <MetricsCard
        title="Escalation Rate"
        value="18%"
        change={-2.5}
        changeLabel="vs last week"
        icon={<AlertTriangle size={20} className="text-blue-500" />}
      />
    </>
  );
}
