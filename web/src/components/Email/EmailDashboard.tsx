import React from 'react';
import { EmailMetrics } from './EmailMetrics';
import { EmailPerformance } from './EmailPerformance';
import { RecentEmails } from './RecentEmails';
import { SentimentAnalysis } from './SentimentAnalysis';

export function EmailDashboard() {
  return (
    <main className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">AI Email Agent</h1>
        <p className="text-gray-600">Monitor and analyze AI-powered email interactions</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <EmailMetrics />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <EmailPerformance />
        </div>
        <div>
          <SentimentAnalysis />
        </div>
      </div>

      <div className="mt-6">
        <RecentEmails />
      </div>
    </main>
  );
}
