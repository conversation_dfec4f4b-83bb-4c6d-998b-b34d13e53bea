import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  Legend,
  Responsive<PERSON>ontainer,
  <PERSON>ltip,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts';

const data = [
  { category: 'Connection Issues', aiResolved: 45, escalated: 12 },
  { category: 'SIM Activation', aiResolved: 38, escalated: 8 },
  { category: 'Data Usage', aiResolved: 32, escalated: 5 },
  { category: 'Roaming Setup', aiResolved: 28, escalated: 10 },
  { category: 'Device Setup', aiResolved: 25, escalated: 7 },
  { category: 'Network Coverage', aiResolved: 22, escalated: 9 },
];

export function EmailPerformance() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-6">Email Resolution by Category</h3>
      <div className="h-[400px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" angle={-45} textAnchor="end" height={100} />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="aiResolved" name="AI Resolved" fill="#3B82F6" />
            <Bar dataKey="escalated" name="Escalated" fill="#EF4444" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
