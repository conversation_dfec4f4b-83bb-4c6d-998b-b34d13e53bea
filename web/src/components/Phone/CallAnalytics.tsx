import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Car<PERSON><PERSON><PERSON><PERSON>,
  Legend,
  Responsive<PERSON><PERSON>r,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts';

const data = [
  { hour: '9AM', automated: 12, transferred: 3 },
  { hour: '10AM', automated: 15, transferred: 2 },
  { hour: '11AM', automated: 18, transferred: 4 },
  { hour: '12PM', automated: 14, transferred: 3 },
  { hour: '1PM', automated: 10, transferred: 2 },
  { hour: '2PM', automated: 16, transferred: 3 },
  { hour: '3PM', automated: 13, transferred: 2 },
  { hour: '4PM', automated: 11, transferred: 1 },
];

export function CallAnalytics() {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-6">Today's Call Volume</h3>
      <div className="h-[400px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="hour" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="automated" name="Automated Handled" fill="#3B82F6" />
            <Bar dataKey="transferred" name="Transferred" fill="#EF4444" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
