import React from 'react';
import { <PERSON>ert<PERSON><PERSON><PERSON>, Clock, Phone, ShieldCheck } from 'lucide-react';
import { MetricsCard } from '../Dashboard/MetricsCard';

export function PhoneMetrics() {
  return (
    <>
      <MetricsCard
        title="Calls Handled Today"
        value="86"
        change={8.5}
        changeLabel="vs yesterday"
        icon={<Phone size={20} className="text-blue-500" />}
      />
      <MetricsCard
        title="Average Call Duration"
        value="2.8m"
        change={-15.3}
        changeLabel="vs last week"
        icon={<Clock size={20} className="text-blue-500" />}
      />
      <MetricsCard
        title="Verification Success"
        value="92%"
        change={4.2}
        changeLabel="vs last week"
        icon={<ShieldCheck size={20} className="text-blue-500" />}
      />
      <MetricsCard
        title="Transfer Rate"
        value="15%"
        change={-3.5}
        changeLabel="vs last week"
        icon={<AlertTriangle size={20} className="text-blue-500" />}
      />
    </>
  );
}
