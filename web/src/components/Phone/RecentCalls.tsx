/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useState } from 'react';
import { AlertCircle, CheckCircle, Clock, Phone } from 'lucide-react';
import { CallModal } from './CallModal';

const calls = [
  {
    id: 1,
    customer: '<PERSON>',
    purpose: 'NHL Game Stream Quality Issues',
    status: 'completed',
    duration: '4m 15s',
    time: '10 min ago',
    outcome:
      'Successfully resolved streaming quality issues by optimizing CDN settings and clearing device cache. Confirmed improved playback quality.',
    details: {
      callQuality: 98,
      noiseLevel: 'Low',
      transcriptionAccuracy: 95,
      customerMood: 'Satisfied',
      topics: ['Stream Quality', 'Bandwidth', 'Device Settings', 'Cache Clearing'],
      actionItems: [
        'Send bandwidth optimization guide',
        'Schedule quality check follow-up',
        'Update customer device preferences',
        'Monitor stream analytics for 24 hours',
      ],
    },
    timeline: [
      {
        id: '1a',
        time: '0:00',
        event: 'Call Initiated',
        details: 'Customer reported poor streaming quality during NHL game',
      },
      {
        id: '1b',
        time: '1:30',
        event: 'Diagnostics',
        details: 'Performed connection and device diagnostics',
      },
      {
        id: '1c',
        time: '2:45',
        event: 'Resolution Steps',
        details: 'Implemented CDN optimization and cache clearing',
      },
      {
        id: '1d',
        time: '4:15',
        event: 'Quality Confirmation',
        details: 'Verified improved streaming quality with customer',
      },
    ],
  },
  {
    id: 2,
    customer: 'Maria Garcia',
    purpose: 'LIV Golf Subscription Upgrade',
    status: 'transferred',
    duration: '5m 30s',
    time: '25 min ago',
    outcome:
      'Assisted with premium subscription upgrade for upcoming tournament coverage. Transferred to billing specialist for payment processing issue.',
    details: {
      callQuality: 95,
      noiseLevel: 'Medium',
      transcriptionAccuracy: 92,
      customerMood: 'Initially Concerned, then Confident',
      topics: [
        'Subscription Upgrade',
        'Premium Features',
        'Payment Processing',
        'Tournament Access',
      ],
      actionItems: [
        'Process subscription upgrade',
        'Send premium features guide',
        'Schedule activation confirmation',
        'Follow up on payment status',
      ],
    },
    timeline: [
      {
        id: '2a',
        time: '0:00',
        event: 'Call Started',
        details: 'Customer inquiring about premium upgrade for tournament',
      },
      {
        id: '2b',
        time: '2:00',
        event: 'Feature Review',
        details: 'Explained premium subscription benefits',
      },
      {
        id: '2c',
        time: '4:00',
        event: 'Payment Issue',
        details: 'Identified payment processing error',
      },
      {
        id: '2d',
        time: '5:30',
        event: 'Specialist Transfer',
        details: 'Connected with billing specialist',
      },
    ],
  },
  {
    id: 3,
    customer: 'Thomas Wright',
    purpose: 'Multi-device Streaming Setup',
    status: 'completed',
    duration: '3m 45s',
    time: '45 min ago',
    outcome:
      'Configured multi-device streaming settings and verified successful simultaneous playback on TV and mobile.',
    details: {
      callQuality: 96,
      noiseLevel: 'Low',
      transcriptionAccuracy: 94,
      customerMood: 'Initially Confused, then Satisfied',
      topics: [
        'Device Management',
        'Simultaneous Streaming',
        'Account Settings',
        'App Configuration',
      ],
      actionItems: [
        'Update device settings',
        'Send multi-device guide',
        'Verify streaming access',
        'Schedule feature check',
      ],
    },
    timeline: [
      {
        id: '3a',
        time: '0:00',
        event: 'Call Received',
        details: 'Customer seeking help with multi-device setup',
      },
      {
        id: '3b',
        time: '1:15',
        event: 'Account Review',
        details: 'Verified premium subscription status',
      },
      {
        id: '3c',
        time: '2:30',
        event: 'Device Setup',
        details: 'Configured simultaneous streaming settings',
      },
      {
        id: '3d',
        time: '3:45',
        event: 'Verification',
        details: 'Confirmed successful multi-device streaming',
      },
    ],
  },
];

export function RecentCalls() {
  const [selectedCall, setSelectedCall] = useState<number | null>(null);

  const selectedCallData = calls.find((call) => call.id === selectedCall);

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Recent Calls</h3>
        </div>
        <div>
          {calls.map((call) => (
            <div
              key={call.id}
              className="p-6 cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-200"
              onClick={() => setSelectedCall(call.id)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div
                    className={`p-2 rounded-lg ${
                      call.status === 'completed' ? 'bg-green-100' : 'bg-yellow-100'
                    }`}
                  >
                    {call.status === 'completed' ? (
                      <CheckCircle size={20} className="text-green-600" />
                    ) : (
                      <AlertCircle size={20} className="text-yellow-600" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{call.purpose}</h4>
                    <p className="text-sm text-gray-500">{call.customer}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-gray-500">{call.time}</div>
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Clock size={14} />
                    {call.duration}
                  </div>
                </div>
              </div>
              <div className="ml-12">
                <div className="flex items-center gap-2 mb-2">
                  <Phone size={16} className="text-blue-500" />
                  <span className="text-sm text-gray-600">Call Summary:</span>
                </div>
                <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{call.outcome}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedCallData && (
        <CallModal
          isOpen={selectedCall !== null}
          onClose={() => setSelectedCall(null)}
          call={selectedCallData}
        />
      )}
    </>
  );
}
