/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react/button-has-type */
import React from 'react';
import { Activity, BarChart2, Bot, Clock, Phone, User, X } from 'lucide-react';

interface CallModalProps {
  isOpen: boolean;
  onClose: () => void;
  call: {
    id: number;
    customer: string;
    purpose: string;
    status: string;
    duration: string;
    time: string;
    outcome: string;
    details: {
      callQuality: number;
      noiseLevel: string;
      transcriptionAccuracy: number;
      customerMood: string;
      topics: string[];
      actionItems: string[];
    };
    timeline: {
      id: string;
      time: string;
      event: string;
      details: string;
    }[];
  };
}

export function CallModal({ isOpen, onClose, call }: CallModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <div className="p-6 border-b flex justify-between items-start border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{call.purpose}</h2>
            <p className="text-sm text-gray-500">{call.customer}</p>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-500 transition-colors">
            <X size={24} />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-2">Call Summary</h3>
                <p className="text-sm text-gray-600">{call.outcome}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-2">Call Metrics</h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Call Quality</span>
                    <div className="flex items-center">
                      <div className="w-24 h-2 bg-gray-200 rounded-full mr-2">
                        <div
                          className="h-full bg-green-500 rounded-full"
                          style={{ width: `${call.details.callQuality}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium">{call.details.callQuality}%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Background Noise</span>
                    <span className="text-sm font-medium">{call.details.noiseLevel}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Transcription Accuracy</span>
                    <span className="text-sm font-medium">
                      {call.details.transcriptionAccuracy}%
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Customer Mood</span>
                    <span className="text-sm font-medium">{call.details.customerMood}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-2">Topics Discussed</h3>
                <div className="flex flex-wrap gap-2">
                  {call.details.topics.map((topic, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                    >
                      {topic}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-4">Call Timeline</h3>
              <div className="space-y-4">
                {call.timeline.map((event) => (
                  <div key={event.id} className="relative pl-6 pb-4 border-l border-gray-200">
                    <div className="absolute left-0 top-2 w-2 h-2 -ml-1 rounded-full bg-blue-500" />
                    <div className="text-xs text-gray-500 mb-1">{event.time}</div>
                    <div className="text-sm font-medium text-gray-900 mb-1">{event.event}</div>
                    <div className="text-sm text-gray-600">{event.details}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="p-6 border-t border-gray-200 bg-gray-50">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Action Items</h3>
            <ul className="space-y-2">
              {call.details.actionItems.map((item, index) => (
                <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                  <span className="text-blue-500 mt-1">•</span>
                  {item}
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Clock size={16} className="text-blue-500" />
                <span className="text-sm text-gray-600">Duration: {call.duration}</span>
              </div>
              <div className="flex items-center gap-2">
                <Activity size={16} className="text-blue-500" />
                <span className="text-sm text-gray-600">
                  Status: <span className="font-medium capitalize">{call.status}</span>
                </span>
              </div>
            </div>
            <span className="text-sm text-gray-500">{call.time}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
