import { VectorKnowledge } from '@/components/AgentBuilder/VectorKnowledge';
import { authGuardHOC } from '@/components/authGuardHOC';
import { AgentBuilder } from '../components/AgentBuilder/AgentBuilder';
import { Layout } from '../components/Layout/Layout';

function AgentBuilderPage() {
  return (
    <Layout>
      <AgentBuilder />
      <div className="p-6">
        <VectorKnowledge />
      </div>
    </Layout>
  );
}

export default authGuardHOC(AgentBuilderPage);
