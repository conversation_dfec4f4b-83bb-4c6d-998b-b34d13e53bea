import { ChatHistory } from '@/components/AgentBuilder/ChatHistory';
import { pilotPageAuthGuardHOC } from '@/components/authGuardHOC';
import { FileUpload } from '@/components/FileUpload/FileUpload';
import PilotNav from '@/components/Layout/PilotNav';
import { AgentBuilder } from '../components/AgentBuilder/AgentBuilder';
import { DocList } from '../components/AgentBuilder/DocList';
import { Analytics } from '../components/Analytics/Analytics';

function PilotPage() {
  return (
    <>
      {/* TODO - de-compose the AgentBuilder component to avoid hiding buttons with style tags  */}
      <style>
        {`
          #create-new-agent-button {
            display: none;
          }
        [aria-label="delete"] {
          display: none;
        }
        `}
      </style>
      <PilotNav />
      <main className="min-h-screen p-6 bg-gray-50">
        <div className="max-w-screen-lg mx-auto">
          <Analytics />
          <AgentBuilder />
          <div className="p-6">
            <FileUpload />
            <DocList />
            <ChatHistory />
          </div>
        </div>
      </main>
    </>
  );
}

export default pilotPageAuthGuardHOC(PilotPage);
