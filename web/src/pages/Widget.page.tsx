import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Anchor } from '@mantine/core';
import Chat from '@/components/Chat/Chat';
import useChat from '@/components/Chat/useChat';
import { Agent } from '@/types/agents';
import { getWebsocketUrl } from '@/util/chat';

function WidgetPage() {
  const { tenantId, agentId } = useParams<{ tenantId: string; agentId: string }>();

  const [config, setConfig] = useState({
    title: 'Support',
    subTitle: "We're here to help!",
    primaryColor: '#0ea5e9',
    domain: 'exampledomain.com',
    placeholder: 'Ask me anything',
  });

  const [_tenantInfo, setTenantInfo] = useState<{
    name: string;
    slug: string;
  } | null>(null);

  // Read URL parameters for configuration
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    setConfig({
      title: urlParams.get('title') || 'Support',
      subTitle: urlParams.get('subTitle') || "We're here to help!",
      primaryColor: urlParams.get('primaryColor') || '#0ea5e9',
      domain: urlParams.get('domain') || 'exampledomain.com',
      placeholder: urlParams.get('placeholder') || 'Ask me anything',
    });
  }, []);

  // Fetch tenant information if tenantId is available
  useEffect(() => {
    if (tenantId) {
      // Map tenant IDs to tenant names for display
      const tenantMap: Record<string, { name: string; slug: string }> = {
        sifive: { name: 'SiFive', slug: 'sifive' },
        safran: { name: 'Safran', slug: 'safran' },
      };

      const tenant = tenantMap[tenantId];
      if (tenant) {
        setTenantInfo(tenant);
        // Update config with tenant-specific defaults
        setConfig((prev) => ({
          ...prev,
          title: prev.title === 'Support' ? `${tenant.name} Support` : prev.title,
        }));
      }
    }
  }, [tenantId]);

  // Create agent data with tenant and agent information
  const agentIdValue = agentId || 'sifive-external-chat-agent';
  const isPrivateAgent = agentIdValue.includes('internal');

  // Block access to private agents through widget
  if (isPrivateAgent) {
    return (
      <div className="h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">
            This agent is not available through the public widget. Private agents require
            authentication.
          </p>
        </div>
      </div>
    );
  }

  const agent: Agent = {
    id: agentIdValue,
    name: config.title,
    description: config.subTitle,
    type: 'CHAT',
    phoneNumber: undefined,
    isPrivate: false, // Widget only supports public agents
    events: [],
  };

  const websocketUrl = getWebsocketUrl(agent);

  const chat = useChat(websocketUrl);

  const hasAuthenticationError = !websocketUrl;
  if (hasAuthenticationError) {
    return (
      <div className="h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Connection Error</h1>
          <p className="text-gray-600 mb-4">Authentication token required for private agents</p>
        </div>
      </div>
    );
  }

  const hasSocketError = chat.status === 'DISCONNECTED' || chat.status === 'ERROR';

  if (hasSocketError) {
    return (
      <div className="h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Connection Error
            <span hidden={!chat.closeEvent?.code}> ({chat.closeEvent?.code})</span>
          </h1>
          <Anchor component="button" onClick={() => window.location.reload()}>
            Retry
          </Anchor>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-white flex flex-col">
      {/* Fixed Header */}
      <div
        className="border-b border-gray-200 px-6 py-4 flex-shrink-0 bg-white"
        style={{ borderBottomColor: `${config.primaryColor}20` }}
      >
        <h1 className="text-xl font-semibold text-gray-900" style={{ color: config.primaryColor }}>
          {agent.name}
        </h1>
        <p className="text-sm text-gray-600 mt-1">{agent.description}</p>
      </div>

      {/* Scrollable Chat Messages */}
      <div className="flex-1 overflow-hidden">
        <Chat
          agent={agent}
          chat={chat}
          primaryColor={config.primaryColor}
          placeholder={config.placeholder}
        />
      </div>
    </div>
  );
}

export default WidgetPage;
