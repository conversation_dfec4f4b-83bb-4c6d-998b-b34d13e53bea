import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Flex, PasswordInput, useMantineTheme } from '@mantine/core';
import { useForm } from '@mantine/form';
import Button from '@/components/Button/Button';
import { Icon } from '@/components/icon/Icon';
import { Input } from '@/components/Input/Input';
import { Heading } from '@/components/Texts/Heading/Heading';
import { Text } from '@/components/Texts/Text/Text';
import { useAppContext } from '@/context/AppContext';

export function LoginPage() {
  const form = useForm({
    initialValues: {
      username: '',
      password: '',
    },
    validate: {
      password: (value) => (value.length > 0 ? null : 'Password is required'),
    },
  });

  const { initialize, userInfo, initialized } = useAppContext();
  const truthKeepTheme = useMantineTheme().other.truthKeep;
  const navigate = useNavigate();

  const handleSubmit = async (values: { username: string; password: string }) => {
    const body = JSON.stringify(values);
    const response = await fetch('/api/login', {
      method: 'POST',
      body,
      headers: { 'Content-Type': 'application/json' },
    });

    if (response.status === 204) {
      navigate('/home');
      initialize();
      return;
    }

    if (response.status === 401) {
      const error = 'Invalid email or password';
      form.setErrors({ username: error, password: error });
    }
  };

  useEffect(() => {
    if (initialized && userInfo) {
      navigate('/home');
    }
  }, [userInfo, initialized, navigate]);

  return (
    <Flex align="center" justify="between" h="100%">
      <div
        className="min-w-[520px] h-full overflow-hidden relative"
        style={{ backgroundColor: truthKeepTheme.secondaryBackgroundColor }}
      >
        <Flex
          direction="column"
          align="center"
          justify="center"
          gap={32}
          className="mx-[82px] mt-[47%]"
        >
          <Heading tagLevel="2" style={{ color: truthKeepTheme.textColor }}>
            Welcome to Truthkeep
          </Heading>
        </Flex>
        <img
          src="/icons/tkLogoLogin.svg"
          alt="logo"
          className="absolute bottom-0 left-1/2 -translate-x-1/2"
        />
      </div>

      <Flex
        direction="column"
        w="100%"
        h="100%"
        gap={12}
        className="p-[40px]"
        style={{ backgroundColor: truthKeepTheme.backgroundColor }}
      >
        <Flex h="72px" align="center" gap={12}>
          <img src="/icons/tkLogoSmall.svg" alt="logo" />
          <Heading tagLevel="3" className="font-normal">
            Truthkeep
          </Heading>
        </Flex>
        <Flex w="100%" h="100%" align="center" justify="center">
          <Flex direction="column" w={289} h="100%" justify="space-between">
            <form
              noValidate
              onSubmit={form.onSubmit(handleSubmit)}
              className="h-full flex items-center"
            >
              <Flex direction="column" w="100%">
                <Text size="xl" bold className="mb-[24px] text-center">
                  Login
                </Text>
                <Flex direction="column" gap={16} className="mb-[24px]">
                  <Input
                    type="text"
                    label="Username"
                    required
                    {...form.getInputProps('username')}
                  />
                  <PasswordInput
                    withAsterisk
                    label="Password"
                    leftSection={<Icon iconCategory="input" iconType="lock" />}
                    {...form.getInputProps('password')}
                  />
                </Flex>
                <Flex justify="space-between" align="center">
                  <Button type="submit" size="auto">
                    Login
                  </Button>
                </Flex>
              </Flex>
            </form>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
}
