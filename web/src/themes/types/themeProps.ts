import { ButtonThemeProps } from './componentProps/buttonThemeProps';
import { SwitchThemeProps } from './componentProps/switchThemeProps';
import { TextThemeProps } from './componentProps/textThemeProps';
import { TurthKeepThemeProps } from './componentProps/truthKeepThemeProps';

export interface ThemeProps {
  other: {
    truthKeep: TurthKeepThemeProps;
    button: ButtonThemeProps;
    switch: SwitchThemeProps;
    text: TextThemeProps;
  };
}
