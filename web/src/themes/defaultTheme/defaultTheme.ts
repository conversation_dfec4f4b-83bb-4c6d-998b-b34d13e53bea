import { MantineThemeOverride } from '@mantine/core';
import { ThemeProps } from '../types/themeProps';
import { defaultButtonTheme } from './defaultComponentTheme/defaultButtonTheme';
import { defaultSwitchTheme } from './defaultComponentTheme/defaultSwitchTheme';
import { defaultTextTheme } from './defaultComponentTheme/defaultTextTheme';
import { defaultTruthKeepTheme } from './defaultComponentTheme/defaultTruthKeepTheme';

export const defaultTheme: MantineThemeOverride = {
  components: {
    InputWrapper: {
      styles: {
        label: {
          fontWeight: 700,
          fontSize: '14px',
          lineHeight: '16px',
        },
      },
    },
  },
  other: {
    truthKeep: defaultTruthKeepTheme,
    button: defaultButtonTheme,
    switch: defaultSwitchTheme,
    text: defaultTextTheme,
  },
} as ThemeProps;
