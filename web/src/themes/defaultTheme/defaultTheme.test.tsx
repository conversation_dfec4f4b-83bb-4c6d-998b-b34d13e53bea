import { defaultTheme } from '@/themes/defaultTheme/defaultTheme';
import { ThemeProps } from '@/themes/types/themeProps';

const themes: { name: string; theme: ThemeProps }[] = [
  { name: 'defaultTheme', theme: defaultTheme as ThemeProps },
];

describe('Button themes for all themes are structurally valid', () => {
  themes.forEach(({ name, theme }) => {
    describe(name, () => {
      const button = theme.other.button;

      it('has primary backgroundColor and color', () => {
        expect(button.primary.backgroundColor).toBeDefined();
        expect(typeof button.primary.backgroundColor).toBe('string');

        expect(button.primary.color).toBeDefined();
        expect(typeof button.primary.color).toBe('string');
      });

      it('has secondary backgroundColor, color, and border', () => {
        expect(button.secondary.backgroundColor).toBeDefined();
        expect(typeof button.secondary.backgroundColor).toBe('string');

        expect(button.secondary.color).toBeDefined();
        expect(typeof button.secondary.color).toBe('string');

        expect(button.secondary.border).toBeDefined();
        expect(typeof button.secondary.border).toBe('string');
      });

      it('has danger backgroundColor and color', () => {
        expect(button.danger.backgroundColor).toBeDefined();
        expect(typeof button.danger.backgroundColor).toBe('string');

        expect(button.danger.color).toBeDefined();
        expect(typeof button.danger.color).toBe('string');
      });

      it('has secondaryDanger backgroundColor, color, and border', () => {
        expect(button.secondaryDanger.backgroundColor).toBeDefined();
        expect(typeof button.secondaryDanger.backgroundColor).toBe('string');

        expect(button.secondaryDanger.color).toBeDefined();
        expect(typeof button.secondaryDanger.color).toBe('string');

        expect(button.secondaryDanger.border).toBeDefined();
        expect(typeof button.secondaryDanger.border).toBe('string');
      });
    });
  });
});

describe('Switch themes for all themes are structurally valid', () => {
  themes.forEach(({ name, theme }) => {
    describe(name, () => {
      const switchTheme = theme.other.switch;

      it('has primary backgroundColor and color', () => {
        expect(switchTheme.backgroundColor).toBeDefined();
        expect(typeof switchTheme.backgroundColor).toBe('string');
      });
    });
  });
});
