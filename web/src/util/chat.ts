import { Agent } from '@/types/agents';

// Helper function to get auth token from cookies
function getAuthTokenFromCookies(): string | null {
  const cookies = document.cookie.split(';');

  // Try different cookie names that might be used
  const possibleNames = ['ws_auth', 'auth', 'access_token', 'token', 'jwt'];

  for (const name of possibleNames) {
    const cookie = cookies.find((c) => c.trim().startsWith(`${name}=`));
    if (cookie) {
      return cookie.split('=')[1];
    }
  }

  return null;
}

export function getWebsocketUrl(agent?: Agent, authToken?: string): string | undefined {
  if (!agent) return undefined;

  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = process.env.NODE_ENV === 'development' ? 'localhost:3000' : window.location.host;

  // Use authenticated endpoint for private agents, public endpoint for public agents
  const endpoint = agent.isPrivate ? '/api/chat/auth' : '/api/chat';

  // For private agents, get token from cookies if not provided
  const token = authToken || (agent.isPrivate ? getAuthTokenFromCookies() : null);

  // If it's a private agent but no token is available, return undefined instead of throwing
  if (agent.isPrivate && !token) {
    return undefined;
  }

  const params = new URLSearchParams({ agentId: agent.id });
  if (agent.isPrivate && token) {
    params.set('token', token);
  }

  const socketUrl = `${protocol}//${host}${endpoint}?${params.toString()}`;
  return socketUrl;
}
