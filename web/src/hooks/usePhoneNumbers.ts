import { useEffect, useState } from 'react';
import * as libphonenumber from 'libphonenumber-js';
import { ComboboxItem } from '@mantine/core';
import { agentService } from '../api/agent/agentService';

export default function usePhoneNumbers(disabled: boolean) {
  const [phoneNumbers, setPhoneNumbers] = useState<ComboboxItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (disabled) return;

    setIsLoading(true);
    agentService.getPhoneNumbers().then((response) => {
      const availablePhoneNumbers = response.data.items as {
        phoneNumber: string;
        isInUse: boolean;
      }[];

      const phoneNumberOptions = availablePhoneNumbers.map(({ phoneNumber, isInUse }) => ({
        label: libphonenumber.format(phoneNumber, 'NA', 'INTERNATIONAL'),
        value: phoneNumber,
        disabled: isInUse,
      }));

      setPhoneNumbers([...phoneNumberOptions, { label: '-', value: '' }]);
      setIsLoading(false);
    });
  }, [disabled]);

  const firstAvailablePhoneNumber = phoneNumbers.find(({ disabled }) => !disabled)?.value;

  return { isLoading, phoneNumbers, firstAvailablePhoneNumber };
}
