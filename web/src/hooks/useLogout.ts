import { useNavigate } from 'react-router-dom';
import { authService } from '@/api/auth/authService';
import { useAppContext } from '@/context/AppContext';

export default function useLogout() {
  const appContext = useAppContext();
  const navigate = useNavigate();

  const logout = async () => {
    await authService.logout();
    appContext.initialize();
    navigate('/login');
  };

  return logout;
}
