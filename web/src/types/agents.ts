export interface Agent {
  id: string;
  name: string;
  type: AgentType;
  description: string;
  phoneNumber?: string;
  isPrivate?: boolean;
  events: Event[];
}

export interface Event {
  id: string;
  name: string;
  description: string;
  actions: { type: ActionType; forwardToNumber?: string }[];
}

export type AgentType = 'PHONE' | 'EMAIL' | 'CHAT';

export type ActionType =
  | 'FORWARD_EMAIL'
  | 'CREATE_TICKET'
  | 'EDIT_TICKET'
  | 'FORWARD_CALL'
  | 'FORWARD_TO_HUMAN';
