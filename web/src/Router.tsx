import { createBrowserRouter, Navigate, RouterProvider } from 'react-router-dom';
import AgentBuilderPage from './pages/AgentBuilder.page';
import EmailDashboardPage from './pages/Email.page';
import { LoginPage } from './pages/Login.page';
import PhoneDashboardPage from './pages/Phone.page';
import PilotPage from './pages/Pilot.page';
import SettingsPage from './pages/Settings.page';
import TicketsPage from './pages/Tickets.page';
import WidgetPage from './pages/Widget.page';

const router = createBrowserRouter([
  {
    path: '/',
    element: <TicketsPage />,
  },
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/home',
    element: <Navigate to="/pilot" replace />,
  },
  {
    path: '/pilot',
    element: <PilotPage />,
  },
  {
    path: '/tickets',
    element: <TicketsPage />,
  },
  {
    path: '/email',
    element: <EmailDashboardPage />,
  },
  {
    path: '/phone',
    element: <PhoneDashboardPage />,
  },
  {
    path: '/settings',
    element: <SettingsPage />,
  },
  {
    path: '/agent-builder',
    element: <AgentBuilderPage />,
  },
  // Dynamic tenant/agent routes
  {
    path: '/:tenantId/:agentId/widget',
    element: <WidgetPage />,
  },
  {
    path: '/:tenantId/:agentId/chat',
    element: <PilotPage />,
  },
  // Legacy routes for backward compatibility
  // {
  //   path: '/sifive/chat',
  //   element: <PilotPage />,
  // },
  // {
  //   path: '/sifive/widget',
  //   element: <WidgetPage />,
  // },
  // {
  //   path: '/chat/widget',
  //   element: <WidgetPage />,
  // },
  {
    path: '*',
    element: <Navigate to="/" replace />,
  },
]);

export function Router() {
  return <RouterProvider router={router} />;
}
