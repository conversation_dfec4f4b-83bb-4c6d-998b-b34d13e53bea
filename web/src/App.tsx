import '@mantine/notifications/styles.css';
import '@mantine/core/styles.css';
import './global.css';

import { Notifications } from '@mantine/notifications';
import { AppProvider } from './context/AppContext';
import { AppThemeProvider } from './context/AppThemeContext';
import { Router } from './Router';

export default function App() {
  return (
    <AppProvider>
      <AppThemeProvider>
        <Notifications />
        <Router />
      </AppThemeProvider>
    </AppProvider>
  );
}
