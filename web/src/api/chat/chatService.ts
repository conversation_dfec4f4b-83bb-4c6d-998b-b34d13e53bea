import apiClient from '../apiClient';

export interface ChatSession {
  id: string;
  date: string;
  lastMessage: string;
  agentId: string;
  agentName: string;
  resolved: boolean;
  endedAt: string;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  text: string;
  createdAt: string;
}

export interface SessionInfo {
  id: string;
  agentId: string;
  agentName: string;
  agentDescription: string;
  createdAt: string;
  endedAt: string;
  resolved: boolean;
}

export interface SessionMessages {
  session: SessionInfo;
  messages: ChatMessage[];
}

export class ChatService {
  static async getConversationSessions(agentId?: string, limit?: number): Promise<ChatSession[]> {
    const params = new URLSearchParams();
    if (agentId) params.append('agentId', agentId);
    if (limit) params.append('limit', limit.toString());

    const response = await apiClient.get(`/chat/sessions?${params.toString()}`);
    return response.data.items;
  }

  static async getSessionMessages(sessionId: string): Promise<SessionMessages> {
    const response = await apiClient.get(`/chat/sessions/${sessionId}/messages`);
    return response.data;
  }
}
