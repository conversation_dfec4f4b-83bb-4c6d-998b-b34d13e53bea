import { ActionType, AgentType } from '../../types/agents';
import apiClient from '../apiClient';

export const agentService = {
  create: async ({
    type,
    name,
    description,
    phoneNumber,
  }: {
    type: AgentType;
    name: string;
    description: string;
    phoneNumber?: string;
  }) => {
    await apiClient.post('/agents', { name, type, description, phoneNumber });
  },

  createEvent: async ({
    agentId,
    name,
    description,
    actions,
  }: {
    agentId?: string;
    name?: string;
    description?: string;
    actions?: { type: ActionType; forwardToNumber?: string }[];
  }) => {
    await apiClient.post(`/agents/${agentId}/events`, { name, description, actions });
  },

  edit: async ({
    id,
    name,
    description,
    phoneNumber,
    events,
  }: {
    id: string;
    name: string;
    description: string;
    phoneNumber?: string;
    events: { name: string; description: string; actions: { type: ActionType }[] }[];
  }) => {
    await apiClient.put(`/agents/${id}`, { name, description, phoneNumber, events });
  },

  list: async () => {
    return apiClient.get('/agents');
  },

  delete: async (agentId: string) => {
    return apiClient.delete(`/agents/${agentId}`);
  },

  deleteEvent: async (agentId?: string, eventId?: string) => {
    return apiClient.delete(`/agents/${agentId}/events/${eventId}`);
  },

  getPhoneNumbers: async () => {
    return apiClient.get('/phone-numbers');
  },
};
