import axios, { AxiosResponse } from 'axios';

const apiClient = axios.create({
  baseURL: '/api',
  withCredentials: true,
});

let refreshPromise: Promise<AxiosResponse<any, any>> | null = null;

const refreshAuthToken = async () => {
  return apiClient.post('/refresh');
};

apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (originalRequest.url?.includes('/refresh')) {
      return Promise.reject(error);
    }

    if (error.response?.status !== 401) {
      return Promise.reject(error);
    }
    if (!refreshPromise) {
      refreshPromise = refreshAuthToken().finally(() => {
        refreshPromise = null;
      });
    }

    try {
      await refreshPromise;
      return apiClient(originalRequest);
    } catch (refreshError) {
      return Promise.reject(refreshError);
    }
  }
);

export default apiClient;
