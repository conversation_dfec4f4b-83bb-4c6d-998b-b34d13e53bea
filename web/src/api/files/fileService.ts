import apiClient from '../apiClient';

export interface PresignedUrlRequest {
  fileName: string;
  contentType: string;
  expiresIn?: number;
  public?: boolean;
}

export interface PresignedUrlResponse {
  url: string;
  bucket: string;
  key: string;
  expiresAt: string;
  operation: string;
}

export const fileService = {
  async getPresignedUrl(request: PresignedUrlRequest): Promise<PresignedUrlResponse> {
    const response = await apiClient.post<PresignedUrlResponse>('/files/presigned-url', request);
    return response.data;
  },

  async uploadFileToS3(file: File, presignedUrl: string): Promise<void> {
    const response = await fetch(presignedUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
  },
};
