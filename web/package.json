{"name": "mantine-vite-template", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "typecheck": "tsc --noEmit", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint . --cache", "stylelint": "stylelint '**/*.css' --cache", "prettier": "prettier --check \"**/*.{ts,tsx}\"", "prettier:write": "prettier --write \"**/*.{ts,tsx}\"", "vitest": "vitest run", "vitest:watch": "vitest", "test": "npm run typecheck && npm run prettier && npm run lint && npm run vitest && npm run build", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build"}, "dependencies": {"@mantine/core": "^7.17.4", "@mantine/form": "^7.17.0", "@mantine/hooks": "^7.17.4", "@mantine/notifications": "^7.17.4", "@tailwindcss/postcss": "^4.0.13", "axios": "^1.8.3", "libphonenumber-js": "^1.12.7", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-imask": "^7.6.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.1.3", "recharts": "^2.15.2", "remark-gfm": "^4.0.1", "tailwindcss": "^4.0.13"}, "devDependencies": {"@eslint/js": "^9.18.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@storybook/addon-essentials": "^8.6.7", "@storybook/react": "^8.5.0", "@storybook/react-vite": "^8.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.0", "@types/node": "^22.10.7", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.18.0", "eslint-config-mantine": "^4.0.3", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "identity-obj-proxy": "^3.0.0", "jsdom": "^26.0.0", "postcss": "^8.5.3", "postcss-preset-mantine": "1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.4.2", "prop-types": "^15.8.1", "storybook": "^8.5.0", "storybook-dark-mode": "^4.0.2", "stylelint": "^16.13.2", "stylelint-config-standard-scss": "^14.0.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "vite": "^6.0.7", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5"}}