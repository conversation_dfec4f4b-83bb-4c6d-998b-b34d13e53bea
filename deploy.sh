#!/usr/bin/env bash

if [[ $(uname -m) =~ ^(arm64|aarch64|arm) ]]; then
    docker buildx build --platform linux/amd64 . -f ./docker/Dockerfile --tag tk-agents-app:latest
else
    docker build . -f ./docker/Dockerfile --tag tk-agents-app:latest
fi

docker tag tk-agents-app 381492288376.dkr.ecr.us-east-2.amazonaws.com/phone-agent-demo:latest
docker push 381492288376.dkr.ecr.us-east-2.amazonaws.com/phone-agent-demo:latest

aws ecs update-service \
  --cluster agents-demo \
  --service phone-agent-demo \
  --force-new-deployment
