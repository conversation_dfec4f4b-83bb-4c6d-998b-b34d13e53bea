/* chat-widget embed v2.0.0 - iframe-based */
(() => {
  // ===== utilities =====
  const b64u = {
    enc: (obj) => {
      const s = JSON.stringify(obj);
      return btoa(unescape(encodeURIComponent(s))).replace(/\+/g,'-').replace(/\//g,'_').replace(/=+$/,'');
    },
    dec: (str) => {
      try {
        str = (str || '').replace(/-/g,'+').replace(/_/g,'/');
        // pad
        while (str.length % 4) str += '=';
        const s = decodeURIComponent(escape(atob(str)));
        return JSON.parse(s);
      } catch { return null; }
    }
  };
  const toBool = (v, d=false) => v == null ? d : String(v).toLowerCase() === "true";
  const num = (v, d) => (v == null || isNaN(+v)) ? d : +v;

  // Read the <script> that loaded this file
  const me = document.currentScript || (() => {
    const scripts = document.getElementsByTagName('script');
    return scripts[scripts.length - 1];
  })();

  // Convert data attributes from kebab-case to camelCase
  const dataAttributes = Array.from(me.attributes).reduce((acc, attr) => {
    if (attr.name.startsWith('data-')) {
      const camelCaseName = attr.name.slice(5).replace(/-([a-z])/g, (g) => g[1].toUpperCase());
      acc[camelCaseName] = attr.value;
    }
    return acc;
  }, {});

  console.log('Data Attributes:', dataAttributes);

  const url = new URL(me.src, location.href);
  const qp = Object.fromEntries(url.searchParams.entries());

  // Allow a single compressed config blob too
  const blob = qp.config ? b64u.dec(qp.config) : null;

  // ===== defaults & final cfg =====
  const DEFAULTS = {
    id: "sifive",
    domain: "exampledomain.com",
    title: "Chat Support",
    subTitle: "We're here to help!",
    position: "bottom-right",
    primaryColor: "#0ea5e9",
    placeholder: "Ask me anything",
    zIndex: 2147483646,
    baseUrl: "https://agents.truthkeep.ai",
    minWidth: 400,
    minHeight: 800,
  };

  const cfg = Object.assign(
    {}, DEFAULTS,
    blob || {},
    dataAttributes,
    {
      domain: qp.domain ?? dataAttributes.domain ?? (blob && blob.domain),
      title: qp.title ?? dataAttributes.title ?? (blob && blob.title),
      subTitle: qp.subTitle ?? dataAttributes.subTitle ?? (blob && blob.subTitle),
      position: qp.position ?? dataAttributes.position ?? (blob && blob.position),
      primaryColor: qp.primaryColor ?? dataAttributes.primaryColor ?? (blob && blob.primaryColor),
      placeholder: qp.placeholder ?? dataAttributes.placeholder ?? (blob && blob.placeholder) ?? DEFAULTS.placeholder,
      zIndex: num(qp.zIndex ?? dataAttributes.zIndex ?? (blob && blob.zIndex), DEFAULTS.zIndex),
      baseUrl: qp.baseUrl ?? dataAttributes.baseUrl ?? (blob && blob.baseUrl) ?? DEFAULTS.baseUrl,
      minWidth: num(qp.minWidth ?? dataAttributes.minWidth ?? (blob && blob.minWidth), DEFAULTS.minWidth),
      minHeight: num(qp.minHeight ?? dataAttributes.minHeight ?? (blob && blob.minHeight), DEFAULTS.minHeight),
    }
  );

  console.log('Chat Widget Config:', cfg);

  // singleton guard
  if (window.ChatWidget?.mounted) return;
  window.ChatWidget = Object.assign(window.ChatWidget || {}, { mounted: true });

  // ===== DOM (Shadow) =====
  const host = document.createElement("div");
  host.id = "chat-widget-host";
  host.style.position = "fixed";
  host.style[cfg.position === "bottom-left" ? "left" : "right"] = "20px";
  host.style.bottom = "20px";
  host.style.zIndex = String(cfg.zIndex);
  document.addEventListener("DOMContentLoaded", () => document.body.appendChild(host));
  const shadow = host.attachShadow({ mode: "open" });

  const style = document.createElement("style");
  style.textContent = `
    :host { all: initial; }
    *, *::before, *::after { box-sizing: border-box; font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, sans-serif; }
    .btn { display:inline-flex;align-items:center;justify-content:center;width:54px;height:54px;border-radius:9999px;border:none;cursor:pointer;
      background:${cfg.primaryColor};color:#fff;box-shadow:0 10px 30px rgba(0,0,0,.15); }
    .panel { width:${cfg.minWidth}px;height:${cfg.minHeight}px;display:none;flex-direction:column;background:#fff;border-radius:16px;overflow:hidden;
      box-shadow:0 20px 60px rgba(0,0,0,.22);margin-bottom:10px; position: relative; }
    .iframe { width:100%;height:100%;border:none;background:#fff; }
    .error{color:#ef4444;font-size:12px;padding:8px;background:#fef2f2;border-radius:8px;margin:8px;text-align:center;}
    .resize-handle { position: absolute; top: 0; left: 0; width: 24px; height: 24px; cursor: nw-resize; 
      background: 
        radial-gradient(circle at 4px 4px, #999 1px, transparent 1px),
        radial-gradient(circle at 8px 4px, #999 1px, transparent 1px),
        radial-gradient(circle at 12px 4px, #999 1px, transparent 1px),
        radial-gradient(circle at 16px 4px, #999 1px, transparent 1px),
        radial-gradient(circle at 4px 8px, #999 1px, transparent 1px),
        radial-gradient(circle at 8px 8px, #999 1px, transparent 1px),
        radial-gradient(circle at 12px 8px, #999 1px, transparent 1px),
        radial-gradient(circle at 4px 12px, #999 1px, transparent 1px),
        radial-gradient(circle at 8px 12px, #999 1px, transparent 1px),
        radial-gradient(circle at 4px 16px, #999 1px, transparent 1px);
      background-size: 24px 24px;
      opacity: 0.7; transition: all 0.2s ease; border-radius: 0 0 16px 0; }
    .resize-handle:hover { opacity: 1; }
  `;
  shadow.appendChild(style);

  const h = (tag, props={}, ...kids) => {
    const el = document.createElement(tag);
    for (const [k,v] of Object.entries(props)) {
      if (k === "style" && v && typeof v === "object") Object.assign(el.style, v);
      else if (k.startsWith("on")) el.addEventListener(k.slice(2).toLowerCase(), v);
      else if (k in el) el[k] = v;
      else el.setAttribute(k, v);
    }
    kids.flat().forEach(c => el.append(c));
    return el;
  };

  // Build iframe URL with configuration parameters
  const buildIframeUrl = () => {
    // Construct the widget URL using the id parameter
    const baseUrl = `${cfg.baseUrl}/${cfg.id}/${cfg.id}-external-chat-agent/widget`;
    const url = new URL(baseUrl);
    url.searchParams.set('title', cfg.title);
    url.searchParams.set('subTitle', cfg.subTitle);
    url.searchParams.set('primaryColor', cfg.primaryColor);
    url.searchParams.set('domain', cfg.domain);
    url.searchParams.set('placeholder', cfg.placeholder);
    return url.toString();
  };

  const panel = h("div", { className: "panel", role: "dialog", "aria-label": "Chat panel" },
    h("iframe", { 
      className: "iframe", 
      id: "chatIframe",
      src: buildIframeUrl(),
      title: cfg.title,
      allow: "microphone; camera"
    }),
    h("div", { className: "resize-handle", id: "resizeHandle" })
  );
  
  const toggleBtn = h("button", { className: "btn", id: "toggleBtn", "aria-expanded": "false", title: "Open chat" }, 
    "💬"
  );

  shadow.appendChild(h("div", {}, panel, toggleBtn));

  const iframe = panel.querySelector("#chatIframe");
  let open = false;
  
  const setOpen = (v) => {
    open = v;
    panel.style.display = open ? "flex" : "none";
    toggleBtn.setAttribute("aria-expanded", String(open));
    toggleBtn.textContent = open ? "X" : "💬";
    toggleBtn.style.position = "fixed";
    toggleBtn.style[cfg.position === "bottom-left" ? "left" : "right"] = "20px";
    toggleBtn.style.bottom = "20px";
    toggleBtn.style[cfg.position === "bottom-left" ? "right" : "left"] = "auto";
    panel.style.marginBottom = open ? "65px" : "0";
  };

  toggleBtn.addEventListener("click", () => setOpen(!open));
  shadow.addEventListener("keydown", (e) => e.key === "Escape" && open && setOpen(false));

  // Resize functionality
  const resizeHandle = panel.querySelector("#resizeHandle");
  let isResizing = false;
  let startX, startY, startWidth, startHeight;

  resizeHandle.addEventListener("mousedown", (e) => {
    isResizing = true;
    startX = e.clientX;
    startY = e.clientY;
    startWidth = parseInt(document.defaultView.getComputedStyle(panel).width, 10);
    startHeight = parseInt(document.defaultView.getComputedStyle(panel).height, 10);
    e.preventDefault();
  });

  document.addEventListener("mousemove", (e) => {
    if (!isResizing) return;
    
    // For top-left resize, we need to invert the calculation
    const newWidth = startWidth - (e.clientX - startX);
    const newHeight = startHeight - (e.clientY - startY);
    
    // Use configurable minimum and maximum sizes
    const minWidth = cfg.minWidth;
    const maxWidth = cfg.maxWidth;
    const minHeight = cfg.minHeight;
    const maxHeight = cfg.maxHeight;
    
    // Apply only minimum constraints - allow unlimited resizing upward
    const constrainedWidth = Math.max(minWidth, newWidth);
    const constrainedHeight = Math.max(minHeight, newHeight);
    
    panel.style.width = constrainedWidth + "px";
    panel.style.height = constrainedHeight + "px";
  });

  document.addEventListener("mouseup", () => {
    isResizing = false;
  });

  // Handle iframe load errors
  iframe.addEventListener("error", () => {
    const widgetUrl = buildIframeUrl();
    console.error('Failed to load chat widget iframe from:', widgetUrl);
    panel.innerHTML = `<div class="error">Unable to load chat widget. Please check if the server is running at ${widgetUrl}</div>`;
  });

  // Handle iframe load success
  iframe.addEventListener("load", () => {
    const widgetUrl = buildIframeUrl();
    console.log('Chat widget iframe loaded successfully from:', widgetUrl);
  });

})();
