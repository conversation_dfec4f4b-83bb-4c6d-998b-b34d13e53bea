<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Widget Test</title>
</head>
<body>
    <h1>Chat Widget Test</h1>
    <p>This page tests the embeddable chat widget using an iframe to localhost:5173/sifive/widget.</p>
    
    <!-- Chat Widget Script -->
    <!-- <script defer 
        src="https://d3uqx3zjobx6e7.cloudfront.net/chat-widget.js"
        data-title="SiFive Chat"
        data-sub-title="Ask anything about SiFive products and services"
        data-primary-color="#f64813"
        data-placeholder="Ask me anything..."
        data-position="bottom-right"
        data-max-height="520"
        data-domain="test.com"
        data-id="sifive"
        >
    </script> -->

    <script defer 
    src="chat-widget.js"
    data-title="SiFive Chat"
    data-sub-title="Ask anything about SiFive products and services"
    data-primary-color="#f64813"
    data-placeholder="Ask me anything..."
    data-position="bottom-right"
    data-max-height="520"
    data-domain="sifive.com"
    data-id="sifive"
    data-base-url="http://localhost:5173"
    >
    </script>
    
    <div style="margin-top: 2rem; padding: 1rem; background: #f0f0f0; border-radius: 8px;"> 
        <h3>Usage:</h3>
        <p>Simply include the script tag with optional customization:</p>
        <pre><code>&lt;script src="chat-widget.js"&gt;&lt;/script&gt;</code></pre>
        
        <h3>✨ Configuration Parameters:</h3>
        <p>You can customize the iframe content by passing parameters:</p>
        <pre><code>&lt;script 
    src="chat-widget.js"
    data-title="SiFive Chat"
    data-sub-title="Ask anything about SiFive products and services"
    data-primary-color="#f64813"
    data-placeholder="Ask me anything..."
    data-domain="mycompany.com"
    data-position="bottom-left"
    data-widget-url="http://localhost:5173/chat/widget"&gt;
&lt;/script&gt;</code></pre>
        
        <h4>Available Parameters:</h4>
        <ul>
            <li><strong>data-title</strong>: Main title shown in the chat header</li>
            <li><strong>data-sub-title</strong>: Subtitle/description text</li>
            <li><strong>data-primary-color</strong>: Primary color for branding (hex code)</li>
            <li><strong>data-placeholder</strong>: Placeholder text in the chat input field</li>
            <li><strong>data-domain</strong>: Domain identifier for analytics</li>
            <li><strong>data-position</strong>: Widget position (bottom-right, bottom-left)</li>
            <li><strong>data-max-height</strong>: Maximum height of the chat panel</li>
            <li><strong>data-widget-url</strong>: URL of the React app to load in iframe</li>
        </ul>
    </div>
</body>
</html>
