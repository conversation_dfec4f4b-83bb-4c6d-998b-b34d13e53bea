NODE_ENV=development
ENVIRONMENT=test
DATABASE_URL=postgresql://root:strongPASS@localhost:5432
POSTGRES_USER=root
POSTGRES_PASSWORD=strongPASS
ADMIN_USER=<EMAIL>
ADMIN_PASS=strongPASSWORD9@!
JWT_SECRET=do-not-use-this-secret-in-production
NOREPLY_EMAIL=<EMAIL>
REDIS_HOST=localhost
REDIS_PORT=6379

TWILIO_ACCOUNT_SID=AC-this-has-to-be-defined
TWILIO_AUTH_TOKEN=this-has-to-be-defined

OPENAI_API_KEY=

HUBSPOT_ACCESS_TOKEN=

S3_INGEST=placeholder
AWS_ACCESS_KEY=
AWS_SECRET_KEY=
