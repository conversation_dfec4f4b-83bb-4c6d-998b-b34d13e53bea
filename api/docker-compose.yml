version: "3.8"

services:
    db:
        image: pgvector/pgvector:pg17
        container_name: postgres
        env_file:
            - .env
        environment:
            POSTGRES_USER: ${POSTGRES_USER}
            POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
        ports:
            - "5432:5432"
        volumes:
            - agents_postgres_data:/var/lib/postgresql/data

    redis:
        image: redis:latest
        container_name: redis
        ports:
            - "6379:6379"
        command: ["redis-server", "--appendonly", "yes"]
        volumes:
            - agents_redis_data:/data

volumes:
    agents_postgres_data:
    agents_redis_data:
        driver: local
