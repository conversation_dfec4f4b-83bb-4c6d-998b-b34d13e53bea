import { Injectable } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

@Injectable()
export class GroupsService {
    constructor(private readonly prisma: PrismaService) {}

    async getGroups(tenantId: string) {
        return this.prisma.group.findMany({
            where: { tenantId },
            orderBy: { name: "asc" },
        });
    }

    async createGroup(
        tenantId: string,
        data: { name: string; description?: string },
    ) {
        return this.prisma.group.create({
            data: {
                ...data,
                tenantId,
            },
        });
    }
}
