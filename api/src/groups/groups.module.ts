import { <PERSON>du<PERSON> } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import { TenantsModule } from "../tenants/tenants.module";
import { GroupsController } from "./groups.controller";
import { GroupsService } from "./groups.service";

@Module({
    imports: [TenantsModule],
    providers: [GroupsService, PrismaService],
    exports: [GroupsService],
    controllers: [GroupsController],
})
export class GroupsModule {}
