import { Controller, Get, Post, Body, UseGuards } from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBearerAuth,
} from "@nestjs/swagger";

import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import { Tenant } from "../tenants/tenant.decorator";
import { TenantGuard } from "../tenants/tenant.guard";
import { GroupsService } from "./groups.service";

type Group = {
    id: number;
    name: string;
    description: string | null;
    createdAt: Date;
    updatedAt: Date;
    tenantId: string;
};

@ApiTags("groups")
@Controller("groups")
@UseGuards(JwtAuthGuard, TenantGuard)
@ApiBearerAuth()
export class GroupsController {
    constructor(private readonly groupsService: GroupsService) {}

    @Get()
    @ApiOperation({ summary: "Get all groups for the current tenant" })
    @ApiResponse({ status: 200, description: "List of groups" })
    getGroups(@Tenant("id") tenantId: string): Promise<Group[]> {
        return this.groupsService.getGroups(tenantId);
    }

    @Post()
    @ApiOperation({ summary: "Create a new group" })
    @ApiResponse({ status: 201, description: "Group created successfully" })
    createGroup(
        @Tenant("id") tenantId: string,
        @Body() createGroupDto: { name: string; description?: string },
    ): Promise<Group> {
        return this.groupsService.createGroup(tenantId, createGroupDto);
    }
}
