import { ConsoleLogger } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { NestExpressApplication } from "@nestjs/platform-express";

import { setupApp } from "./app-config";
import { AppModule } from "./app.module";
import { requestLoggerMiddleware } from "./common/middleware/request-logger.middleware";

async function bootstrap() {
    const logger = new ConsoleLogger({
        json: true,
        colors: process.env.NODE_ENV === "production" ? false : true,
    });

    const app: NestExpressApplication = await NestFactory.create(AppModule, {
        logger,
    });

    app.use(requestLoggerMiddleware(logger));
    await setupApp(app);
}

bootstrap();
