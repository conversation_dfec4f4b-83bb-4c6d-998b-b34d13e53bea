import { Injectable } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import { TwilioApiService } from "./../twilio-api/twilio-api.service";

@Injectable()
export class PhoneNumbersService {
    constructor(
        private readonly prismaService: PrismaService,
        private readonly twilioApiService: TwilioApiService,
    ) {}

    private async getActiveTwilioPhoneNumbers() {
        return this.twilioApiService.getActiveNumbers();
    }

    private async getAssignedAgentNumbers() {
        return this.prismaService.agent.findMany({
            select: { phoneNumber: true },
            where: {
                phoneNumber: {
                    not: {
                        equals: undefined,
                    },
                },
            },
        });
    }

    async findAll(hostName: string) {
        const activeNumbers = await this.getActiveTwilioPhoneNumbers();
        const agents = await this.getAssignedAgentNumbers();

        const assignedNumbers = new Set(
            agents.map(({ phoneNumber }) => phoneNumber),
        );

        const phoneNumbers = activeNumbers
            .filter(({ voiceUrl }) => new URL(voiceUrl).hostname === hostName)
            .map(({ phoneNumber }) => ({
                phoneNumber,
                isInUse: assignedNumbers.has(phoneNumber),
            }));

        return phoneNumbers;
    }

    async findOne(phoneNumber: string) {
        return this.prismaService.agent.findUniqueOrThrow({
            where: {
                type: "PHONE",
                phoneNumber,
            },
        });
    }
}
