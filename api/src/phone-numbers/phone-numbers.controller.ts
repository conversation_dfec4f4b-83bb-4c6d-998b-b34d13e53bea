import { Controller, Get, Req } from "@nestjs/common";
import { ApiResponse, ApiTags } from "@nestjs/swagger";

import { Request } from "express";

import { GetPhoneNumbersResponseDto } from "./dto/get-phone-numbers.dto";
import { PhoneNumbersService } from "./phone-numbers.service";

@Controller("phone-numbers")
@ApiTags("agents")
export class PhoneNumbersController {
    constructor(private readonly phoneNumbersService: PhoneNumbersService) {}

    @Get()
    @ApiResponse({ type: GetPhoneNumbersResponseDto })
    async findAll(@Req() req: Request): Promise<GetPhoneNumbersResponseDto> {
        const phoneNumbers = await this.phoneNumbersService.findAll(
            req.hostname,
        );

        return {
            items: phoneNumbers,
        };
    }
}
