import { Modu<PERSON> } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import { TwilioApiModule } from "../twilio-api/twilio-api.module";
import { PhoneNumbersController } from "./phone-numbers.controller";
import { PhoneNumbersService } from "./phone-numbers.service";

@Module({
    providers: [PhoneNumbersService, PrismaService],
    controllers: [PhoneNumbersController],
    imports: [TwilioApiModule],
    exports: [PhoneNumbersService],
})
export class PhoneNumbersModule {}
