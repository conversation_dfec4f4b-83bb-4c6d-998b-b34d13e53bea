import { ApiProperty } from "@nestjs/swagger";

export class ChatMessageDto {
    @ApiProperty()
    id: string;

    @ApiProperty({ enum: ["user", "assistant"] })
    role: "user" | "assistant";

    @ApiProperty()
    text: string;

    @ApiProperty()
    createdAt: string;
}

export class SessionInfoDto {
    @ApiProperty()
    id: string;

    @ApiProperty()
    agentId: string;

    @ApiProperty()
    agentName: string;

    @ApiProperty()
    agentDescription: string;

    @ApiProperty()
    createdAt: string;

    @ApiProperty()
    endedAt: string;

    @ApiProperty()
    resolved: boolean;
}

export class GetSessionMessagesResponseDto {
    @ApiProperty()
    session: SessionInfoDto;

    @ApiProperty({ type: [ChatMessageDto], isArray: true })
    messages: ChatMessageDto[];
}
