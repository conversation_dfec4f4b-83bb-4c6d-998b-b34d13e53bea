import { ApiProperty } from "@nestjs/swagger";

export class ConversationSessionDto {
    @ApiProperty()
    id: string;

    @ApiProperty()
    date: string;

    @ApiProperty()
    lastMessage: string;

    @ApiProperty()
    agentId: string;

    @ApiProperty()
    agentName: string;

    @ApiProperty()
    resolved: boolean;

    @ApiProperty()
    endedAt: string;
}

export class GetConversationSessionsResponseDto {
    @ApiProperty({ type: [ConversationSessionDto], isArray: true })
    items: ConversationSessionDto[];
}
