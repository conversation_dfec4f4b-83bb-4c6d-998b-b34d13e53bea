import { BullModule } from "@nestjs/bullmq";
import { Modu<PERSON> } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";
import { PrismaService } from "nestjs-prisma";

import { AgentsModule } from "../agents/agents.module";
import { AuthModule } from "../auth/auth.module";
import { jwtConstants } from "../auth/constants";
import { HubspotModule } from "../hubspot/hubspot.module";
import { OpenaiModule } from "../openai/openai.module";
import { TenantsModule } from "../tenants/tenants.module";
import { ChatAuthGateway } from "./chat-auth.gateway";
import { ChatController } from "./chat.controller";
import { ChatGateway } from "./chat.gateway";
import { ChatService } from "./chat.service";

@Module({
    imports: [
        AgentsModule,
        AuthModule,
        BullBoardModule.forFeature({
            name: "Chat agent events",
            adapter: BullMQAdapter,
        }),
        BullModule.registerQueue({
            name: "Chat agent events",
        }),
        HubspotModule,
        JwtModule.register({
            secret: jwtConstants.secret,
            signOptions: { expiresIn: "15m" },
        }),
        OpenaiModule,
        TenantsModule,
    ],
    controllers: [ChatController],
    providers: [ChatAuthGateway, ChatGateway, ChatService, PrismaService],
})
export class ChatModule {}
