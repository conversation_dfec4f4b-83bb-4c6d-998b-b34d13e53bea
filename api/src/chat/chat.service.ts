import { Injectable } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

@Injectable()
export class ChatService {
    constructor(private readonly prisma: PrismaService) {}

    async logMessage(
        connectionId: string,
        agentId: string,
        sender: "USER" | "AI",
        content: string,
    ) {
        // Check if session exists
        let session = await this.prisma.conversationSession.findUnique({
            where: { id: connectionId },
        });

        // If no session exists, create one
        session = await this.prisma.conversationSession.upsert({
            where: {
                id: connectionId,
            },
            create: {
                id: connectionId,
                agentId: agentId,
            },
            update: {},
        });

        // Log the message
        await this.prisma.conversationMessage.create({
            data: {
                sessionId: session.id,
                sender,
                content,
            },
        });
    }

    async endSession(connectionId: string) {
        await this.prisma.conversationSession.updateMany({
            where: { id: connectionId },
            data: { endedAt: new Date() },
        });
    }

    async markSessionResolved(connectionId: string) {
        await this.prisma.conversationSession.update({
            where: { id: connectionId },
            data: { resolved: true },
        });
    }

    async markSessionUnresolved(connectionId: string) {
        await this.prisma.conversationSession.update({
            where: { id: connectionId },
            data: { resolved: false },
        });
    }

    async getConversationSessions(
        tenantId: string,
        agentId?: string,
        limit: number = 50,
    ) {
        const sessions = await this.prisma.conversationSession.findMany({
            where: {
                ...(agentId && { agentId }),
                endedAt: { not: null }, // Only completed sessions
                agent: {
                    tenantId,
                },
            },
            include: {
                agent: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
                messages: {
                    orderBy: { createdAt: "desc" },
                    take: 1, // Get only the last message
                },
            },
            orderBy: { createdAt: "desc" },
            take: limit,
        });

        return sessions.map((session) => ({
            id: session.id,
            date: session.createdAt.toISOString(),
            lastMessage: session.messages[0]?.content || "No messages",
            agentId: session.agent.id,
            agentName: session.agent.name,
            resolved: session.resolved,
            endedAt: session.endedAt?.toISOString(),
        }));
    }

    async getSessionMessages(tenantId: string, sessionId: string) {
        const session = await this.prisma.conversationSession.findFirst({
            where: {
                id: sessionId,
                agent: {
                    tenantId,
                },
            },
            include: {
                agent: {
                    select: {
                        id: true,
                        name: true,
                        description: true,
                    },
                },
                messages: {
                    orderBy: { createdAt: "asc" },
                },
            },
        });

        if (!session) {
            throw new Error("Session not found");
        }

        return {
            session: {
                id: session.id,
                agentId: session.agent.id,
                agentName: session.agent.name,
                agentDescription: session.agent.description,
                createdAt: session.createdAt.toISOString(),
                endedAt: session.endedAt?.toISOString(),
                resolved: session.resolved,
            },
            messages: session.messages.map((message) => ({
                id: message.id,
                role: (message.sender === "USER" ? "user" : "assistant") as
                    | "user"
                    | "assistant",
                text: message.content,
                createdAt: message.createdAt.toISOString(),
            })),
        };
    }
}
