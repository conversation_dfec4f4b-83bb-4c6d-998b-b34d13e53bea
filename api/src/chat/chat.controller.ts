import { Controller, Get, Param, Query, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Tenant } from "../tenants/tenant.decorator";
import { TenantGuard } from "../tenants/tenant.guard";
import { ChatService } from "./chat.service";
import { GetConversationSessionsResponseDto } from "./dto/get-conversation-sessions.dto";
import { GetSessionMessagesResponseDto } from "./dto/get-session-messages.dto";

@Controller("chat")
@ApiTags("chat")
@UseGuards(TenantGuard)
export class ChatController {
    constructor(private readonly chatService: ChatService) {}

    @Get("sessions")
    @ApiOperation({ summary: "Get conversation sessions" })
    @ApiResponse({ status: 200, type: GetConversationSessionsResponseDto })
    async getConversationSessions(
        @Tenant("id") tenantId: string,
        @Query("agentId") agentId?: string,
        @Query("limit") limit?: string,
    ): Promise<GetConversationSessionsResponseDto> {
        const sessions = await this.chatService.getConversationSessions(
            tenantId,
            agentId,
            limit ? parseInt(limit, 10) : 50,
        );

        return { items: sessions };
    }

    @Get("sessions/:sessionId/messages")
    @ApiOperation({ summary: "Get messages for a specific session" })
    @ApiResponse({ status: 200, type: GetSessionMessagesResponseDto })
    async getSessionMessages(
        @Tenant("id") tenantId: string,
        @Param("sessionId") sessionId: string,
    ): Promise<GetSessionMessagesResponseDto> {
        return this.chatService.getSessionMessages(tenantId, sessionId);
    }
}
