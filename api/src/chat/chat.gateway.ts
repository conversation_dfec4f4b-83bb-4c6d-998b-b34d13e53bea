import { InjectQueue } from "@nestjs/bullmq";
import { BadRequestException, Logger } from "@nestjs/common";
import {
    ConnectedSocket,
    MessageBody,
    OnGatewayConnection,
    OnGatewayDisconnect,
    SubscribeMessage,
    WebSocketGateway,
    WebSocketServer,
} from "@nestjs/websockets";

import { Queue } from "bullmq";
import { randomUUID } from "crypto";
import {
    ResponseOutputItemDoneEvent,
    ResponseTextDeltaEvent,
    ResponseTextDoneEvent,
    SessionUpdateEvent,
} from "openai/resources/beta/realtime/realtime";
import type { WebSocket as WsWebSocket } from "ws";
import { Server } from "ws";
import { z } from "zod";

import { AgentsService } from "../agents/agents.service";
import { HubspotService } from "../hubspot/hubspot.service";
import { OpenaiService } from "../openai/openai.service";
import { getAgentSpecificTools } from "../openai/util";
import { ChatService } from "./chat.service";
import { RealtimeChat } from "./entity/realtime-chat";
import { UserMessages } from "./types";
import { getSystemInstruction } from "./util";
import { getWebSocketId, setWebSocketId } from "./websocket-helpers";

@WebSocketGateway({ path: "/api/chat" })
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private logger = new Logger("ChatGateway");

    @WebSocketServer()
    private server: Server;

    private chats: Record<string, RealtimeChat> = {};

    constructor(
        private readonly agentsService: AgentsService,
        private readonly openaiService: OpenaiService,
        private readonly chatService: ChatService,
        @InjectQueue("Chat agent events") private agentEventQueue: Queue,
        private readonly hubspotService: HubspotService,
    ) {}

    async handleConnection(
        websocket: WsWebSocket,
        {
            url,
            headers,
        }: {
            url: string;
            headers: Record<string, string | string[] | undefined>;
        },
    ) {
        // Check origin header for domain restrictions (including subdomains)
        const origin = headers.origin || headers.Origin;
        const allowedDomains = [
            "truthkeep.ai",
            "sifive.com",
            "safran-group.com",
            ...(process.env.NODE_ENV === "development" ? ["localhost"] : []),
        ];

        if (origin) {
            const originString = Array.isArray(origin) ? origin[0] : origin;
            const originUrl = new URL(originString);
            const hostname = originUrl.hostname;

            const isAllowed = allowedDomains.some((domain) => {
                // Exact match
                if (hostname === domain) return true;
                // Subdomain match
                if (hostname.endsWith("." + domain)) return true;
                // Localhost with port
                if (domain === "localhost" && hostname.startsWith("localhost"))
                    return true;
                return false;
            });

            if (!isAllowed) {
                this.logger.warn(
                    `Blocked connection from unauthorized origin: ${origin}`,
                );
                websocket.close(1008, "Unauthorized origin");
                return;
            }
        }

        const agentId = new URLSearchParams(url.split("?")[1]).get("agentId");
        if (!agentId) throw new BadRequestException("Agent ID is required");

        let agent;
        try {
            agent = await this.agentsService.getAgent(agentId);
        } catch (error) {
            if (error.status === 404) {
                // Check if this is a private agent by ID pattern
                const isPrivateAgent = agentId.includes("internal");

                // Block private agents immediately - don't create fallback
                if (isPrivateAgent) {
                    throw new BadRequestException(
                        "This agent is not available through the public widget. Private agents require authentication.",
                    );
                }

                // Only create fallback for public agents
                agent = {
                    id: agentId,
                    name: "Chat Support",
                    description: "We're here to help!",
                    type: "CHAT",
                    isPrivate: false, // Widget fallbacks are always public
                    events: [],
                };
            } else {
                throw error;
            }
        }

        // Only allow public agents on this public endpoint
        if (agent.isPrivate) {
            throw new BadRequestException(
                "This endpoint is only for public agents. Use /api/chat/auth for private agents.",
            );
        }

        const chatId = randomUUID();
        setWebSocketId(websocket, chatId);

        this.chats[chatId] = {
            socket: websocket,
            agent,
            userHasSentMessage: false,
        };

        this.logger.log(`Client connection ${chatId} for agent ${agent.id}`);
        this.openaiService.openWebSocket(chatId);

        // Send initial session update
        // Send initial prompt
        const systemInstruction = getSystemInstruction(agent);
        const systemInstructionSnippet =
            systemInstruction.length > 100
                ? `${systemInstruction.slice(0, 100)}...`
                : systemInstruction;
        const hasEmbeddings = await this.agentsService.hasEmbeddings(agent.id);
        const tools: SessionUpdateEvent.Session.Tool[] = getAgentSpecificTools(
            agent.id,
            agent.events,
            hasEmbeddings,
        );

        this.logger.log(
            `Using system instruction: "${systemInstructionSnippet}"`,
        );
        this.logger.log(`Using tools: ${JSON.stringify(tools)}`);

        this.openaiService.sendInitialTextConversationMessage(chatId, {
            instructions: systemInstruction,
            tools,
            agentId: agent.id,
        });

        this.openaiService.listenTextDelta(chatId, (event) => {
            this.handleTextDelta(websocket, event);
        });
        this.openaiService.listenForTextDone(chatId, (event) => {
            this.handleTextDone(websocket, event);
        });

        // Handle tool calls
        this.openaiService.listenForResponseOutputItemDone(chatId, (event) =>
            this.handleResponseOutputItemDone(chatId, event),
        );
    }

    handleDisconnect(websocket: WsWebSocket) {
        const chatId = getWebSocketId(websocket);
        if (!chatId) {
            return;
        }

        this.logger.log(`Client disconnect ${chatId}`);
        this.openaiService.closeWebSocket(chatId);
        this.chatService.endSession(chatId);
        delete this.chats[chatId];
    }

    @SubscribeMessage("messages")
    async handleMessages(
        @ConnectedSocket() websocket: WsWebSocket,
        @MessageBody() messages: string,
    ) {
        const userMessages = UserMessages.parse(messages);

        const chatId = getWebSocketId(websocket);

        if (!chatId) {
            this.logger.warn("Received messages for unidentified websocket");
            return;
        }

        // Check if chat still exists
        if (!this.chats[chatId]) {
            this.logger.warn(
                `Chat ${chatId} no longer exists, ignoring messages`,
            );
            return;
        }

        const agentId = this.chats[chatId]?.agent.id;

        this.chats[chatId].userHasSentMessage = true;

        for (const message of userMessages) {
            switch (message.role) {
                case "system":
                    this.logger.log(
                        `Recieved sales lead form message "${JSON.stringify(message.data)}"`,
                    );
                    await this.hubspotService
                        .peristConversation(chatId, message.data)
                        .then(() =>
                            this.openaiService.sendSystemMessage(
                                chatId,
                                "The user's contact information has been successfully submitted. Let them know that we will reach out to them shortly.",
                            ),
                        )
                        .catch(() =>
                            this.openaiService.sendSystemMessage(
                                chatId,
                                "There was an error submitting the user's contact information. Ask them to please try again later.",
                            ),
                        );
                    break;

                case "user":
                    this.logger.log(`Recieved message "${message.text}"`);
                    this.openaiService.sendText(chatId, message.text);
                    await this.chatService.logMessage(
                        chatId,
                        agentId,
                        "USER",
                        message.text,
                    );
                    break;
                default:
                    throw new BadRequestException("Invalid message role");
            }
        }

        this.openaiService.sendResponseCreate(chatId);
    }

    handleTextDelta(websocket: WsWebSocket, event: ResponseTextDeltaEvent) {
        this.logger.log(`Recieved text delta "${event.delta}"`);
        const chatId = getWebSocketId(websocket);

        // Check if WebSocket is still open before sending
        if (websocket.readyState === 1) {
            // WebSocket.OPEN = 1
            websocket.send(JSON.stringify({ text: event.delta }));
        } else {
            this.logger.warn(
                `WebSocket ${chatId ?? "unknown"} is closed, skipping text delta send`,
            );
        }
    }

    handleTextDone(websocket: WsWebSocket, event: ResponseTextDoneEvent) {
        this.logger.log(`Recieved text done "${event.text}"`);

        const chatId = getWebSocketId(websocket);
        if (!chatId) {
            this.logger.warn("Received text done for unidentified websocket");
            return;
        }
        const agentId = this.chats[chatId]?.agent.id;

        const responseMessage = {
            text: event.text,
        };

        if (this.chats[chatId]?.userHasSentMessage) {
            this.chatService.logMessage(
                chatId,
                agentId,
                "AI",
                responseMessage.text,
            );
        }

        // Check if WebSocket is still open before sending
        if (websocket.readyState === 1) {
            // WebSocket.OPEN = 1
            websocket.send(JSON.stringify({ text: "END" }));
        } else {
            this.logger.warn(
                `WebSocket ${chatId} is closed, skipping text done send`,
            );
        }
    }

    async handleResponseOutputItemDone(
        chatId: string,
        event: ResponseOutputItemDoneEvent,
    ) {
        this.logger.log(`Handling ${event.type}`);

        // Check if chat still exists (hasn't been disconnected)
        if (!this.chats[chatId]) {
            this.logger.warn(
                `Chat ${chatId} no longer exists, skipping tool call handling`,
            );
            return;
        }

        const { name } = event.item;

        if (name === "lookup") return this.handleLookup(chatId, event);
        if (name === "resolve") return this.handleResolve(chatId, event);
        if (name === "unresolve") return this.handleUnresolve(chatId, event);
        if (name === "resolve_to_sales")
            return this.handleResolveToSales(chatId, event);

        // Create a BullMQ job to handle the event
        const formattedEventName = `${name[0].toUpperCase()}${name.slice(1).replaceAll("_", " ")}`;

        const { agent } = this.chats[chatId];

        const jobData = {
            chatId,
            event: event.item.name,
            agent: agent.name,
        };

        this.logger.log({ formattedEventName, jobData });
        const job = await this.agentEventQueue.add(formattedEventName, jobData);
        this.logger.log(`Job ${job.id} added to queue`);

        this.openaiService.sendGenericToolResponse(chatId, event.item.call_id);
    }

    private async handleLookup(
        chatId: string,
        event: ResponseOutputItemDoneEvent,
    ) {
        this.logger.log(event);

        // Check if chat still exists and WebSocket is open
        if (!this.chats[chatId]) {
            this.logger.warn(
                `Chat ${chatId} no longer exists, skipping lookup`,
            );
            return;
        }

        const websocket = this.chats[chatId].socket;
        if (websocket.readyState !== 1) {
            // WebSocket.OPEN = 1
            this.logger.warn(`WebSocket ${chatId} is closed, skipping lookup`);
            return;
        }

        await this.chatService.logMessage(
            chatId,
            this.chats[chatId].agent.id,
            "AI",
            "Toolcall: lookup",
        );

        // Send analysis preview message to user
        websocket.send(
            JSON.stringify({
                text: `Searching knowledge base...`,
            }),
        );

        const schema = z.object({ question: z.string() });
        let toolCallArguments: z.infer<typeof schema>;
        try {
            toolCallArguments = schema.parse(JSON.parse(event.item.arguments));
        } catch (error) {
            this.logger.error(error);
            return;
        }
        if (!toolCallArguments.question) {
            this.logger.error('Required field "question" missing');
            return;
        }
        const { question } = toolCallArguments;

        const embedding = await this.openaiService.getEmbedding(
            toolCallArguments.question,
            "text-embedding-3-small",
        );

        const agentId = this.chats[chatId].agent.id;

        const searchableEntities = await this.agentsService.semanticSearch(
            agentId,
            embedding.data[0],
            150,
        );
        this.logger.log(
            `Semantic search for "${searchableEntities.length}" entities`,
        );

        // Clean the question for text search by removing special characters
        const cleanedQuestion = question
            .replace(/[^\w\s]/g, " ")
            .replace(/\s+/g, " ")
            .trim();

        const textSearchableEntities = await this.agentsService.textSearch(
            agentId,
            cleanedQuestion,
            150,
        );
        this.logger.log(
            `Text search for "${textSearchableEntities.length}" entities`,
        );

        const allSearchableEntities = [
            ...searchableEntities,
            ...textSearchableEntities,
        ];

        if (!allSearchableEntities.length) {
            this.logger.log(
                `Found no embedded content to respond to "${question}"`,
            );
            // TODO respond anyway to say nothing was found?
            return;
        }

        // Check WebSocket state again before sending thinking message
        if (websocket.readyState === 1) {
            // WebSocket.OPEN = 1
            websocket.send(
                JSON.stringify({
                    text: `\n\nThinking...`,
                }),
            );
        }

        const rerankedSearchableEntities =
            await this.openaiService.rerankSearchableEntities(
                allSearchableEntities,
                question,
                20,
            );
        this.logger.log(
            `Reranked search for "${rerankedSearchableEntities.length}" entities`,
        );

        // Increment reference counts for the top 20 results
        const entityIds = rerankedSearchableEntities.map((entity) => entity.id);
        await this.agentsService.incrementReferenceCounts(entityIds);

        // Send response to tool call
        this.logger.log(
            `Responding to query "${question}" with embedded content "${rerankedSearchableEntities.map((entity) => entity.embedded_content).join(", ")}"`,
        );

        // Send citation message to user
        const uniqueFileNames = [
            ...new Set(
                rerankedSearchableEntities.map((entity) => entity.file_name),
            ),
        ];
        const fileLinks = uniqueFileNames
            .map((fileName) => {
                // Find the first entity with this file name to get page number
                const entity = rerankedSearchableEntities.find(
                    (e) => e.file_name === fileName,
                );
                const pageInfo = entity?.page_number
                    ? ` (page ${entity.page_number})`
                    : "";
                return `[${fileName}${pageInfo}](#)`;
            })
            .join("  \n");

        // Check WebSocket state before sending final messages
        if (websocket.readyState === 1) {
            // WebSocket.OPEN = 1
            websocket.send(
                JSON.stringify({
                    text: `Analyzing:\n\n**${fileLinks}**`,
                    overwrite: true,
                }),
            );
            websocket.send(JSON.stringify({ text: "END", overwrite: true }));
        }

        // Send all entities to the tool call response
        const toolCallOutput = JSON.stringify(
            rerankedSearchableEntities.map((entity) => ({
                file_name: entity.file_name,
                page_number: entity.page_number,
                embedded_content: entity.embedded_content,
                similarity: entity.similarity,
            })),
        );

        this.openaiService.sendLookupToolCallResponse(
            chatId,
            event.item.call_id,
            toolCallOutput,
        );
    }

    private async handleResolve(
        chatId: string,
        event: ResponseOutputItemDoneEvent,
    ) {
        try {
            this.logger.log(event);

            // Check if chat still exists
            if (!this.chats[chatId]) {
                this.logger.warn(
                    `Chat ${chatId} no longer exists, skipping resolve`,
                );
                return;
            }

            await this.chatService.markSessionResolved(chatId);
            await this.chatService.logMessage(
                chatId,
                this.chats[chatId].agent.id,
                "AI",
                "Toolcall: resolve",
            );
            await this.openaiService.sendResolveToolCallResponse(
                chatId,
                event.item.call_id,
            );
        } catch (error) {
            // Log error but don't throw - we don't want to break the chat flow
            this.logger.error("Error handling resolve:", error);
        }
    }

    private async handleUnresolve(
        chatId: string,
        event: ResponseOutputItemDoneEvent,
    ) {
        try {
            this.logger.log(event);

            // Check if chat still exists
            if (!this.chats[chatId]) {
                this.logger.warn(
                    `Chat ${chatId} no longer exists, skipping unresolve`,
                );
                return;
            }

            await this.chatService.markSessionUnresolved(chatId);
            await this.chatService.logMessage(
                chatId,
                this.chats[chatId].agent.id,
                "AI",
                "Toolcall: unresolve",
            );
            await this.openaiService.sendUnresolveToolCallResponse(
                chatId,
                event.item.call_id,
                this.chats[chatId].agent.unresolvedUrl || "",
            );
        } catch (error) {
            // Log error but don't throw - we don't want to break the chat flow
            this.logger.error("Error handling unresolve:", error);
        }
    }

    private async handleResolveToSales(
        chatId: string,
        event: ResponseOutputItemDoneEvent,
    ) {
        try {
            this.logger.log(event);

            // Check if chat still exists
            if (!this.chats[chatId]) {
                this.logger.warn(
                    `Chat ${chatId} no longer exists, skipping resolve to sales`,
                );
                return;
            }

            await this.chatService.markSessionResolved(chatId);
            await this.chatService.logMessage(
                chatId,
                this.chats[chatId].agent.id,
                "AI",
                "Toolcall: resolve to sales",
            );

            await this.openaiService.sendResolveToSalesToolCallResponse(
                chatId,
                event.item.call_id,
            );
        } catch (error) {
            this.logger.error("Error handling resolve to sales:", error);
        }
    }
}
