import { Agent } from "@prisma/client";

export function getSystemInstruction<
    T extends Pick<Agent, "description"> & {
        tenant: { name: string; systemInstructionTemplate?: string | null };
    },
>(agent: T): string {
    if (!agent) return "";

    const descriptionInstruction = getDescriptionInstructions(
        agent.description,
    );

    // Use tenant's template if available, otherwise fall back to default
    const template =
        agent.tenant.systemInstructionTemplate ||
        `You are <PERSON>, {{tenantName}}'s AI assistant. {{tenantName}} is a leading company serving customers with innovative solutions.

Core identity:
- Role: {{agentDescription}}
- Speak as "we/our company" with internal knowledge
- Professional, friendly tone representing {{tenantName}}'s interests

CRITICAL: Remember to use toolcalls to respond. Search company knowledge, check documentation`;

    return template
        .replace(/\{\{tenantName\}\}/g, agent.tenant.name)
        .replace(/\{\{agentDescription\}\}/g, descriptionInstruction);
}

function getDescriptionInstructions(description?: string): string {
    if (!description) return "";

    return `You are described as "${description}"`;
}
