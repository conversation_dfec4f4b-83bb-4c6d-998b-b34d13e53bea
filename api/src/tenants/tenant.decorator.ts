import { createParamDecorator, ExecutionContext } from "@nestjs/common";

export const Tenant = createParamDecorator(
    (data: string, ctx: ExecutionContext) => {
        const request = ctx.switchToHttp().getRequest();
        const user = request.user;

        if (!user) {
            return null;
        }

        if (data === "id") {
            return user.tenantId;
        }

        if (data === "slug") {
            return user.tenantSlug;
        }

        // Return full tenant context if no specific field requested
        return {
            tenantId: user.tenantId,
            tenantSlug: user.tenantSlug,
        };
    },
);
