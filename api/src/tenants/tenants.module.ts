import { Modu<PERSON> } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import { TenantContextService } from "./tenant-context.service";
import { TenantGuard } from "./tenant.guard";
import { TenantsService } from "./tenants.service";

@Module({
    providers: [
        PrismaService,
        TenantContextService,
        TenantGuard,
        TenantsService,
    ],
    exports: [TenantContextService, TenantGuard, TenantsService],
})
export class TenantsModule {}
