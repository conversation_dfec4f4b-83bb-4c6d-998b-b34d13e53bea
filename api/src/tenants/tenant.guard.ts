import {
    Injectable,
    CanActivate,
    ExecutionContext,
    ForbiddenException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";

import { TenantContextService } from "./tenant-context.service";

@Injectable()
export class TenantGuard implements CanActivate {
    constructor(
        private reflector: Reflector,
        private tenantContext: TenantContextService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const user = request.user;

        if (!user || !user.tenantId) {
            throw new ForbiddenException("Tenant context required");
        }

        // Set tenant context for this request
        this.tenantContext.setTenantContext(user.tenantId, user.tenantSlug);

        // Add tenant context to request for use in services
        request.tenantId = user.tenantId;
        request.tenantSlug = user.tenantSlug;

        return true;
    }
}
