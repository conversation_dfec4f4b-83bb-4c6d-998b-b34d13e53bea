import { Injectable, Scope } from "@nestjs/common";

@Injectable({ scope: Scope.REQUEST })
export class TenantContextService {
    private tenantId: string | null = null;
    private tenantSlug: string | null = null;

    setTenantContext(tenantId: string, tenantSlug?: string): void {
        this.tenantId = tenantId;
        this.tenantSlug = tenantSlug || null;
    }

    getTenantId(): string | null {
        return this.tenantId;
    }

    getTenantSlug(): string | null {
        return this.tenantSlug;
    }

    getTenantContext(): { tenantId: string | null; tenantSlug: string | null } {
        return {
            tenantId: this.tenantId,
            tenantSlug: this.tenantSlug,
        };
    }

    clearTenantContext(): void {
        this.tenantId = null;
        this.tenantSlug = null;
    }
}
