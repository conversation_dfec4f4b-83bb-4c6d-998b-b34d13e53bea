import { Injectable } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

@Injectable()
export class TenantsService {
    constructor(private readonly prisma: PrismaService) {}

    async findAll() {
        return this.prisma.tenant.findMany({
            orderBy: { createdAt: "desc" },
        });
    }

    async findOne(id: string) {
        return this.prisma.tenant.findUnique({
            where: { id },
            include: {
                users: {
                    select: {
                        id: true,
                        username: true,
                        email: true,
                    },
                },
                agents: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                        isPrivate: true,
                    },
                },
                groups: {
                    select: {
                        id: true,
                        name: true,
                        description: true,
                    },
                },
                roles: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });
    }

    async findBySlug(slug: string) {
        return this.prisma.tenant.findUnique({
            where: { slug },
            include: {
                users: {
                    select: {
                        id: true,
                        username: true,
                        email: true,
                    },
                },
                agents: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                        isPrivate: true,
                    },
                },
                groups: {
                    select: {
                        id: true,
                        name: true,
                        description: true,
                    },
                },
                roles: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });
    }

    async getTenantStats(id: string) {
        const [userCount, agentCount, groupCount, roleCount] =
            await Promise.all([
                this.prisma.user.count({ where: { tenantId: id } }),
                this.prisma.agent.count({ where: { tenantId: id } }),
                this.prisma.group.count({ where: { tenantId: id } }),
                this.prisma.role.count({ where: { tenantId: id } }),
            ]);

        return {
            userCount,
            agentCount,
            groupCount,
            roleCount,
        };
    }
}
