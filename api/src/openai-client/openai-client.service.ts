import { Injectable, Logger } from "@nestjs/common";

import OpenAI from "openai";
import { OpenAIRealtimeWebSocket } from "openai/beta/realtime/websocket";
import { RealtimeClientEvent } from "openai/resources/beta/realtime/realtime";
import { EmbeddingModel } from "openai/resources/index";
import { Stream } from "openai/streaming";

@Injectable()
export class OpenaiClientService {
    private client: OpenAI;
    private logger = new Logger("OpenaiClientService");

    constructor() {
        this.client = new OpenAI({
            apiKey: process.env["OPENAI_API_KEY"],
            baseURL: process.env["OPENAI_BASE_URL"],
        });
    }

    openWebSocket(): OpenAIRealtimeWebSocket {
        this.logger.log("Opening OpenAI websocket");
        const socket = new OpenAIRealtimeWebSocket(
            { model: "gpt-4o-realtime-preview-2024-12-17" },
            this.client,
        );

        socket.on("error", (error) => this.logger.error(error));

        return socket;
    }

    closeWebSocket(socket: OpenAIRealtimeWebSocket) {
        this.logger.log("Closing OpenAI websocket");
        socket.close();
    }

    sendMessage(socket: OpenAIRealtimeWebSocket, message: RealtimeClientEvent) {
        const socketState = socket?.socket?.readyState;
        const socketReady = socketState === 1; // WebSocket.OPEN = 1
        if (!socketReady) return;

        socket.send(message);
    }

    listenForRealtimeServerEvent(
        socket: OpenAIRealtimeWebSocket,
        eventType: Parameters<OpenAIRealtimeWebSocket["on"]>[0],
        cb: Parameters<OpenAIRealtimeWebSocket["on"]>[1],
    ) {
        socket.on(eventType, cb);
    }

    async getEmbedding(
        input: string,
        model: EmbeddingModel,
        dimensions: number,
    ): Promise<OpenAI.Embeddings.CreateEmbeddingResponse> {
        return this.client.embeddings.create({
            input,
            model,
            dimensions,
        });
    }

    async createChatCompletion(
        params: OpenAI.Chat.ChatCompletionCreateParams,
    ): Promise<
        OpenAI.Chat.ChatCompletion | Stream<OpenAI.Chat.ChatCompletionChunk>
    > {
        return this.client.chat.completions.create(params);
    }
}
