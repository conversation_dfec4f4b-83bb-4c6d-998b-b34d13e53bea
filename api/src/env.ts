// https://www.jacobparis.com/content/type-safe-env
import { z, TypeOf } from "zod";

const zodEnv = z.object({
    NODE_ENV: z
        .enum(["development", "production", "test", "provision"])
        .default("development"),
    PORT: z.coerce.number().min(80).default(3000),
    DATABASE_URL: z.string(),
    ADMIN_USER: z.string(),
    ADMIN_PASS: z.string().min(8),
    JWT_SECRET: z.string().min(16),
    REDIS_HOST: z.string(),
    REDIS_PORT: z.coerce.number().min(1000),
    TWILIO_ACCOUNT_SID: z.string().startsWith("AC"),
    TWILIO_AUTH_TOKEN: z.string(),
    OPENAI_API_KEY: z.string().optional(),
    PRISMIC_REPO: z.string().optional(),
    PRISMIC_ACCESS_TOKEN: z.string().optional(),
    PRISMIC_WEBHOOK_SECRET: z.string().optional(),
    PRISMIC_TENANT_ID: z.string().optional(),
    PRISMIC_TENANT_SLUG: z.string().optional(),
    PRISMIC_HMAC_ALGORITHM: z.enum(["sha1", "sha256"]).default("sha256"),
    DAVID_INGEST_URL: z.string().url().optional(),
    DAVID_INGEST_API_KEY: z.string().optional(),
});

declare global {
    // eslint-disable-next-line @typescript-eslint/no-namespace
    namespace NodeJS {
        // eslint-disable-next-line @typescript-eslint/no-empty-object-type
        interface ProcessEnv extends TypeOf<typeof zodEnv> {}
    }
}
try {
    zodEnv.parse(process.env);
} catch (err) {
    if (err instanceof z.ZodError) {
        const { fieldErrors } = err.flatten();
        const errorMessage = Object.entries(fieldErrors)
            .map(([field, errors]) =>
                errors ? `${field}: ${errors.join(", ")}` : field,
            )
            .join("\n  ");
        throw new Error(`Missing environment variables:\n  ${errorMessage}`);
        process.exit(1);
    }
}
