import { Injectable } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import { RolesService } from "../roles/roles.service";

@Injectable()
export class UsersService {
    constructor(
        private prisma: PrismaService,
        private rolesService: RolesService,
    ) {}

    async findOne(usernameOrEmail: string) {
        return this.prisma.user.findFirst({
            where: {
                OR: [{ username: usernameOrEmail }, { email: usernameOrEmail }],
            },
            include: {
                tenant: true,
            },
        });
    }

    async findById(userId: string) {
        const user = await this.prisma.user.findUniqueOrThrow({
            select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
                tenantId: true,
                tenant: {
                    select: {
                        id: true,
                        name: true,
                        slug: true,
                    },
                },
                roles: {
                    include: {
                        role: true,
                    },
                },
            },
            where: {
                id: userId,
            },
        });

        return user;
    }
}
