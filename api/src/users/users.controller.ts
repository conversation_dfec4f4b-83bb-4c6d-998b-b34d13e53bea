import { <PERSON>, Get, Req } from "@nestjs/common";
import { ApiResponse, ApiTags } from "@nestjs/swagger";

import { Request } from "express";

import { Role } from "../roles/role.enum";
import { RolesService } from "../roles/roles.service";
import { UserDto } from "./dto/user.dto";
import { UsersService } from "./users.service";

@Controller("users")
@ApiTags("users")
export class UsersController {
    constructor(
        private readonly userService: UsersService,
        private readonly rolesService: RolesService,
    ) {}

    @Get("me")
    @ApiResponse({ type: UserDto })
    async getProfile(@Req() req: Request): Promise<UserDto> {
        const { roles, ...user } = await this.userService.findById(
            req.user["userId"],
        );
        const isSifivePilotUser = !!roles.find(
            ({ role }) => role.name === Role.SifivePilot,
        );

        return {
            ...user,
            isSifivePilotUser,
            groups: [],
        };
    }
}
