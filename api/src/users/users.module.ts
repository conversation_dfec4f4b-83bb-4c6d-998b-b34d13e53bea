import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { JwtModule, JwtService } from "@nestjs/jwt";

import { PrismaService } from "nestjs-prisma";

import { jwtConstants } from "../auth/constants";
import { RolesModule } from "../roles/roles.module";
import { UsersController } from "./users.controller";
import { UsersService } from "./users.service";

@Module({
    imports: [
        JwtModule.register({
            secret: jwtConstants.secret,
            signOptions: { expiresIn: "60s" },
        }),
        JwtModule,
        RolesModule,
    ],
    providers: [JwtService, PrismaService, UsersService],
    exports: [UsersService],
    controllers: [UsersController],
})
export class UsersModule {}
