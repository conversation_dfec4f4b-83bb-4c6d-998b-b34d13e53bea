import { Injectable, Logger } from "@nestjs/common";

import { Twi<PERSON> } from "twilio";

@Injectable()
export class TwilioApiService {
    private logger = new Logger("TwilioApiService");
    private twilio: Twilio;

    constructor() {
        try {
            this.twilio = new Twilio(
                process.env.TWILIO_ACCOUNT_SID,
                process.env.TWILIO_AUTH_TOKEN,
            );
        } catch (e) {
            this.logger.warn("Failed to create Twilio client", e);
        }
    }

    async getActiveNumbers() {
        this.logger.log("getActiveNumbers");
        try {
            return this.twilio.incomingPhoneNumbers.list();
        } catch (e) {
            this.logger.warn("Failed to fetch numbers from <PERSON><PERSON><PERSON>", e);
            return [];
        }
    }

    async updateCall(callSid: string, twiml: string) {
        await this.twilio.calls.get(callSid).update({ twiml });
    }
}
