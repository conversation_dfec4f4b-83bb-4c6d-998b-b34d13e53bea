import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import {
    IsString,
    IsOptional,
    IsN<PERSON>ber,
    Min,
    Max,
    IsBoolean,
} from "class-validator";

export class PresignedUrlRequestDto {
    @ApiProperty({
        description: "The file name/key for the S3 object",
        example: "uploads/document.pdf",
    })
    @IsString()
    fileName: string;

    @ApiPropertyOptional({
        description: "Whether the file should be public or private",
        example: false,
    })
    @IsOptional()
    @IsBoolean()
    public?: boolean = false;

    @ApiProperty({
        description: "Content type of the file (required for uploads)",
        example: "application/pdf",
    })
    @IsString()
    contentType: string;

    @ApiPropertyOptional({
        description:
            "URL expiration time in seconds (default: 3600, max: 86400)",
        example: 3600,
    })
    @IsOptional()
    @IsNumber()
    @Min(60)
    @Max(86400)
    expiresIn?: number;
}

export class PresignedUrlResponseDto {
    @ApiProperty({
        description: "The presigned URL for uploading to S3_INGEST bucket",
        example: "https://s3.amazonaws.com/bucket/key?X-Amz-Algorithm=...",
    })
    url: string;

    @ApiProperty({
        description: "The S3_INGEST bucket name",
        example: "my-ingest-bucket",
    })
    bucket: string;

    @ApiProperty({
        description: "The S3 object key",
        example: "uploads/document.pdf",
    })
    key: string;

    @ApiProperty({
        description: "The expiration timestamp of the URL",
        example: "2024-01-01T12:00:00.000Z",
    })
    expiresAt: string;

    @ApiProperty({
        description: "The operation type (always PUT for uploads)",
        example: "PUT",
    })
    operation: string;
}
