import { Injectable, InternalServerErrorException } from "@nestjs/common";

import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { PrismaService } from "nestjs-prisma";

import { UsersService } from "../users/users.service";
import {
    PresignedUrlRequestDto,
    PresignedUrlResponseDto,
} from "./dto/presigned-url.dto";

@Injectable()
export class FilesService {
    private readonly s3Client: S3Client;
    private readonly bucketName: string;

    constructor(
        private readonly prisma: PrismaService,
        private readonly usersService: UsersService,
    ) {
        this.bucketName = process.env.S3_INGEST;

        if (!this.bucketName) {
            throw new Error("S3_INGEST environment variable is required");
        }

        this.s3Client = new S3Client({
            region: process.env.AWS_REGION || "us-east-2",
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY,
                secretAccessKey: process.env.AWS_SECRET_KEY,
            },
        });
    }

    async generatePresignedUrl(
        request: PresignedUrlRequestDto,
        userId: string,
    ): Promise<PresignedUrlResponseDto> {
        const {
            fileName,
            contentType,
            expiresIn = 3600,
            public: isPublic,
        } = request;

        console.log("=== PRESIGNED URL GENERATION ===");
        console.log("Request:", request);
        console.log("Bucket:", this.bucketName);
        console.log("Region:", process.env.AWS_REGION || "us-east-1");

        try {
            // Get user and tenant information
            const user = await this.usersService.findById(userId);
            const tenantSlug = user.tenant?.slug || "default";

            console.log("User ID:", userId);
            console.log("User tenant:", user.tenant);
            console.log("Using tenant slug:", tenantSlug);

            // Generate file path based on public/private setting
            const timestamp = Date.now();
            const fileExtension = fileName.split(".").pop();
            const baseFileName = fileName.replace(/\.[^/.]+$/, ""); // Remove extension

            const s3Key = isPublic
                ? `public/${tenantSlug}/${timestamp}-${baseFileName}.${fileExtension}`
                : `private/${tenantSlug}/${timestamp}-${baseFileName}.${fileExtension}`;

            console.log("Generated S3 key:", s3Key);
            console.log("Is public:", isPublic);

            const command = new PutObjectCommand({
                Bucket: this.bucketName,
                Key: s3Key,
                ContentType: contentType,
                ACL: "private", // Ensure files are private
            });

            console.log("S3 Command:", command);

            const url = await getSignedUrl(this.s3Client, command, {
                expiresIn,
            });
            const expiresAt = new Date(
                Date.now() + expiresIn * 1000,
            ).toISOString();

            console.log("Generated presigned URL:", url);
            console.log("Expires at:", expiresAt);

            const response = {
                url,
                bucket: this.bucketName,
                key: s3Key,
                expiresAt,
                operation: "PUT",
            };

            console.log("Response:", response);
            console.log("=== END PRESIGNED URL GENERATION ===");

            return response;
        } catch (error) {
            console.error("Error generating presigned URL:", error);
            throw new InternalServerErrorException(
                "Failed to generate presigned URL",
            );
        }
    }
}
