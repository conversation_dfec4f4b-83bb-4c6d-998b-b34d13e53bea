import { <PERSON>du<PERSON> } from "@nestjs/common";

import { PrismaModule } from "nestjs-prisma";

import { RolesModule } from "../roles/roles.module";
import { UsersModule } from "../users/users.module";
import { FilesController } from "./files.controller";
import { FilesService } from "./files.service";

@Module({
    imports: [PrismaModule, RolesModule, UsersModule],
    controllers: [FilesController],
    providers: [FilesService],
    exports: [FilesService],
})
export class FilesModule {}
