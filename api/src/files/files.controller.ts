import {
    Controller,
    Post,
    Body,
    UseGuards,
    HttpCode,
    HttpStatus,
    Req,
} from "@nestjs/common";
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBearerAuth,
} from "@nestjs/swagger";

import { Request } from "express";

import { JwtAuthGuard } from "../auth/jwt-auth.guard";
import {
    PresignedUrlRequestDto,
    PresignedUrlResponseDto,
} from "./dto/presigned-url.dto";
import { FilesService } from "./files.service";

@ApiTags("Files")
@Controller("files")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class FilesController {
    constructor(private readonly filesService: FilesService) {}

    @Post("presigned-url")
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: "Generate presigned URL for uploading to S3_INGEST",
        description:
            "Generate a presigned URL for uploading files to the S3_INGEST bucket (PUT operation only)",
    })
    @ApiResponse({
        status: 200,
        description: "Presigned URL generated successfully",
        type: PresignedUrlResponseDto,
    })
    @ApiResponse({
        status: 400,
        description: "Bad request - invalid parameters",
    })
    @ApiResponse({
        status: 401,
        description: "Unauthorized - invalid or missing JWT token",
    })
    @ApiResponse({
        status: 500,
        description: "Internal server error - failed to generate presigned URL",
    })
    async generatePresignedUrl(
        @Body() request: PresignedUrlRequestDto,
        @Req() req: Request,
    ): Promise<PresignedUrlResponseDto> {
        const userId = req.user["userId"];
        return this.filesService.generatePresignedUrl(request, userId);
    }
}
