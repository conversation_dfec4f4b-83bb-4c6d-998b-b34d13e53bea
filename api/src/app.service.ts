import { Injectable, Logger } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import Redis, { Cluster } from "ioredis";

@Injectable()
export class AppService {
    private logger = new Logger("AppService");

    async isDatabaseConnectionHealthy(): Promise<boolean> {
        try {
            const prisma = new PrismaClient();
            await prisma.$connect();
            await prisma.$disconnect();
            return true;
        } catch (err) {
            this.logger.error("❌ Database connection error:", err.message);
            return false;
        }
    }

    async isRedisConnectionHealthy(): Promise<boolean> {
        let client: Redis | Cluster;
        if (process.env.ENVIRONMENT === "test") {
            client = new Redis({
                host: process.env.REDIS_HOST,
                port: process.env.REDIS_PORT,
            });
        } else {
            client = new Redis.Cluster(
                [
                    {
                        host: process.env.REDIS_HOST,
                        port: process.env.REDIS_PORT,
                    },
                ],
                {
                    dnsLookup: (address, callback) => callback(null, address),
                    redisOptions: {
                        tls: {},
                    },
                },
            );
        }
        try {
            await client.ping();
            return true;
        } catch (error) {
            this.logger.error("❌ Redis connection error:", error.message);
            return false;
        } finally {
            client.disconnect();
        }
    }
}
