import { Modu<PERSON> } from "@nestjs/common";

import { ExpressAdapter } from "@bull-board/express";
import { BullBoardModule } from "@bull-board/nestjs";

import { AuthModule } from "../auth/auth.module";
import { JwtStrategy } from "../auth/jwt.strategy";

// BullBoard does not integrate well with Nestjs auth guards
// https://github.com/felixmosh/bull-board/issues/698
// Hopefully this setup can be simplified some day
@Module({
    imports: [
        AuthModule,
        BullBoardModule.forRootAsync({
            useFactory: (jwtAuthStrategy: JwtStrategy) => {
                return {
                    route: "/events",
                    adapter: ExpressAdapter,
                    middleware: async (req, res, next) => {
                        const authCookie = req.cookies?.["auth"];
                        if (!authCookie) return res.sendStatus(401);

                        try {
                            jwtAuthStrategy.validate(authCookie);
                            next();
                        } catch {
                            res.sendStatus(401);
                        }
                    },
                };
            },
            imports: [AuthModule],
            inject: [JwtStrategy],
        }),
    ],
})
export class SecureBullBoardModule {}
