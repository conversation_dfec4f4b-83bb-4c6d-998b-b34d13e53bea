import { Injectable, UnauthorizedException } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";

import { Request } from "express";
import { Strategy } from "passport-jwt";

import { jwtConstants } from "./constants";

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor() {
        super({
            jwtFromRequest: (req: Request) => req.cookies?.["auth"],
            ignoreExpiration: true,
            secretOrKey: jwtConstants.secret,
        });
    }

    async validate(payload) {
        if (payload.exp * 1000 < Date.now()) {
            throw new UnauthorizedException("Expired token");
        }
        return {
            userId: payload.sub,
            tenantId: payload.tenantId,
            tenantSlug: payload.tenantSlug,
        };
    }
}
