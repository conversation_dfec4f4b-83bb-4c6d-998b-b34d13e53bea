import {
    <PERSON>,
    Post,
    Req,
    Request,
    Res,
    UnauthorizedException,
    UseGuards,
} from "@nestjs/common";
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Response } from "express";

import { UsersService } from "../users/users.service";
import { AuthService } from "./auth.service";
import {
    ACCESS_TOKEN_COOKIE_OPTIONS,
    REFRESH_TOKEN_COOKIE_OPTIONS,
    Public,
} from "./constants";
import { UserLoginDto } from "./dto/user-login.dto";
import { LocalAuthGuard } from "./local-auth.guard";

@Controller()
@ApiTags("auth")
export class AuthController {
    constructor(
        private readonly authService: AuthService,
        private readonly userService: UsersService,
    ) {}

    @Post("login")
    @Public()
    @UseGuards(LocalAuthGuard)
    @ApiOperation({
        description:
            "Logs in the user and sets an auth cookie. `username` may be a username or an email",
    })
    @ApiBody({ type: UserLoginDto })
    @ApiResponse({ status: 204, description: `Sets "auth" cookie with a JWT` })
    async login(@Request() req, @Res() res: Response): Promise<void> {
        const { accessToken, refreshToken } = await this.authService.login(
            req.user,
        );

        res.cookie("auth", accessToken, ACCESS_TOKEN_COOKIE_OPTIONS);
        res.cookie("refresh", refreshToken, REFRESH_TOKEN_COOKIE_OPTIONS);

        // Set a non-httpOnly cookie for WebSocket authentication
        res.cookie("ws_auth", accessToken, {
            httpOnly: false,
            maxAge: 3600000,
            sameSite: "strict",
        });

        res.sendStatus(204);
    }

    @Post("refresh")
    @Public()
    @ApiOperation({ summary: `Refresh the auth cookie` })
    @ApiResponse({ status: 204 })
    async refresh(@Request() req, @Res() res: Response): Promise<void> {
        const refeshToken = req.cookies["refresh"];
        if (!refeshToken) throw new UnauthorizedException();

        const accessToken = await this.authService.refresh(refeshToken);

        res.cookie("auth", accessToken, ACCESS_TOKEN_COOKIE_OPTIONS);

        // Also update the WebSocket auth cookie
        res.cookie("ws_auth", accessToken, {
            httpOnly: false,
            maxAge: 3600000,
            sameSite: "strict",
        });

        res.sendStatus(204);
    }

    @Post("logout")
    @ApiResponse({ status: 204 })
    async logout(@Req() req, @Res() res: Response): Promise<void> {
        const refreshToken = req.cookies["refresh"];

        this.authService.logout(refreshToken);

        res.clearCookie("auth");
        res.clearCookie("refresh");

        res.sendStatus(204);
    }
}
