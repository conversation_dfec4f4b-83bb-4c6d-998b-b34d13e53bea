import { Injectable, UnauthorizedException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";

import { compare } from "bcrypt";
import { PrismaService } from "nestjs-prisma";

import { UsersService } from "../users/users.service";

@Injectable()
export class AuthService {
    constructor(
        private userService: UsersService,
        private jwtService: JwtService,
        private prisma: PrismaService,
    ) {}

    async validateUser(usernameOrEmail: string, pass: string) {
        const user = await this.userService.findOne(usernameOrEmail);
        if (user && (await compare(pass, user.passwordHash))) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { passwordHash, ...result } = user;
            return result;
        }
        return null;
    }

    async login(user) {
        const accessToken = this.jwtService.sign(
            {
                sub: user.id,
                tenantId: user.tenantId,
                tenantSlug: user.tenant?.slug,
            },
            { expiresIn: "15m" },
        );

        const session = await this.prisma.userSession.create({
            data: {
                userId: user.id,
            },
        });

        const refreshToken = this.jwtService.sign(
            {
                sub: user.id,
                sessionId: session.id,
                tenantId: user.tenantId,
            },
            {
                expiresIn: "30d",
            },
        );

        return { accessToken, refreshToken };
    }

    async refresh(refreshToken: string) {
        let decoded;
        try {
            decoded = this.jwtService.verify(refreshToken);
        } catch {
            throw new UnauthorizedException();
        }

        const session = await this.prisma.userSession.findUnique({
            where: {
                id: decoded.sessionId,
            },
        });

        if (!session) throw new UnauthorizedException();

        const payload = {
            sub: decoded.sub,
            tenantId: decoded.tenantId,
        };
        return this.jwtService.sign(payload);
    }

    async logout(refreshToken: string) {
        const decoded = this.jwtService.verify(refreshToken);
        await this.prisma.userSession.deleteMany({
            where: {
                userId: decoded.userId,
            },
        });
    }
}
