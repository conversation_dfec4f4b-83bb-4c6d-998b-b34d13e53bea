import { BullModule } from "@nestjs/bullmq";
import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { ServeStaticModule } from "@nestjs/serve-static";

import { PrismaModule } from "nestjs-prisma";
import { PrismaService } from "nestjs-prisma";
import { join } from "path";

import { AgentsModule } from "./agents/agents.module";
import { AnalyticsModule } from "./analytics/analytics.module";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { AuthModule } from "./auth/auth.module";
import { SecureBullBoardModule } from "./bullboard/secure-bullboard.module";
import { CacheModule } from "./cache/cache.module";
import { CallsModule } from "./calls/calls.module";
import { ChatModule } from "./chat/chat.module";
import "./env";
import { GroupsModule } from "./groups/groups.module";
import { OpenaiClientModule } from "./openai-client/openai-client.module";
import { OpenaiModule } from "./openai/openai.module";
import { PhoneNumbersModule } from "./phone-numbers/phone-numbers.module";
import { RolesModule } from "./roles/roles.module";
import { TenantsModule } from "./tenants/tenants.module";
import { TwilioApiModule } from "./twilio-api/twilio-api.module";
import { TwilioModule } from "./twilio/twilio.module";
import { UsersModule } from "./users/users.module";
import { WebhooksModule } from "./webhooks/webhooks.module";

@Module({
    imports: [
        AgentsModule,
        AnalyticsModule,
        AuthModule,
        BullModule.forRoot({
            connection: {
                host: process.env.REDIS_HOST,
                port: Number(process.env.REDIS_PORT),
                dnsLookup: (address, callback) => callback(null, address),
                tls: process.env.ENVIRONMENT === "test" ? undefined : {},
            },
            prefix: "{BULLMQ}",
        }),
        CacheModule,
        CallsModule,
        ChatModule,
        GroupsModule,
        OpenaiClientModule,
        OpenaiModule,
        PhoneNumbersModule,
        PrismaModule,
        RolesModule,
        SecureBullBoardModule,
        ServeStaticModule.forRoot({
            rootPath: join(__dirname, "..", "static"),
        }),
        TenantsModule,
        TwilioApiModule,
        TwilioModule,
        WebhooksModule,
        UsersModule,
    ],
    controllers: [AppController],
    providers: [AppService, PrismaService],
    exports: [],
})
export class AppModule {}
