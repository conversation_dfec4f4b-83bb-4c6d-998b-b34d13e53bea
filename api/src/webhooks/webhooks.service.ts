import { InjectQueue } from "@nestjs/bullmq";
import { Injectable, Logger } from "@nestjs/common";

import { Queue } from "bullmq";
import { createHmac, randomUUID, timingSafeEqual } from "crypto";

import { PrismicWebhookPayloadDto } from "./dto/prismic-webhook.dto";
import { PRISMIC_SYNC_QUEUE, PrismicSyncJobData } from "./types";

@Injectable()
export class WebhooksService {
    private readonly logger = new Logger(WebhooksService.name);
    private readonly hmacAlgorithm =
        process.env["PRISMIC_HMAC_ALGORITHM"] ?? "sha256";

    constructor(
        @InjectQueue(PRISMIC_SYNC_QUEUE)
        private readonly prismicSyncQueue: Queue<PrismicSyncJobData>,
    ) {}

    verifyPrismicSignature(rawBody: Buffer | undefined, signature?: string) {
        const secret = process.env["PRISMIC_WEBHOOK_SECRET"];

        if (!secret) {
            this.logger.error("PRISMIC_WEBHOOK_SECRET is not configured");
            throw new Error("Prismic webhook secret is not configured");
        }

        if (!signature) {
            this.logger.warn("Missing x-prismic-signature header");
            return false;
        }

        if (!rawBody) {
            this.logger.warn("Raw request body missing for signature verification");
            return false;
        }

        const digest = createHmac(this.hmacAlgorithm, secret)
            .update(rawBody)
            .digest("hex");

        let signatureBuffer: Buffer;
        try {
            signatureBuffer = Buffer.from(signature, "hex");
        } catch (err) {
            this.logger.warn(
                `Failed to parse provided Prismic signature: ${(err as Error).message}`,
            );
            return false;
        }

        const digestBuffer = Buffer.from(digest, "hex");

        if (digestBuffer.length !== signatureBuffer.length) {
            return false;
        }

        return timingSafeEqual(digestBuffer, signatureBuffer);
    }

    async enqueueIncrementalSync(payload: PrismicWebhookPayloadDto) {
        const documentIds = Array.from(
            new Set(payload.documents?.map((doc) => doc.id).filter(Boolean) ?? []),
        );

        const jobData: PrismicSyncJobData = {
            mode: "incremental",
            deliveryId: payload.masterRef ?? randomUUID(),
            triggeredAt: new Date().toISOString(),
            documentIds,
            payload,
            masterRef: payload.masterRef,
        };

        await this.prismicSyncQueue.add("prismic-delivery", jobData, {
            attempts: 5,
            backoff: {
                type: "exponential",
                delay: 30000,
            },
            removeOnComplete: true,
        });

        this.logger.log(
            `Queued Prismic sync job (${documentIds.length} document(s), delivery ${jobData.deliveryId})`,
        );
    }

    async enqueueFullSync() {
        const jobData: PrismicSyncJobData = {
            mode: "full",
            deliveryId: randomUUID(),
            triggeredAt: new Date().toISOString(),
            documentIds: [],
        };

        await this.prismicSyncQueue.add("prismic-full-sync", jobData, {
            attempts: 3,
            backoff: {
                type: "fixed",
                delay: 60000,
            },
            removeOnComplete: true,
        });

        this.logger.log(
            `Queued Prismic full sync job (delivery ${jobData.deliveryId})`,
        );
    }
}
