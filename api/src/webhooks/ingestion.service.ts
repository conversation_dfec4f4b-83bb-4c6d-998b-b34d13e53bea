import { Injectable, Logger } from "@nestjs/common";

import {
    IngestionDeleteRequest,
    IngestionUpsertRequest,
    SerializedPrismicDocument,
} from "./types";

@Injectable()
export class IngestionService {
    private readonly logger = new Logger(IngestionService.name);

    async sendUpsert(payload: Omit<IngestionUpsertRequest, "tenantId">) {
        const tenantId = this.getTenantId();
        const body: IngestionUpsertRequest = {
            ...payload,
            tenantId,
        };
        await this.dispatch(body);
    }

    async sendDelete(payload: Omit<IngestionDeleteRequest, "tenantId">) {
        const tenantId = this.getTenantId();
        const body: IngestionDeleteRequest = {
            ...payload,
            tenantId,
        };
        await this.dispatch(body);
    }

    async sendUpsertBatches(
        documents: SerializedPrismicDocument[],
        basePayload: Omit<
            IngestionUpsertRequest,
            "tenantId" | "documents" | "batchId"
        >,
        batchSize: number,
    ) {
        const tenantId = this.getTenantId();
        for (let index = 0; index < documents.length; index += batchSize) {
            const batch = documents.slice(index, index + batchSize);
            const batchId = `${basePayload.deliveryId}-batch-${
                Math.floor(index / batchSize) + 1
            }`;

            const body: IngestionUpsertRequest = {
                ...basePayload,
                tenantId,
                batchId,
                documents: batch,
            };

            await this.dispatch(body);
        }
    }

    private async dispatch(
        body: IngestionUpsertRequest | IngestionDeleteRequest,
    ) {
        const url = process.env["INGESTION_SERVICE_URL"];
        if (!url) {
            this.logger.error(
                "INGESTION_SERVICE_URL environment variable is not set",
            );
            throw new Error("Missing INGESTION_SERVICE_URL configuration");
        }

        const apiKey = process.env["INGESTION_SERVICE_API_KEY"];

        const headers: Record<string, string> = {
            "Content-Type": "application/json",
            "User-Agent": "truthkeep-agents-prismic/1.0",
        };

        if (apiKey) {
            headers["Authorization"] = `Bearer ${apiKey}`;
        }

        const itemCount =
            "documents" in body
                ? body.documents.length
                : body.documentIds.length;

        this.logger.log(
            `Dispatching ${body.operation} request to ingestion service (${itemCount} items)`,
        );

        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 15000);

        const response = await fetch(url, {
            method: "POST",
            headers,
            body: JSON.stringify(body),
            signal: controller.signal,
        });

        clearTimeout(timeout);

        if (!response.ok) {
            const text = await response.text();
            this.logger.error(
                `Ingestion service responded with ${response.status}: ${text}`,
            );
            throw new Error(
                `Failed to deliver ingestion payload (status ${response.status})`,
            );
        }
    }

    private getTenantId() {
        const tenantId = process.env["PRISMIC_TENANT_ID"];
        if (!tenantId) {
            throw new Error("Set PRISMIC_TENANT_ID to route Prismic content");
        }
        return tenantId;
    }
}
