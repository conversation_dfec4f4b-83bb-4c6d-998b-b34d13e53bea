import { Injectable, Logger } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import {
    IngestionDeleteRequest,
    IngestionUpsertRequest,
    SerializedPrismicDocument,
} from "./types";

@Injectable()
export class IngestionService {
    private readonly logger = new Logger(IngestionService.name);
    private cachedTenantId?: string;

    constructor(private readonly prisma: PrismaService) {}

    async sendUpsert(payload: Omit<IngestionUpsertRequest, "tenantId">) {
        const tenantId = await this.resolveTenantId();
        const body: IngestionUpsertRequest = {
            ...payload,
            tenantId,
        };
        await this.dispatch(body);
    }

    async sendDelete(payload: Omit<IngestionDeleteRequest, "tenantId">) {
        const tenantId = await this.resolveTenantId();
        const body: IngestionDeleteRequest = {
            ...payload,
            tenantId,
        };
        await this.dispatch(body);
    }

    async sendUpsertBatches(
        documents: SerializedPrismicDocument[],
        basePayload: Omit<
            IngestionUpsertRequest,
            "tenantId" | "documents" | "batchId"
        >,
        batchSize: number,
    ) {
        const tenantId = await this.resolveTenantId();
        for (let index = 0; index < documents.length; index += batchSize) {
            const batch = documents.slice(index, index + batchSize);
            const batchId = `${basePayload.deliveryId}-batch-${
                Math.floor(index / batchSize) + 1
            }`;

            const body: IngestionUpsertRequest = {
                ...basePayload,
                tenantId,
                batchId,
                documents: batch,
            };

            await this.dispatch(body);
        }
    }

    private async dispatch(
        body: IngestionUpsertRequest | IngestionDeleteRequest,
    ) {
        const url = process.env["DAVID_INGEST_URL"];
        if (!url) {
            this.logger.error(
                "DAVID_INGEST_URL environment variable is not set",
            );
            throw new Error("Missing DAVID_INGEST_URL configuration");
        }

        const apiKey = process.env["DAVID_INGEST_API_KEY"];

        const headers: Record<string, string> = {
            "Content-Type": "application/json",
            "User-Agent": "truthkeep-agents-prismic/1.0",
        };

        if (apiKey) {
            headers["Authorization"] = `Bearer ${apiKey}`;
        }

        const itemCount =
            "documents" in body
                ? body.documents.length
                : body.documentIds.length;

        this.logger.log(
            `Dispatching ${body.operation} request to ingestion service (${itemCount} items)`,
        );

        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 15000);

        const response = await fetch(url, {
            method: "POST",
            headers,
            body: JSON.stringify(body),
            signal: controller.signal,
        });

        clearTimeout(timeout);

        if (!response.ok) {
            const text = await response.text();
            this.logger.error(
                `Ingestion service responded with ${response.status}: ${text}`,
            );
            throw new Error(
                `Failed to deliver ingestion payload (status ${response.status})`,
            );
        }
    }

    private async resolveTenantId() {
        if (this.cachedTenantId) return this.cachedTenantId;

        const tenantIdFromEnv = process.env["PRISMIC_TENANT_ID"];
        if (tenantIdFromEnv) {
            this.cachedTenantId = tenantIdFromEnv;
            return tenantIdFromEnv;
        }

        const tenantSlug = process.env["PRISMIC_TENANT_SLUG"];
        if (!tenantSlug) {
            throw new Error(
                "Set PRISMIC_TENANT_ID or PRISMIC_TENANT_SLUG to route Prismic content",
            );
        }

        const tenant = await this.prisma.tenant.findUnique({
            where: { slug: tenantSlug },
            select: { id: true },
        });

        if (!tenant) {
            throw new Error(`Unable to resolve tenant by slug: ${tenantSlug}`);
        }

        this.cachedTenantId = tenant.id;
        return tenant.id;
    }
}
