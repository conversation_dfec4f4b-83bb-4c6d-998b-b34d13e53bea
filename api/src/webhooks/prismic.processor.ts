import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Injectable, Logger } from "@nestjs/common";

import { Job } from "bullmq";

import { IngestionService } from "./ingestion.service";
import { PrismicService } from "./prismic.service";
import {
    PRISMIC_SYNC_QUEUE,
    PrismicSyncJobData,
    SerializedPrismicDocument,
} from "./types";

@Processor(PRISMIC_SYNC_QUEUE)
@Injectable()
export class PrismicSyncProcessor extends WorkerHost {
    private readonly logger = new Logger(PrismicSyncProcessor.name);
    private readonly batchSize = 20;

    constructor(
        private readonly prismicService: PrismicService,
        private readonly ingestionService: IngestionService,
    ) {
        super();
    }

    async process(job: Job<PrismicSyncJobData>) {
        this.logger.log(
            `Processing Prismic job ${job.id} (mode=${job.data.mode}, documents=${job.data.documentIds.length})`,
        );

        if (job.data.mode === "full") {
            await this.handleFullSync(job);
            return;
        }

        await this.handleIncrementalSync(job);
    }

    private async handleIncrementalSync(job: Job<PrismicSyncJobData>) {
        const { documentIds, deliveryId, triggeredAt, masterRef } = job.data;

        const uniqueDocumentIds = Array.from(new Set(documentIds));

        if (!uniqueDocumentIds.length) {
            this.logger.warn(
                "Incremental sync received with no document IDs. Skipping.",
            );
            return;
        }

        const documents = await this.prismicService.getDocumentsByIds(
            uniqueDocumentIds,
            masterRef,
        );

        const serialized: SerializedPrismicDocument[] = documents.map((doc) =>
            this.prismicService.serializeDocument(doc),
        );

        const fetchedIds = new Set(serialized.map((doc) => doc.id));
        const missingIds = uniqueDocumentIds.filter((id) => !fetchedIds.has(id));

        if (serialized.length) {
            await this.ingestionService.sendUpsertBatches(
                serialized,
                {
                    operation: "upsert",
                    source: "prismic",
                    deliveryId,
                    triggeredAt,
                },
                this.batchSize,
            );
        }

        if (missingIds.length) {
            this.logger.log(
                `Detected ${missingIds.length} missing documents in Prismic response. Treating as unpublish.`,
            );

            await this.ingestionService.sendDelete({
                operation: "delete",
                source: "prismic",
                deliveryId,
                triggeredAt,
                documentIds: missingIds,
            });
        }

    }

    private async handleFullSync(job: Job<PrismicSyncJobData>) {
        const { deliveryId, triggeredAt } = job.data;

        let page = 0;
        for await (const pageDocuments of this.prismicService.iterateAllDocuments(
            this.batchSize,
        )) {
            if (!pageDocuments.length) continue;

            page += 1;

            const serialized = pageDocuments.map((document) =>
                this.prismicService.serializeDocument(document),
            );

            await this.ingestionService.sendUpsertBatches(
                serialized,
                {
                    operation: "upsert",
                    source: "prismic",
                    deliveryId,
                    triggeredAt,
                },
                this.batchSize,
            );

            this.logger.log(
                `Full sync processed page ${page} (${serialized.length} documents)`,
            );
        }
    }
}
