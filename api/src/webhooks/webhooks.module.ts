import { BullModule } from "@nestjs/bullmq";
import { Mo<PERSON><PERSON> } from "@nestjs/common";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";
import { PrismaService } from "nestjs-prisma";

import { IngestionService } from "./ingestion.service";
import { PrismicSyncProcessor } from "./prismic.processor";
import { PrismicService } from "./prismic.service";
import { PRISMIC_SYNC_QUEUE } from "./types";
import { WebhooksController } from "./webhooks.controller";
import { WebhooksService } from "./webhooks.service";

@Module({
    imports: [
        BullBoardModule.forFeature({
            adapter: BullMQAdapter,
            name: PRISMIC_SYNC_QUEUE,
        }),
        BullModule.registerQueue({
            name: PRISMIC_SYNC_QUEUE,
        }),
    ],
    controllers: [WebhooksController],
    providers: [
        IngestionService,
        PrismaService,
        PrismicService,
        PrismicSyncProcessor,
        WebhooksService,
    ],
})
export class WebhooksModule {}
