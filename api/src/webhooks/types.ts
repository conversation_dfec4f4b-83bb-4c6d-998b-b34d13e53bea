import { PrismicWebhookPayloadDto } from "./dto/prismic-webhook.dto";

export const PRISMIC_SYNC_QUEUE = "Prismic sync";

export type PrismicSyncMode = "incremental" | "full";

export interface PrismicSyncJobData {
    mode: PrismicSyncMode;
    deliveryId: string;
    triggeredAt: string;
    documentIds: string[];
    payload?: PrismicWebhookPayloadDto;
    masterRef?: string;
}

export interface SerializedPrismicDocument {
    id: string;
    uid?: string | null;
    type: string;
    lang: string;
    tags: string[];
    slugs: string[];
    url?: string | null;
    href?: string;
    firstPublicationDate?: string | null;
    lastPublicationDate?: string | null;
    data: Record<string, unknown>;
    alternateLanguages?: Array<{ id: string; lang: string; type: string }>;
}

export interface IngestionUpsertRequest {
    source: "prismic";
    operation: "upsert";
    tenantId: string;
    deliveryId: string;
    triggeredAt: string;
    batchId: string;
    documents: SerializedPrismicDocument[];
}

export interface IngestionDeleteRequest {
    source: "prismic";
    operation: "delete";
    tenantId: string;
    deliveryId: string;
    triggeredAt: string;
    documentIds: string[];
}
