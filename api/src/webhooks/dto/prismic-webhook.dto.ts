import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

import { Type } from "class-transformer";
import {
    IsArray,
    IsDateString,
    IsObject,
    IsOptional,
    IsString,
    ValidateNested,
} from "class-validator";

export class PrismicWebhookDocumentDto {
    @ApiProperty({ description: "Document ID" })
    @IsString()
    id: string;

    @ApiPropertyOptional({ description: "Document UID" })
    @IsOptional()
    @IsString()
    uid?: string;

    @ApiProperty({ description: "Document type" })
    @IsString()
    type: string;

    @ApiProperty({ description: "Document language" })
    @IsString()
    lang: string;

    @ApiProperty({ description: "Document tags", type: String, isArray: true })
    @IsArray()
    tags: string[];

    @ApiPropertyOptional({
        description: "Document slugs",
        type: String,
        isArray: true,
    })
    @IsOptional()
    @IsArray()
    slugs?: string[];

    @ApiPropertyOptional({
        description: "Document data payload",
        type: "object",
        additionalProperties: true,
    })
    @IsOptional()
    @IsObject()
    data?: Record<string, unknown>;

    @ApiPropertyOptional({ description: "Document URL" })
    @IsOptional()
    @IsString()
    url?: string;

    @ApiPropertyOptional({
        description: "First publication date",
        type: String,
        format: "date-time",
    })
    @IsOptional()
    @IsDateString()
    first_publication_date?: string;

    @ApiPropertyOptional({
        description: "Last publication date",
        type: String,
        format: "date-time",
    })
    @IsOptional()
    @IsDateString()
    last_publication_date?: string;
}

export class PrismicWebhookPayloadDto {
    @ApiProperty({
        description: "Webhook type (api-update, test-trigger, ... )",
    })
    @IsString()
    type: string;

    @ApiPropertyOptional({ description: "Master ref for the release" })
    @IsOptional()
    @IsString()
    masterRef?: string;

    @ApiPropertyOptional({ description: "Repository domain" })
    @IsOptional()
    @IsString()
    domain?: string;

    @ApiPropertyOptional({ description: "API endpoint" })
    @IsOptional()
    @IsString()
    apiUrl?: string;

    @ApiPropertyOptional({
        description: "Trigger documents",
        type: PrismicWebhookDocumentDto,
        isArray: true,
    })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => PrismicWebhookDocumentDto)
    documents?: PrismicWebhookDocumentDto[];

    @ApiPropertyOptional({
        description: "Releases payload",
        type: "object",
        additionalProperties: true,
    })
    @IsOptional()
    @IsObject()
    releases?: Record<string, unknown>;

    @ApiPropertyOptional({
        description: "Masks payload",
        type: "object",
        additionalProperties: true,
    })
    @IsOptional()
    @IsObject()
    masks?: Record<string, unknown>;

    @ApiPropertyOptional({
        description: "Tags payload",
        type: "object",
        additionalProperties: true,
    })
    @IsOptional()
    @IsObject()
    tags?: Record<string, unknown>;
}
