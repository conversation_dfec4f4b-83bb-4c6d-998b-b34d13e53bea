import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
    IsArray,
    IsDateString,
    IsObject,
    IsOptional,
    IsString,
    ValidateNested,
} from "class-validator";

export class PrismicWebhookDocumentDto {
    @ApiProperty({ description: "Document ID" })
    @IsString()
    id: string;

    @ApiProperty({ description: "Document UID", required: false })
    @IsOptional()
    @IsString()
    uid?: string;

    @ApiProperty({ description: "Document type" })
    @IsString()
    type: string;

    @ApiProperty({ description: "Document language" })
    @IsString()
    lang: string;

    @ApiProperty({ description: "Document tags", type: [String] })
    @IsArray()
    tags: string[];

    @ApiProperty({ description: "Document slugs", type: [String], required: false })
    @IsOptional()
    @IsArray()
    slugs?: string[];

    @ApiProperty({ description: "Document data payload", type: "object", required: false })
    @IsOptional()
    @IsObject()
    data?: Record<string, unknown>;

    @ApiProperty({ description: "Document URL", required: false })
    @IsOptional()
    @IsString()
    url?: string;

    @ApiProperty({
        description: "First publication date",
        required: false,
        type: String,
        format: "date-time",
    })
    @IsOptional()
    @IsDateString()
    first_publication_date?: string;

    @ApiProperty({
        description: "Last publication date",
        required: false,
        type: String,
        format: "date-time",
    })
    @IsOptional()
    @IsDateString()
    last_publication_date?: string;
}

export class PrismicWebhookPayloadDto {
    @ApiProperty({ description: "Webhook type (api-update, test-trigger, ... )" })
    @IsString()
    type: string;

    @ApiProperty({ description: "Master ref for the release", required: false })
    @IsOptional()
    @IsString()
    masterRef?: string;

    @ApiProperty({ description: "Repository domain", required: false })
    @IsOptional()
    @IsString()
    domain?: string;

    @ApiProperty({ description: "API endpoint", required: false })
    @IsOptional()
    @IsString()
    apiUrl?: string;

    @ApiProperty({ description: "Trigger documents", type: [PrismicWebhookDocumentDto], required: false })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => PrismicWebhookDocumentDto)
    documents?: PrismicWebhookDocumentDto[];

    @ApiProperty({ description: "Releases payload", required: false, type: "object" })
    @IsOptional()
    @IsObject()
    releases?: Record<string, unknown>;

    @ApiProperty({ description: "Masks payload", required: false, type: "object" })
    @IsOptional()
    @IsObject()
    masks?: Record<string, unknown>;

    @ApiProperty({ description: "Tags payload", required: false, type: "object" })
    @IsOptional()
    @IsObject()
    tags?: Record<string, unknown>;
}
