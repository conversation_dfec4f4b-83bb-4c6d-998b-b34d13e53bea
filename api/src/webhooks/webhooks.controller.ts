import {
    BadRequestException,
    Body,
    Controller,
    Headers,
    HttpCode,
    HttpStatus,
    Logger,
    Post,
    Req,
} from "@nestjs/common";
import { ApiHeader, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import type { Request } from "express";

import { Public } from "../auth/constants";
import { PrismicWebhookPayloadDto } from "./dto/prismic-webhook.dto";
import { WebhooksService } from "./webhooks.service";

type RawBodyRequest<T> = T & { rawBody?: Buffer };

@ApiTags("webhooks")
@Controller("webhooks")
export class WebhooksController {
    private readonly logger = new Logger(WebhooksController.name);

    constructor(private readonly webhooksService: WebhooksService) {}

    @Post("prismic")
    @Public()
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: "Handle Prismic webhook deliveries",
        description:
            "Receives publish/unpublish notifications from Prismic and enqueues background sync jobs.",
    })
    @ApiHeader({
        name: "x-prismic-signature",
        description: "HMAC signature generated by Prismic",
        required: true,
    })
    @ApiResponse({
        status: 200,
        description: "Accepted",
        schema: {
            type: "object",
            properties: {
                status: { type: "string" },
                message: { type: "string" },
            },
        },
    })
    async handlePrismicWebhook(
        @Req() request: RawBodyRequest<Request>,
        @Body() payload: PrismicWebhookPayloadDto,
        @Headers("x-prismic-signature") signature?: string,
    ) {
        this.logger.log(`Received Prismic webhook of type ${payload.type}`);

        const isValid = this.webhooksService.verifyPrismicSignature(
            request.rawBody,
            signature,
        );

        if (!isValid) {
            throw new BadRequestException("Invalid webhook signature");
        }

        if (payload.type === "test-trigger") {
            return {
                status: "success",
                message: "Prismic test delivery acknowledged",
            };
        }

        if (payload.type === "api-update") {
            await this.webhooksService.enqueueIncrementalSync(payload);
            return {
                status: "success",
                message: "Prismic delivery queued for processing",
            };
        }

        this.logger.warn(`Unhandled Prismic webhook type: ${payload.type}`);
        return {
            status: "ignored",
            message: `Unhandled webhook type: ${payload.type}`,
        };
    }

    @Post("prismic/backfill")
    @HttpCode(HttpStatus.ACCEPTED)
    @ApiOperation({
        summary: "Trigger a full Prismic backfill",
    })
    async triggerPrismicBackfill() {
        await this.webhooksService.enqueueFullSync();
        return {
            status: "accepted",
            message: "Prismic backfill job enqueued",
        };
    }
}
