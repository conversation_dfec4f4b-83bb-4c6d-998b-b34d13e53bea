import { Injectable, Logger } from "@nestjs/common";

import * as prismic from "@prismicio/client";

import { SerializedPrismicDocument } from "./types";

type PrismicDocument = prismic.PrismicDocument<Record<string, unknown>>;

@Injectable()
export class PrismicService {
    private readonly logger = new Logger(PrismicService.name);
    private clientInstance?: prismic.Client<prismic.Content.AllDocumentTypes>;
    private endpoint?: string;

    async getDocumentsByIds(ids: string[], masterRef?: string) {
        if (!ids.length) {
            return [] as PrismicDocument[];
        }

        const parameters = masterRef ? { ref: masterRef } : undefined;
        this.logger.debug(
            `Fetching ${ids.length} Prismic documents with ref ${masterRef ?? "default"}`,
        );
        return this.getClient().getAllByIDs(ids, parameters);
    }

    async *iterateAllDocuments(pageSize = 50) {
        let page = 1;
        while (true) {
            const response = await this.getClient().get({
                pageSize,
                page,
            });

            if (!response.results.length) {
                break;
            }

            yield response.results as PrismicDocument[];

            if (page >= response.total_pages) {
                break;
            }

            page += 1;
        }
    }

    serializeDocument(document: PrismicDocument): SerializedPrismicDocument {
        return {
            id: document.id,
            uid: document.uid,
            type: document.type,
            lang: document.lang,
            tags: document.tags ?? [],
            slugs: document.slugs ?? [],
            url: document.url,
            href: document.href,
            firstPublicationDate: document.first_publication_date,
            lastPublicationDate: document.last_publication_date,
            data: document.data ?? {},
            alternateLanguages: document.alternate_languages?.map(
                (alternate) => ({
                    id: alternate.id,
                    lang: alternate.lang,
                    type: alternate.type,
                }),
            ),
        };
    }

    private getClient() {
        if (this.clientInstance) {
            return this.clientInstance;
        }

        const repository = process.env["PRISMIC_REPO"];
        if (!repository) {
            throw new Error("PRISMIC_REPO environment variable is not set");
        }

        if (!this.endpoint) {
            this.endpoint = prismic.getRepositoryEndpoint(repository);
        }

        this.clientInstance = prismic.createClient(this.endpoint, {
            accessToken: process.env["PRISMIC_ACCESS_TOKEN"],
        });

        return this.clientInstance;
    }
}
