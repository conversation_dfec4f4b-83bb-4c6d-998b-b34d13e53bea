import { AgentActionType } from "@prisma/client";
import { SessionUpdateEvent } from "openai/resources/beta/realtime/realtime";

type Tool = SessionUpdateEvent.Session.Tool;

export const HANG_UP_FUNCTION: Tool = {
    type: "function",
    name: "hang_up",
    description: "End the call, hanging up the phone",
};

type AgentEventWithActions = {
    name: string;
    description: string;
    actions: {
        agentAction: {
            type: AgentActionType;
            forwardToNumber?: string;
        };
    }[];
};

export function getAssistantTools<T extends Partial<AgentEventWithActions[]>>(
    events: T,
): Tool[] {
    const eventTools = [];

    for (const event of events) {
        const { name, description } = event;
        if (isCallForwardEvent(event)) {
            const sanitizedNumber = getForwardActionPhoneNumber(event).replace(
                "+",
                "",
            );
            eventTools.push({
                type: "function",
                name: `forward_to_${sanitizedNumber}`,
                description: `${description}. Forwards the call.`,
            });
        } else {
            eventTools.push({
                type: "function",
                name: name.replaceAll(" ", "_").toLowerCase(),
                description,
            });
        }
    }

    return eventTools;
}

function isCallForwardEvent(event: AgentEventWithActions) {
    for (const action of event.actions) {
        if (action.agentAction.type === AgentActionType.FORWARD_CALL)
            return true;
    }
    return false;
}

function getForwardActionPhoneNumber(event: AgentEventWithActions) {
    for (const action of event.actions) {
        if (action.agentAction.type === AgentActionType.FORWARD_CALL)
            return action.agentAction.forwardToNumber;
    }
}

export function getVectorStoreTools(): Tool[] {
    return [
        {
            type: "function",
            name: "lookup",
            description:
                "If the caller asks a question which you don't have the answer to, call this function to look it up. Once you are given the answer, repond appropriately.",
            parameters: {
                type: "object",
                properties: {
                    question: {
                        type: "string",
                        description: "The question that needs answering",
                    },
                },
            },
        },
    ];
}

export function getUnresolveTools(): Tool[] {
    return [
        {
            type: "function",
            name: "unresolve",
            description:
                "If the user rejects the answer or asks to speak to a human, call this function mark the conversation as unresolved.",
        },
    ];
}

export function getResolveToSalesTools(): Tool[] {
    return [
        {
            type: "function",
            name: "resolve_to_sales",
            description:
                "If the user asks the price or wants to know how to buy, call this function mark the conversation as resolved and direct the user to sales.",
        },
    ];
}

export function getResolveTools(): Tool[] {
    return [
        {
            type: "function",
            name: "resolve",
            description:
                "If the user accepts the answer or does not reject the answer, call this function mark the conversation as resolved.",
        },
    ];
}

export function getAgentSpecificTools(
    agentId: string,
    events: AgentEventWithActions[],
    hasEmbeddings: boolean,
): Tool[] {
    // Base tools: vector search, resolve, and unresolve
    const baseTools: Tool[] = [...(hasEmbeddings ? getVectorStoreTools() : [])];

    // Internal agent gets lookup tool (essential for answering questions) plus resolve tools
    if (agentId === "sifive-internal-chat-agent") {
        return [
            ...getVectorStoreTools(),
            ...getResolveTools(),
            ...getUnresolveTools(),
        ];
    }

    // External agent gets base tools plus assistant tools (including forwarding/human escalation)
    if (agentId === "sifive-external-chat-agent") {
        return [
            ...baseTools,
            ...getAssistantTools(events),
            ...getResolveTools(),
            ...getUnresolveTools(),
            ...getResolveToSalesTools(),
        ];
    }

    // Default fallback for other agents
    return [...baseTools, ...getAssistantTools(events)];
}
