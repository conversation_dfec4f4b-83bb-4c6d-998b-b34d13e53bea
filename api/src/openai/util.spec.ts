import { AgentActionType } from "@prisma/client";

import { getAssistantTools } from "./util";

describe("getAssistantTools", () => {
    it("Adds a function for each custom agent event", () => {
        const events = [
            {
                id: "2",
                name: "Support Request",
                description: "The caller needs support",
                createdAt: new Date(),
                agentId: "1",
                actions: [],
            },
        ];

        const tools = getAssistantTools(events);
        expect(tools).toMatchObject([
            {
                type: "function",
                name: "support_request",
                description: "The caller needs support",
            },
        ]);
    });

    it("Adds a special function for call forwarding events", () => {
        const events = [
            {
                name: "Forward to L1 support",
                description: "The caller needs L1 support",
                actions: [
                    {
                        agentAction: {
                            type: AgentActionType.FORWARD_CALL,
                            forwardToNumber: "+164710105555",
                        },
                    },
                ],
            },
            {
                name: "Forward to L2 support",
                description: "The caller needs L2 support",
                actions: [
                    {
                        agentAction: {
                            type: AgentActionType.FORWARD_CALL,
                            forwardToNumber: "+14165550101",
                        },
                    },
                ],
            },
        ];

        const tools = getAssistantTools(events);
        expect(tools).toMatchObject([
            {
                type: "function",
                name: "forward_to_164710105555",
                description: "The caller needs L1 support. Forwards the call.",
            },
            {
                type: "function",
                name: "forward_to_14165550101",
                description: "The caller needs L2 support. Forwards the call.",
            },
        ]);
    });
});
