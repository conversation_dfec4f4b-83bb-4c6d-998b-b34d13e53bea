import {
    Injectable,
    InternalServerErrorException,
    Logger,
} from "@nestjs/common";

import cachified from "@epic-web/cachified";
import { OpenAIRealtimeWebSocket } from "openai/beta/realtime/websocket";
import {
    ConversationItemCreatedEvent,
    ConversationItemCreateEvent,
    ResponseFunctionCallArgumentsDeltaEvent,
    ResponseAudioDeltaEvent,
    SessionUpdateEvent,
    ResponseOutputItemDoneEvent,
    RealtimeServerEvent,
    ResponseCreateEvent,
    ResponseTextDoneEvent,
    ResponseTextDeltaEvent,
} from "openai/resources/beta/realtime/realtime";
import {
    CreateEmbeddingResponse,
    EmbeddingModel,
} from "openai/resources/embeddings";

import { CacheService } from "../cache/cache.service";
import { OpenaiClientService } from "../openai-client/openai-client.service";

type Connection = {
    socket: OpenAIRealtimeWebSocket;
    lastAssistantItem?: ResponseAudioDeltaEvent;
};
@Injectable()
export class OpenaiService {
    private logger = new Logger("OpenaiService");
    private openaiConnections: Record<string, Connection> = {};

    constructor(
        private readonly openaiClientService: OpenaiClientService,
        private readonly cacheService: CacheService,
    ) {}

    openWebSocket(connectionId: string) {
        if (this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Attempt to open a duplicate connection with id=${connectionId}`,
            );
            throw new InternalServerErrorException();
        }

        const socket = this.openaiClientService.openWebSocket();
        this.openaiConnections[connectionId] = {
            socket,
        };
    }

    closeWebSocket(connectionId: string) {
        if (this.openaiConnections[connectionId]) {
            this.openaiClientService.closeWebSocket(
                this.openaiConnections[connectionId].socket,
            );
            delete this.openaiConnections[connectionId];
        }
    }

    listenForAudioDeltaResponse(connectionId: string, cb) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping listenForAudioDeltaResponse`,
            );
            return;
        }

        this.openaiClientService.listenForRealtimeServerEvent(
            this.openaiConnections[connectionId].socket,
            "response.audio.delta",
            (event: ResponseAudioDeltaEvent) => {
                if (this.openaiConnections[connectionId]) {
                    this.openaiConnections[connectionId].lastAssistantItem =
                        event;
                    cb(event);
                }
            },
        );
    }

    listenForSpeechStartedResponse(connectionId: string, cb) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping listenForSpeechStartedResponse`,
            );
            return;
        }

        this.openaiClientService.listenForRealtimeServerEvent(
            this.openaiConnections[connectionId].socket,
            "input_audio_buffer.speech_started",
            (event: ResponseAudioDeltaEvent) => {
                if (this.openaiConnections[connectionId]) {
                    cb(event);
                }
            },
        );
    }

    listenForFunctionCallStart(
        connectionId: string,
        cb: (event: ConversationItemCreatedEvent) => void,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping listenForFunctionCallStart`,
            );
            return;
        }

        this.openaiClientService.listenForRealtimeServerEvent(
            this.openaiConnections[connectionId].socket,
            "conversation.item.created",
            (event: ConversationItemCreatedEvent) => {
                if (this.openaiConnections[connectionId]) {
                    this.logger.log(event);
                    if (event.item.type === "function_call") cb(event);
                }
            },
        );
    }

    listenForFunctionCallArgumentsDelta(
        connectionId: string,
        cb: (event: ResponseFunctionCallArgumentsDeltaEvent) => void,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping listenForFunctionCallArgumentsDelta`,
            );
            return;
        }

        this.openaiClientService.listenForRealtimeServerEvent(
            this.openaiConnections[connectionId].socket,
            "response.function_call_arguments.delta",
            (event: ResponseFunctionCallArgumentsDeltaEvent) => {
                if (this.openaiConnections[connectionId]) {
                    this.logger.log(event);
                    cb(event);
                }
            },
        );
    }

    listenForResponseOutputItemDone(
        connectionId: string,
        cb: (event: ResponseOutputItemDoneEvent) => void,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping listenForResponseOutputItemDone`,
            );
            return;
        }

        this.openaiClientService.listenForRealtimeServerEvent(
            this.openaiConnections[connectionId].socket,
            "response.output_item.done",
            (event: RealtimeServerEvent) => {
                if (this.openaiConnections[connectionId]) {
                    if (event.type !== "response.output_item.done") return;
                    if (event.item.type !== "function_call") return;
                    this.logger.log(event);
                    cb(event);
                }
            },
        );
    }

    listenTextDelta(
        connectionId: string,
        cb: (event: ResponseTextDeltaEvent) => void,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping listenTextDelta`,
            );
            return;
        }

        this.openaiClientService.listenForRealtimeServerEvent(
            this.openaiConnections[connectionId].socket,
            "response.text.delta",
            (event: ResponseTextDeltaEvent) => {
                if (this.openaiConnections[connectionId]) {
                    this.logger.log(event);
                    cb(event);
                }
            },
        );
    }

    listenForTextDone(
        connectionId: string,
        cb: (event: ResponseTextDoneEvent) => void,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping listenForTextDone`,
            );
            return;
        }

        this.openaiClientService.listenForRealtimeServerEvent(
            this.openaiConnections[connectionId].socket,
            "response.text.done",
            (event: ResponseTextDoneEvent) => {
                if (this.openaiConnections[connectionId]) {
                    this.logger.log(event);
                    cb(event);
                }
            },
        );
    }

    sendAudioAppend(connectionId: string, audio: string) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendAudioAppend`,
            );
            return;
        }

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            {
                type: "input_audio_buffer.append",
                audio,
            },
        );
    }

    sendText(connectionId: string, text: string) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendText`,
            );
            return;
        }

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            {
                type: "conversation.item.create",
                item: {
                    type: "message",
                    role: "user",
                    content: [{ type: "input_text", text }],
                },
            },
        );
    }

    async sendInitialConversationMessage(
        connectionId: string,
        {
            instructions,
            tools,
        }: {
            instructions: string;
            tools: SessionUpdateEvent.Session.Tool[];
        },
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendInitialConversationMessage`,
            );
            return;
        }

        await new Promise((resolve) =>
            this.openaiClientService.listenForRealtimeServerEvent(
                this.openaiConnections[connectionId].socket,
                "session.created",
                resolve,
            ),
        );

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            {
                type: "session.update",
                session: {
                    turn_detection: { type: "semantic_vad" },
                    input_audio_format: "g711_ulaw",
                    output_audio_format: "g711_ulaw",
                    voice: "alloy",
                    instructions,
                    modalities: ["text", "audio"],
                    temperature: 0.8,
                    tools,
                },
            },
        );

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            {
                type: "conversation.item.create",
                item: {
                    type: "message",
                    role: "user",
                    content: [
                        {
                            type: "input_text",
                            text: 'Greet the user happily with "Thanks for calling, how many I help you?"',
                        },
                    ],
                },
            },
        );

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            {
                type: "response.create",
            },
        );
    }

    // TODO - how can we refactor this class to be ... better ?
    async sendInitialTextConversationMessage(
        connectionId: string,
        {
            instructions,
            tools,
            agentId,
            tenantName,
        }: {
            instructions: string;
            tools: SessionUpdateEvent.Session.Tool[];
            agentId?: string;
            tenantName?: string;
        },
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendInitialTextConversationMessage`,
            );
            return;
        }

        await new Promise((resolve) =>
            this.openaiClientService.listenForRealtimeServerEvent(
                this.openaiConnections[connectionId].socket,
                "session.created",
                resolve,
            ),
        );

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            {
                type: "session.update",
                session: {
                    instructions,
                    modalities: ["text"],
                    temperature: 0.8,
                    tools,
                },
            },
        );

        // Send agent-specific initial message
        let initialMessageText: string;

        if (agentId.includes("internal-chat-agent")) {
            initialMessageText = `Say these exact words: 'Hi, I'm an AI Support Agent with ${tenantName}. I'm here to help with any questions you might have about ${tenantName} or our products. While I try my best to provide accurate information, I may occasionally make mistakes.'`;
        } else {
            initialMessageText = `Say these exact words: 'Hi, I'm an AI Support Agent with ${tenantName}. I'm here to help with any questions you might have about ${tenantName} or our products. While I try my best to provide accurate information, I may occasionally make mistakes. At any time let me know if you want to speak to a human!'`;
        }

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            {
                type: "conversation.item.create",
                item: {
                    type: "message",
                    role: "user",
                    content: [
                        {
                            type: "input_text",
                            text: initialMessageText,
                        },
                    ],
                },
            },
        );

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            {
                type: "response.create",
            },
        );
    }

    sendResponseCreate(connectionId: string) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendResponseCreate`,
            );
            return;
        }

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            { type: "response.create" },
        );
    }

    async sendTruncate(
        connectionId: string,
        { elapsedTime }: { elapsedTime: number },
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendTruncate`,
            );
            return;
        }

        if (!this.openaiConnections[connectionId].lastAssistantItem) return;

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            {
                type: "conversation.item.truncate",
                item_id:
                    this.openaiConnections[connectionId].lastAssistantItem
                        .item_id,
                audio_end_ms: elapsedTime,
                content_index: 0,
            },
        );

        this.openaiConnections[connectionId].lastAssistantItem = null;
    }

    sendHangupToolCallResponse(connectionId: string) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendHangupToolCallResponse`,
            );
            return;
        }

        const responseItemCreate: ResponseCreateEvent = {
            type: "response.create",
            response: {
                instructions:
                    "Thank the caller for calling Truthkeep, and say goodbye to them.",
            },
        };

        this.logger.debug(responseItemCreate);
        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            responseItemCreate,
        );
    }

    sendLookupToolCallPreview(
        connectionId: string,
        call_id: string,
        embeddedContent: string,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendLookupToolCallPreview`,
            );
            return;
        }

        this.sendToolCallOutput(connectionId, call_id, embeddedContent);
    }

    sendLookupToolCallResponse(
        connectionId: string,
        call_id: string,
        embeddedContent: string,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendLookupToolCallResponse`,
            );
            return;
        }

        this.sendToolCallOutput(connectionId, call_id, embeddedContent);

        const responseItemCreate: ResponseCreateEvent = {
            type: "response.create",
            response: {
                instructions: `Reply based on the lookup output. Provide a markdown block quote to show what you are referencing and make relevant keywords bolded.`,
            },
        };

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            responseItemCreate,
        );
    }

    sendResolveToolCallResponse(connectionId: string, call_id: string) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendResolveToolCallResponse`,
            );
            return;
        }

        this.sendToolCallOutput(connectionId, call_id, "");

        const responseItemCreate: ResponseCreateEvent = {
            type: "response.create",
            response: {
                instructions:
                    "Ask the user if there's anything else you can help them with.",
            },
        };

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            responseItemCreate,
        );
    }

    sendUnresolveToolCallResponse(
        connectionId: string,
        call_id: string,
        unresolvedUrl: string,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendUnresolveToolCallResponse`,
            );
            return;
        }

        this.sendToolCallOutput(connectionId, call_id, "");

        const responseItemCreate: ResponseCreateEvent = {
            type: "response.create",
            response: {
                instructions: `Apologize for not resolving their issue and direct the user to this url: ${unresolvedUrl}`,
            },
        };

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            responseItemCreate,
        );
    }

    sendResolveToSalesToolCallResponse(
        connectionId: string,
        call_id: string,
        unresolvedUrl: string,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendResolveToSalesToolCallResponse`,
            );
            return;
        }

        this.sendToolCallOutput(connectionId, call_id, "");

        const responseItemCreate: ResponseCreateEvent = {
            type: "response.create",
            response: {
                instructions: `Thank the user for chatting with us and direct them to this url: ${unresolvedUrl}`,
            },
        };

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            responseItemCreate,
        );
    }

    sendForwardCallResponse(connectionId: string, call_id: string) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendForwardCallResponse`,
            );
            return;
        }

        this.sendToolCallOutput(connectionId, call_id, "success");

        const responseItemCreate: ResponseCreateEvent = {
            type: "response.create",
            response: {
                instructions:
                    "Tell the caller that they are being transferred.",
            },
        };

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            responseItemCreate,
        );
    }

    sendGenericToolResponse(connectionId: string, call_id: string) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendGenericToolResponse`,
            );
            return;
        }

        this.sendToolCallOutput(connectionId, call_id, "");

        const responseItemCreate: ResponseCreateEvent = {
            type: "response.create",
        };

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            responseItemCreate,
        );
    }

    sendToolCallOutput(
        connectionId: string,
        call_id: string,
        toolCallOutput: string,
    ) {
        if (!this.openaiConnections[connectionId]) {
            this.logger.warn(
                `Connection ${connectionId} not found, skipping sendToolCallOutput`,
            );
            return;
        }

        const conversationItemCreate: ConversationItemCreateEvent = {
            type: "conversation.item.create",
            item: {
                call_id,
                type: "function_call_output",
                output: toolCallOutput,
            },
        };

        this.openaiClientService.sendMessage(
            this.openaiConnections[connectionId].socket,
            conversationItemCreate,
        );
    }

    async getEmbedding(
        input: string,
        model: EmbeddingModel,
        dimensions = 1536,
    ): Promise<CreateEmbeddingResponse> {
        return cachified({
            key: `embedding-${dimensions}-${model}-"${input}"`,
            cache: this.cacheService.cache,
            getFreshValue: () =>
                this.openaiClientService.getEmbedding(input, model, dimensions),
        });
    }

    async rerankSearchableEntities(
        searchableEntities: {
            id;
            document_id;
            file_name;
            page_number;
            embedded_content;
            similarity;
        }[],
        question: string,
        limit: number,
    ) {
        const content = searchableEntities
            .map(
                (entity) =>
                    `ID: ${entity.id}\nDocument: ${entity.file_name} (Page ${entity.page_number})\nContent: ${entity.embedded_content}`,
            )
            .join("\n\n");

        const prompt = `Given the following question: "${question}"

And these searchable entities:
${content}

Please rerank these entities by relevance to the question. Return ONLY the numeric IDs in order of relevance (most relevant first), separated by commas. Do not return file names, page numbers, or any other text. Limit to ${limit} results.

Example format: 123,456,789`;

        try {
            const response =
                await this.openaiClientService.createChatCompletion({
                    model: "gpt-4o-mini",
                    messages: [
                        {
                            role: "user",
                            content: prompt,
                        },
                    ],
                    temperature: 0.1,
                    max_tokens: 100,
                });

            // Handle both completion and stream responses
            if ("choices" in response) {
                const aiResponse = response.choices[0]?.message?.content;
                console.log(`AI response: ${aiResponse}`);

                const rankedIds =
                    aiResponse
                        ?.split(",")
                        .map((id) => id.trim())
                        .filter((id) => id.length > 0) || [];

                console.log(`Parsed ranked IDs: ${rankedIds}`);
                console.log(
                    `Available entity IDs: ${searchableEntities.map((e) => e.id).slice(0, 5)}`,
                );

                // Map back to original entities in the ranked order
                const rerankedSearchableEntities = rankedIds
                    .map((id) =>
                        searchableEntities.find(
                            (entity) => entity.id.toString() === id,
                        ),
                    )
                    .filter(Boolean)
                    .slice(0, limit);

                console.log(
                    `Reranked entities found: ${rerankedSearchableEntities.length}`,
                );
                return rerankedSearchableEntities;
            } else {
                this.logger.warn(
                    "Received stream response, falling back to original order",
                );
                return searchableEntities.slice(0, limit);
            }
        } catch (error) {
            this.logger.error("Error reranking searchable entities:", error);
            return searchableEntities.slice(0, limit);
        }
    }
}
