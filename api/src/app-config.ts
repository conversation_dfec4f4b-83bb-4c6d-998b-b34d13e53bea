import {
    BadRequestException,
    ValidationError,
    ValidationPipe,
} from "@nestjs/common";
import { HttpAdapterHost } from "@nestjs/core";
import { NestExpressApplication } from "@nestjs/platform-express";
import { WsAdapter } from "@nestjs/platform-ws";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";

import * as cookieParser from "cookie-parser";
import { json } from "express";
import type { Request } from "express";
import { PrismaClientExceptionFilter } from "nestjs-prisma";

export async function setupApp(app: NestExpressApplication) {
    const PRISMIC_WEBHOOK_PATH = "/api/webhooks/prismic";

    app.use(
        PRISMIC_WEBHOOK_PATH,
        json({
            limit: "5mb",
            verify: (req: Request & { rawBody?: Buffer }, _res, buffer) => {
                req.rawBody = Buffer.from(buffer);
            },
        }),
    );

    app.setGlobalPrefix("api");

    app.set("trust proxy", 1);

    // Websockets
    // https://docs.nestjs.com/websockets/adapter#ws-library
    const wsAdapter = new WsAdapter(app);
    wsAdapter.setMessageParser((message) => {
        const messageObject = JSON.parse(message.toString());

        // chat messages
        if (messageObject.messages) {
            return {
                event: "messages",
                data: messageObject.messages,
            };
        }

        // Twilio media-stream messages
        const { event, sequenceNumber, streamSid, ...data } = messageObject;
        return {
            event,
            data: {
                event,
                sequenceNumber,
                streamSid,
                ...data,
            },
        };
    });
    app.useWebSocketAdapter(wsAdapter);

    // Swagger
    const enableSwaggerUI = process.env.NODE_ENV !== "production";
    const config = new DocumentBuilder()
        .setTitle("Truthkeep Agents API")
        .setVersion("1.0")
        .build();
    const documentFactory = () => SwaggerModule.createDocument(app, config);
    SwaggerModule.setup("api", app, documentFactory, {
        ui: enableSwaggerUI,
        raw: enableSwaggerUI,
    });
    app.use(cookieParser());

    // Validation
    app.useGlobalPipes(
        new ValidationPipe({
            forbidUnknownValues: true,
            exceptionFactory: (validationErrors: ValidationError[] = []) => {
                return new BadRequestException(
                    validationErrors.map((error) => ({
                        field: error.property,
                        error: Object.values(error.constraints || {}).join(
                            ", ",
                        ),
                    })),
                );
            },
        }),
    );

    // Prisma error handling
    const { httpAdapter } = app.get(HttpAdapterHost);
    app.useGlobalFilters(new PrismaClientExceptionFilter(httpAdapter));

    await app.listen(process.env.PORT ?? 3000, "0.0.0.0");
}
