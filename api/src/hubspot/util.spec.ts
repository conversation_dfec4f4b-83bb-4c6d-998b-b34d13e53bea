import { formatNote } from "./util";

describe("formatNote", () => {
    it("Formats the conversation as a single string", () => {
        const messages = [
            { sender: "USER" as const, content: "Hello" },
            { sender: "AI" as const, content: "Hello to you too" },
        ];
        const note = formatNote(messages);
        expect(note).toBe("USER: Hello\n\nAI: Hello to you too");
    });
});
