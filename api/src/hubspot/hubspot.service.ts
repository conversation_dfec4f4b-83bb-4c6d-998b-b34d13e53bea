import { Injectable } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import { Contact } from "../hubspot-client/entity/contact";
import { HubspotClientService } from "../hubspot-client/hubspot-client.service";
import { formatNote } from "./util";

@Injectable()
export class HubspotService {
    constructor(
        private readonly hubspotClientService: HubspotClientService,
        private readonly prisma: PrismaService,
    ) {}

    async peristConversation(chatId: string, body: Record<string, unknown>) {
        const messages = await this.prisma.conversationMessage.findMany({
            where: {
                sessionId: chatId,
            },
        });

        const note = formatNote(messages);

        const name = (body["name"] as string) ?? "UNKNOWN";
        const [firstname, lastname] = name.split(" ");
        const contact: Contact = {
            firstname: firstname,
            lastname: lastname,
            email: (body["email"] as string) ?? "UNKNOWN",
            company: (body["company"] as string) ?? "UNKNOWN",
            phone: (body["phone"] as string) ?? "UNKNOWN",
        };

        // Ensure contact exists
        const { id: contactId } =
            (await this.getContactByEmail(contact.email)) ||
            (await this.hubspotClientService.createContact(contact));

        // Attach note to contact
        await this.hubspotClientService.createNote(note, { contactId });
    }

    async getContactByEmail(email: string) {
        const {
            results: [contact],
        } = await this.hubspotClientService.searchContacts({
            query: email,
            limit: 1,
            filterGroups: [
                { propertyName: "email", operator: "EQ", value: email },
            ],
        });
        return contact;
    }
}
