import { Injectable } from "@nestjs/common";

import { Request } from "express";

import { CreateCallRequestDto } from "./dto/create-call.dto";
import { getMediaStreamUrl } from "./util";

@Injectable()
export class CallsService {
    getMediaStreamTwiml(
        incomingRequest: Request,
        body: CreateCallRequestDto,
    ): string {
        const mediaStreamUrl = getMediaStreamUrl(incomingRequest);
        return `<?xml version="1.0" encoding="UTF-8"?><Response><Connect><Stream url="${mediaStreamUrl}"><Parameter name="To" value="${body.To}"/><Parameter name="From" value="${body.From}"/></Stream></Connect></Response>`;
    }
}
