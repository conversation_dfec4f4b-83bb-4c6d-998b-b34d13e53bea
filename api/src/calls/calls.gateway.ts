import { InjectQueue } from "@nestjs/bullmq";
import { InternalServerErrorException, Logger } from "@nestjs/common";
import {
    ConnectedSocket,
    MessageBody,
    OnGatewayConnection,
    OnGatewayDisconnect,
    SubscribeMessage,
    WebSocketGateway,
    WebSocketServer,
} from "@nestjs/websockets";

import { Queue } from "bullmq";
import { PrismaService } from "nestjs-prisma";
import {
    ResponseAudioDeltaEvent,
    ResponseOutputItemDoneEvent,
    SessionUpdateEvent,
} from "openai/resources/beta/realtime/realtime";
import { Server, WebSocket } from "ws";
import { z } from "zod";

import { OpenaiService } from "../openai/openai.service";
import { getAgentSpecificTools, HANG_UP_FUNCTION } from "../openai/util";
import { TwilioService } from "../twilio/twilio.service";
import { AgentsService } from "./../agents/agents.service";
import { CallState, RealtimeCall } from "./entity/realtime-call.entity";
import { waitFor } from "./util";
import { getSystemInstruction } from "./util";

@WebSocketGateway({ path: "/api/media-stream" })
export class CallsGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private logger = new Logger("CallsGateway");

    @WebSocketServer()
    private server: Server;

    private twilioConnections: Record<string, RealtimeCall> = {};

    constructor(
        private readonly agentsService: AgentsService,
        private readonly openaiService: OpenaiService,
        private readonly twilioService: TwilioService,
        private readonly prisma: PrismaService,
        @InjectQueue("Phone agent events") private agentEventQueue: Queue,
    ) {}

    async handleConnection() {
        this.logger.log("Client connection");
    }

    handleDisconnect() {
        this.logger.log("Client disconnect");
    }

    @SubscribeMessage("start")
    async handleStart(
        @ConnectedSocket() twilioSocket: WebSocket,
        @MessageBody() message,
    ) {
        this.logger.log("Call start event", message);

        const { streamSid } = message;
        const { callSid } = message.start;
        const { To, From } = message.start.customParameters;

        this.openaiService.openWebSocket(streamSid);
        // For phone calls, we need to find the agent first to get the tenant context
        // Since phone numbers are now unique per tenant, we can find the agent without tenantId
        const agent = await this.prisma.agent.findFirst({
            where: {
                type: "PHONE",
                phoneNumber: To,
            },
            include: {
                tenant: true,
                events: {
                    include: {
                        actions: {
                            include: {
                                agentAction: true,
                            },
                        },
                    },
                },
            },
        });

        if (!agent) {
            this.logger.warn(`No agent found with phone number "${To}"`);
            twilioSocket.close();
            return;
        }

        this.logger.log(
            `Using agent "${agent.name}" with phone number ${To} for tenant "${agent.tenant.name}"`,
        );

        this.twilioConnections[streamSid] = {
            agent,
            twilioSocket,
            latestMediaTimestamp: 0,
            responseStartTimestampTwilio: null,
            markQueue: [],
            call: { From },
            state: CallState.ACTIVE,
            callSid,
        };

        // Send initial session update
        // Send initial prompt
        const systemInstruction = getSystemInstruction(agent);
        const systemInstructionSnippet =
            systemInstruction.length > 100
                ? `${systemInstruction.slice(0, 100)}...`
                : systemInstruction;
        const hasEmbeddings = await this.agentsService.hasEmbeddings(agent.id);
        const tools: SessionUpdateEvent.Session.Tool[] = [
            HANG_UP_FUNCTION,
            ...getAgentSpecificTools(agent.id, agent.events, hasEmbeddings),
        ];

        this.logger.log(
            `Using system instruction: "${systemInstructionSnippet}"`,
        );
        this.logger.log(`Using tools: ${JSON.stringify(tools)}`);
        this.openaiService.sendInitialConversationMessage(streamSid, {
            instructions: systemInstruction,
            tools,
        });

        // Set up handler for audio events coming back from OpenAI
        this.openaiService.listenForAudioDeltaResponse(streamSid, (event) => {
            this.handleAudioDeltaResponse(streamSid, event);
        });

        // Handle interruption when the caller's speech starts
        this.openaiService.listenForSpeechStartedResponse(streamSid, () =>
            this.handleSpeechStartedResponse(streamSid),
        );

        // Handle tool calls
        this.openaiService.listenForResponseOutputItemDone(streamSid, (event) =>
            this.handleResponseOutputItemDone(streamSid, event),
        );

        this.logger.log(`Initialized stream "${streamSid}"`);
    }

    @SubscribeMessage("stop")
    async handleStop(
        @ConnectedSocket() twilioSocket: WebSocket,
        @MessageBody() { streamSid },
    ) {
        this.openaiService.closeWebSocket(streamSid);
        delete this.twilioConnections[streamSid];
    }

    @SubscribeMessage("media")
    handleMedia(
        @MessageBody()
        messageBody: {
            media: { payload: string; timestamp: number };
            streamSid: string;
        },
    ) {
        const { streamSid, media } = messageBody;

        if (!this.twilioConnections[streamSid]) {
            this.logger.warn(
                `Recieved "media" message for a stream that doesn't exist ("${streamSid}")`,
            );
            throw new InternalServerErrorException();
        }

        this.twilioConnections[streamSid].latestMediaTimestamp =
            media.timestamp;
        this.openaiService.sendAudioAppend(streamSid, media.payload);
    }

    @SubscribeMessage("mark")
    handleMark(
        @MessageBody()
        messageBody: {
            streamSid: string;
        },
    ) {
        const { streamSid } = messageBody;
        this.twilioConnections[streamSid].markQueue.shift();
    }

    private handleAudioDeltaResponse(
        streamSid: string,
        audioDeltaEvent: ResponseAudioDeltaEvent,
    ) {
        const twilioConnection = this.twilioConnections[streamSid];
        twilioConnection.responseStartTimestampTwilio =
            twilioConnection.responseStartTimestampTwilio ||
            twilioConnection.latestMediaTimestamp;

        const mediaEvent = {
            event: "media",
            streamSid,
            media: {
                payload: audioDeltaEvent.delta,
            },
        };
        twilioConnection.twilioSocket.send(JSON.stringify(mediaEvent));

        const markEvent = {
            event: "mark",
            streamSid,
            mark: {
                name: "responsePart",
            },
        };
        twilioConnection.twilioSocket.send(JSON.stringify(markEvent));
        twilioConnection.markQueue.push("responsePart");
    }

    private handleSpeechStartedResponse(streamSid: string) {
        this.logger.log("Handling speech started event");
        const twilioConnection = this.twilioConnections[streamSid];

        // The caller interrupted while the agent was about to stop the call
        if (this.twilioConnections[streamSid].state === CallState.ENDING) {
            this.twilioConnections[streamSid].state = CallState.ACTIVE;
        }
        // The caller interrupted while the agent was about to forward the call
        if (this.twilioConnections[streamSid].state === CallState.FORWARDING) {
            this.twilioConnections[streamSid].state = CallState.ACTIVE;
        }

        if (!twilioConnection.markQueue.length) return;
        if (twilioConnection.responseStartTimestampTwilio == null) return;

        const elapsedTime =
            twilioConnection.latestMediaTimestamp -
            twilioConnection.responseStartTimestampTwilio;

        const clearEvent = {
            event: "clear",
            streamSid,
        };
        this.logger.log("Sending clear event to twilio");
        twilioConnection.twilioSocket.send(JSON.stringify(clearEvent));

        this.openaiService.sendTruncate(streamSid, { elapsedTime });

        twilioConnection.markQueue = [];
        twilioConnection.responseStartTimestampTwilio = null;
    }

    private async handleResponseOutputItemDone(
        streamSid: string,
        event: ResponseOutputItemDoneEvent,
    ) {
        this.logger.log(`Handling ${event.type}`);
        const { name } = event.item;

        if (name === "hang_up")
            return this.handleAssistantHangupEvent(streamSid);

        if (name === "lookup") return this.handleLookup(streamSid, event);

        if (name.match("forward_to_"))
            return this.handleForwardCall(streamSid, event);

        const formattedEventName = `${name[0].toUpperCase()}${name.slice(1).replaceAll("_", " ")}`;

        const { agent, call } = this.twilioConnections[streamSid];

        const jobData = {
            event,
            agent: agent.name,
            caller: call.From,
        };

        this.logger.log({ formattedEventName, jobData });
        const job = await this.agentEventQueue.add(formattedEventName, jobData);
        this.logger.log(`Job ${job.id} added to queue`);
    }

    private async handleAssistantHangupEvent(streamSid: string) {
        this.twilioConnections[streamSid].state = CallState.ENDING;

        // Remind assistant to say bye
        this.openaiService.sendHangupToolCallResponse(streamSid);

        // Wait for all pending audio to finish playing before ending call
        try {
            await waitFor(
                () => this.twilioConnections[streamSid].markQueue.length == 0,
                10 * 1000,
            );
        } catch {
            this.logger.log(
                "Timeout waiting for agent audio to finish playing. Aborting hang up.",
            );
            return;
        }

        // Give caller one last chance to continue conversation
        await new Promise((resolve) => setTimeout(resolve, 2 * 1000));
        if (this.twilioConnections[streamSid].state !== CallState.ENDING) {
            this.logger.log("Aborting hang up");
            return;
        }

        // Finally end the call completely
        this.logger.log("Hanging up");
        this.openaiService.closeWebSocket(streamSid);
        this.twilioConnections[streamSid].twilioSocket.close();
        delete this.twilioConnections[streamSid];
    }

    private async handleLookup(
        streamSid: string,
        event: ResponseOutputItemDoneEvent,
    ) {
        this.logger.log(event);

        const schema = z.object({ question: z.string() });
        let toolCallArguments: z.infer<typeof schema>;
        try {
            toolCallArguments = schema.parse(JSON.parse(event.item.arguments));
        } catch (error) {
            this.logger.error(error);
            return;
        }
        if (!toolCallArguments.question) {
            this.logger.error('Required field "question" missing');
            return;
        }
        const { question } = toolCallArguments;

        const embedding = await this.openaiService.getEmbedding(
            toolCallArguments.question,
            "text-embedding-3-small",
        );

        // Lookup in database with ... agent service?
        const agentId = this.twilioConnections[streamSid].agent.id;
        const searchableEntities = await this.agentsService.semanticSearch(
            agentId,
            embedding.data[0],
            20,
        );

        if (!searchableEntities.length) {
            this.logger.log(
                `Found no embedded content to respond to "${question}"`,
            );
            // TODO respond anyway to say nothing was found?
            return;
        }
        const [{ file_name, embedded_content, similarity }] =
            searchableEntities;

        // Send response to tool call
        this.logger.log(
            `Responding to query "${question}" with embedded content "${embedded_content}" (file "${file_name}", similarity ${similarity})`,
        );
        this.openaiService.sendLookupToolCallResponse(
            streamSid,
            event.item.call_id,
            embedded_content,
        );
    }

    private async handleForwardCall(
        streamSid: string,
        event: ResponseOutputItemDoneEvent,
    ) {
        this.twilioConnections[streamSid].state = CallState.FORWARDING;

        // Immediately send response to openai and tell it to say something
        this.openaiService.sendForwardCallResponse(
            streamSid,
            event.item.call_id,
        );

        // Wait for all pending audio to finish playing before forwarding call
        try {
            await waitFor(
                () => this.twilioConnections[streamSid].markQueue.length == 0,
                10 * 1000,
            );
        } catch {
            this.logger.log(
                "Timeout waiting for agent audio to finish playing. Aborting call forward.",
            );
            return;
        }

        // Give caller one last chance to continue conversation
        await new Promise((resolve) => setTimeout(resolve, 2 * 1000));
        if (this.twilioConnections[streamSid].state !== CallState.FORWARDING) {
            this.logger.log("Aborting call forward");
            return;
        }

        // Instruct Twilio to forward this call
        const [number] = event.item.name.match(/\d+/);
        const forwardToNumber = `+${number}`;
        const callSid = this.twilioConnections[streamSid].callSid;
        this.logger.log(`Forwarding call to ${forwardToNumber} (${callSid})`);
        this.twilioService.forwardCall(callSid, forwardToNumber);
    }
}
