import { getMediaStreamUrl, getSystemInstruction } from "./util";

describe("getMediaStreamUrl", () => {
    test("localhost over http", () => {
        const url = getMediaStreamUrl({
            protocol: "http",
            headers: { host: "localhost:3000" },
        });
        expect(url).toEqual("ws://localhost:3000/api/media-stream");
    });

    test("public URL over https", () => {
        const url = getMediaStreamUrl({
            protocol: "https",
            headers: { host: "agents.truthkeep.ai" },
        });
        expect(url).toEqual("wss://agents.truthkeep.ai/api/media-stream");
    });
});

describe("getSystemInstruction", () => {
    const systemInstruction = getSystemInstruction({
        name: "test agent",
        description: "Generic agent description",
    });

    expect(systemInstruction).toEqual(
        `
You are a helpful and professional AI voice assistant.

You are described as "Generic agent description"

Always respond to the user in a friendly, concise, and helpful way.
`.trim(),
    );
});
