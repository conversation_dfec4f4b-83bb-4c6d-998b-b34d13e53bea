import { Agent } from "@prisma/client";
import { Request } from "express";

export function getMediaStreamUrl(
    incomingRequest: Pick<Request, "protocol" | "headers">,
) {
    const {
        protocol,
        headers: { host },
    } = incomingRequest;

    return protocol === "http"
        ? `ws://${host}/api/media-stream`
        : `wss://${host}/api/media-stream`;
}

export function getSystemInstruction<T extends Pick<Agent, "description">>(
    agent: T,
): string {
    if (!agent) return "";

    const descriptionInstruction = getDescriptionInstructions(
        agent.description,
    );

    return [
        "You are a helpful and professional AI voice assistant.",
        descriptionInstruction,
        "Always respond to the user in a friendly, concise, and helpful way.",
    ]
        .filter(Boolean)
        .join("\n\n");
}

function getDescriptionInstructions(description?: string): string {
    if (!description) return "";

    return `You are described as "${description}"`;
}

export async function waitFor<ReturnValue>(
    cb: () => ReturnValue | Promise<ReturnValue>,
    timeout = 2500,
) {
    const endTime = Date.now() + timeout;
    let lastError;
    while (Date.now() < endTime) {
        try {
            const response = await cb();
            if (response) return response;
        } catch (e: unknown) {
            lastError = e;
        }
        await new Promise((r) => setTimeout(r, 100));
    }
    throw lastError;
}
