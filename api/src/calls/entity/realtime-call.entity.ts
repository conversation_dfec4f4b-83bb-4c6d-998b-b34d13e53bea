import { Agent } from "@prisma/client";
import type WebSocket from "ws";

export class RealtimeCall {
    agent: Agent;
    twilioSocket: WebSocket;

    latestMediaTimestamp: number;
    responseStartTimestampTwilio: number;
    markQueue: string[];

    call: {
        From: string;
    };

    state: CallState;

    callSid: string;
}

export enum CallState {
    ACTIVE,
    ENDING,
    FORWARDING,
}
