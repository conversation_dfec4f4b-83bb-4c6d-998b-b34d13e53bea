import { Agent } from "@prisma/client";
import type { WebSocket as WsWebSocket } from "ws";

export class RealtimeCall {
    agent: Agent;
    twilioSocket: WsWebSocket;

    latestMediaTimestamp: number;
    responseStartTimestampTwilio: number;
    markQueue: string[];

    call: {
        From: string;
    };

    state: CallState;

    callSid: string;
}

export enum CallState {
    ACTIVE,
    ENDING,
    FORWARDING,
}
