import { Body, Controller, Logger, <PERSON>, Req, Res } from "@nestjs/common";
import { ApiResponse, ApiTags } from "@nestjs/swagger";

import { Request, Response } from "express";

import { Public } from "../auth/constants";
import { PhoneNumbersService } from "./../phone-numbers/phone-numbers.service";
import { CallsService } from "./calls.service";
import { CreateCallRequestDto } from "./dto/create-call.dto";

@ApiTags("calls")
@Controller("calls")
export class CallsController {
    private logger = new Logger("CallsController");

    constructor(
        private readonly callsService: CallsService,
        private readonly phoneNumbersService: PhoneNumbersService,
    ) {}

    // TODO - protect this route by verifying x-twilio-signature header
    @Public()
    @Post("/incoming-call")
    @ApiResponse({ type: String })
    async incomingCall(
        @Req() req: Request,
        @Body() body: CreateCallRequestDto,
        @Res() res: Response,
    ): Promise<void> {
        await this.phoneNumbersService.findOne(body.To);

        const twiml = this.callsService.getMediaStreamTwiml(req, body);

        this.logger.log(twiml);

        res.type("text/xml");
        res.send(twiml);
    }
}
