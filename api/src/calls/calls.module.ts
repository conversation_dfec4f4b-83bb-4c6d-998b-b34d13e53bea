import { BullModule } from "@nestjs/bullmq";
import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { BullBoardModule } from "@bull-board/nestjs";
import { PrismaService } from "nestjs-prisma";

import { AgentsModule } from "../agents/agents.module";
import { OpenaiModule } from "../openai/openai.module";
import { PhoneNumbersModule } from "../phone-numbers/phone-numbers.module";
import { TwilioModule } from "../twilio/twilio.module";
import { CallsController } from "./calls.controller";
import { CallsGateway } from "./calls.gateway";
import { CallsService } from "./calls.service";

@Module({
    imports: [
        AgentsModule,
        BullBoardModule.forFeature({
            name: "Phone agent events",
            adapter: BullMQAdapter,
        }),
        BullModule.registerQueue({
            name: "Phone agent events",
        }),
        OpenaiModule,
        PhoneNumbersModule,
        TwilioModule,
    ],
    controllers: [CallsController],
    providers: [CallsGateway, CallsService, PrismaService],
})
export class CallsModule {}
