import { Injectable } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import { Role } from "./role.enum";

@Injectable()
export class RolesService {
    constructor(private prisma: PrismaService) {}

    async attachRoleToUser(userId: string, tenantId: string, roleName: Role) {
        const role = await this.prisma.role.findUniqueOrThrow({
            where: {
                name_tenantId: {
                    name: roleName,
                    tenantId: tenantId,
                },
            },
        });

        if (await this.hasUserRole(userId, roleName)) return;

        await this.prisma.rolesOnUsers.create({
            data: {
                userId,
                roleId: role.id,
            },
        });
    }

    async removeRoleFromUser(userId: string, tenantId: string, roleName: Role) {
        const role = await this.prisma.role.findUniqueOrThrow({
            where: {
                name_tenantId: {
                    name: roleName,
                    tenantId: tenantId,
                },
            },
        });

        if (!(await this.hasUserRole(userId, roleName))) return;

        await this.prisma.rolesOnUsers.delete({
            where: {
                userId_roleId: {
                    userId,
                    roleId: role.id,
                },
            },
        });
    }

    async setUserRole(
        userId: string,
        tenantId: string,
        roleName: Role,
        attached: boolean,
    ) {
        return attached
            ? this.attachRoleToUser(userId, tenantId, roleName)
            : this.removeRoleFromUser(userId, tenantId, roleName);
    }

    async getUserRoles(userId: string) {
        return this.prisma.user.findUnique({
            select: {
                roles: {
                    select: {
                        role: true,
                    },
                },
            },
            where: { id: userId },
        });
    }

    async hasUserRole(userId: string, roleName: Role): Promise<boolean> {
        const roles = (await this.getUserRoles(userId)) || { roles: [] };
        return !!roles.roles.find(({ role: { name } }) => roleName === name);
    }
}
