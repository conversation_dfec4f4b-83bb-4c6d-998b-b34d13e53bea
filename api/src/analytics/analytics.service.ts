import { Injectable } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import {
    AnalyticsResponse,
    DailyStats,
    DailyStatsMap,
    SessionWithMessages,
} from "./dto/get-analytics.dto";

@Injectable()
export class AnalyticsService {
    constructor(private readonly prisma: PrismaService) {}

    async getAnalytics(
        tenantId: string,
        days: number = 28,
    ): Promise<AnalyticsResponse> {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        // Get all sessions and their messages within the date range for the specific tenant
        const sessions = await this.prisma.conversationSession.findMany({
            where: {
                createdAt: {
                    gte: startDate,
                },
                agent: {
                    tenantId,
                },
            },
            include: {
                messages: {
                    orderBy: {
                        createdAt: "asc",
                    },
                },
            },
        });

        // Get previous period sessions for comparison (also filtered by tenant)
        const prevStartDate = new Date(startDate);
        prevStartDate.setDate(prevStartDate.getDate() - days);
        const prevSessions = await this.prisma.conversationSession.findMany({
            where: {
                createdAt: {
                    gte: prevStartDate,
                    lt: startDate,
                },
                agent: {
                    tenantId,
                },
            },
            include: {
                messages: {
                    orderBy: {
                        createdAt: "asc",
                    },
                },
            },
        });

        // Calculate daily stats
        const dailyStats = this.calculateDailyStats(sessions, startDate);

        // Calculate total stats
        const totals = this.calculateTotalStats(sessions, prevSessions);

        return {
            dailyStats,
            metrics: [
                {
                    label: "Total Sessions",
                    value: totals.totalSessions,
                    change: totals.sessionsChange,
                    key: "sessions",
                },
                {
                    label: "Total Messages",
                    value: totals.totalMessages,
                    change: totals.messagesChange,
                    key: "messages",
                },
                {
                    label: "Avg Msgs/Session",
                    value: totals.avgMsgs,
                    change: 0, // Not calculating this change as it's a derived metric
                    key: "avgMsgs",
                },
                {
                    label: "Resolved Rate",
                    value: `${totals.resolvedRate}%`,
                    change: totals.resolvedChange,
                    key: "resolved",
                },
                {
                    label: "Avg Duration",
                    value: `${totals.avgDuration}s`,
                    change: totals.durationChange,
                    key: "duration",
                },
            ],
        };
    }

    private calculateDailyStats(
        sessions: SessionWithMessages[],
        startDate: Date,
    ): DailyStats[] {
        const dailyStats = new Map<string, DailyStatsMap>();

        // Initialize all dates in the range
        for (
            let d = new Date(startDate);
            d <= new Date();
            d.setDate(d.getDate() + 1)
        ) {
            const dateStr = d.toISOString().split("T")[0];
            dailyStats.set(dateStr, {
                sessions: 0,
                messages: 0,
                resolvedSessions: 0,
                durations: [],
            });
        }

        // Aggregate stats for each session
        sessions.forEach((session) => {
            const dateStr = session.createdAt.toISOString().split("T")[0];
            const stats = dailyStats.get(dateStr);
            if (!stats) return;

            stats.sessions++;
            stats.messages += session.messages.length;
            if (session.resolved) stats.resolvedSessions++;

            // Calculate session duration
            if (session.messages.length > 1) {
                const duration =
                    (session.messages[
                        session.messages.length - 1
                    ].createdAt.getTime() -
                        session.messages[0].createdAt.getTime()) /
                    1000;
                stats.durations.push(duration);
            }
        });

        // Convert to array and format
        return Array.from(dailyStats.entries()).map(([date, stats]) => ({
            date: new Date(date).toLocaleDateString("en-US", {
                day: "2-digit",
                month: "short",
            }),
            sessions: stats.sessions,
            messages: stats.messages,
            avgMsgs:
                stats.sessions > 0
                    ? Number((stats.messages / stats.sessions).toFixed(1))
                    : 0,
            resolved:
                stats.sessions > 0
                    ? Math.round(
                          (stats.resolvedSessions / stats.sessions) * 100,
                      )
                    : 0,
            duration: Math.round(
                stats.durations.length > 0
                    ? stats.durations.reduce((a, b) => a + b, 0) /
                          stats.durations.length
                    : 0,
            ),
        }));
    }

    private calculateTotalStats(
        sessions: SessionWithMessages[],
        prevSessions: SessionWithMessages[],
    ) {
        const calculateStats = (sessions: SessionWithMessages[]) => {
            const totalSessions = sessions.length;
            const totalMessages = sessions.reduce(
                (sum, s) => sum + s.messages.length,
                0,
            );
            const resolvedSessions = sessions.filter((s) => s.resolved).length;
            const durations = sessions
                .filter((s) => s.messages.length > 1)
                .map(
                    (s) =>
                        (s.messages[s.messages.length - 1].createdAt.getTime() -
                            s.messages[0].createdAt.getTime()) /
                        1000,
                );

            return {
                totalSessions,
                totalMessages,
                resolvedSessions,
                avgDuration:
                    durations.length > 0
                        ? durations.reduce((a, b) => a + b, 0) /
                          durations.length
                        : 0,
            };
        };

        const current = calculateStats(sessions);
        const previous = calculateStats(prevSessions);

        const calculateChange = (current: number, previous: number) =>
            previous > 0
                ? Math.round(((current - previous) / previous) * 100)
                : 0;

        return {
            totalSessions: current.totalSessions,
            totalMessages: current.totalMessages,
            avgMsgs:
                current.totalSessions > 0
                    ? Number(
                          (
                              current.totalMessages / current.totalSessions
                          ).toFixed(2),
                      )
                    : 0,
            resolvedRate:
                current.totalSessions > 0
                    ? Math.round(
                          (current.resolvedSessions / current.totalSessions) *
                              100,
                      )
                    : 0,
            avgDuration: Math.round(current.avgDuration),
            sessionsChange: calculateChange(
                current.totalSessions,
                previous.totalSessions,
            ),
            messagesChange: calculateChange(
                current.totalMessages,
                previous.totalMessages,
            ),
            resolvedChange:
                calculateChange(
                    current.totalSessions > 0
                        ? current.resolvedSessions / current.totalSessions
                        : 0,
                    previous.totalSessions > 0
                        ? previous.resolvedSessions / previous.totalSessions
                        : 0,
                ) * 100,
            durationChange: calculateChange(
                current.avgDuration,
                previous.avgDuration,
            ),
        };
    }
}
