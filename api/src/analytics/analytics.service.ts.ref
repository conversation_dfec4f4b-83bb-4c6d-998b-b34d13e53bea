// This is a reference implementation of the analytics service.
// It is not used in the application, but is provided for reference.
// It is not up to date with the latest changes to the database schema.
// It is not used in the application, but is provided for reference.


import { Injectable } from "@nestjs/common";
import { PrismaService } from "nestjs-prisma";


@Injectable()
export class AnalyticsService {
    constructor(private readonly prisma: PrismaService) {}

    async getAnalytics(days: number = 28) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        // Get daily stats
        const dailyStats = await this.prisma.$queryRaw`
            WITH dates AS (
                SELECT generate_series(
                    date_trunc('day', ${startDate}::timestamp),
                    date_trunc('day', now()::timestamp),
                    '1 day'::interval
                )::date AS date
            ),
            session_durations AS (
                SELECT 
                    cs.id,
                    date_trunc('day', cs."createdAt")::date AS date,
                    EXTRACT(EPOCH FROM (
                        MAX(cm."createdAt") - MIN(cm."createdAt")
                    ))::integer as duration
                FROM "ConversationSession" cs
                LEFT JOIN "ConversationMessage" cm ON cm."sessionId" = cs.id
                WHERE cs."createdAt" >= ${startDate}
                GROUP BY cs.id, date_trunc('day', cs."createdAt")::date
            ),
            daily_data AS (
                SELECT 
                    date_trunc('day', cs."createdAt")::date AS date,
                    COUNT(DISTINCT cs.id)::integer as sessions,
                    COUNT(DISTINCT cm.id)::integer as messages,
                    COUNT(DISTINCT CASE WHEN cs.resolved = true THEN cs.id END)::integer as resolved_sessions,
                    AVG(sd.duration)::integer as avg_duration
                FROM "ConversationSession" cs
                LEFT JOIN "ConversationMessage" cm ON cm."sessionId" = cs.id
                LEFT JOIN session_durations sd ON sd.id = cs.id
                WHERE cs."createdAt" >= ${startDate}
                GROUP BY date_trunc('day', cs."createdAt")::date
            )
            SELECT 
                to_char(d.date, 'DD Mon') as date,
                COALESCE(dd.sessions, 0) as sessions,
                COALESCE(dd.messages, 0) as messages,
                CASE 
                    WHEN dd.sessions > 0 THEN ROUND(CAST(dd.messages AS DECIMAL) / dd.sessions, 1)
                    ELSE 0 
                END as avgMsgs,
                CASE 
                    WHEN dd.sessions > 0 THEN ROUND(CAST(dd.resolved_sessions AS DECIMAL) / dd.sessions * 100)
                    ELSE 0 
                END as resolved,
                ROUND(COALESCE(dd.avg_duration, 0)) as duration
            FROM dates d
            LEFT JOIN daily_data dd ON d.date = dd.date
            ORDER BY d.date;
        `;

        // Get total stats
        const totalStats = await this.prisma.$queryRaw`
            WITH session_durations AS (
                SELECT 
                    cs.id,
                    EXTRACT(EPOCH FROM (
                        MAX(cm."createdAt") - MIN(cm."createdAt")
                    ))::integer as duration
                FROM "ConversationSession" cs
                LEFT JOIN "ConversationMessage" cm ON cm."sessionId" = cs.id
                WHERE cs."createdAt" >= ${startDate}
                GROUP BY cs.id
            ),
            total_data AS (
                SELECT 
                    COUNT(DISTINCT cs.id)::integer as total_sessions,
                    COUNT(DISTINCT cm.id)::integer as total_messages,
                    COUNT(DISTINCT CASE WHEN cs.resolved = true THEN cs.id END)::integer as total_resolved,
                    AVG(sd.duration)::integer as total_avg_duration
                FROM "ConversationSession" cs
                LEFT JOIN "ConversationMessage" cm ON cm."sessionId" = cs.id
                LEFT JOIN session_durations sd ON sd.id = cs.id
                WHERE cs."createdAt" >= ${startDate}
            ),
            prev_session_durations AS (
                SELECT 
                    cs.id,
                    EXTRACT(EPOCH FROM (
                        MAX(cm."createdAt") - MIN(cm."createdAt")
                    ))::integer as duration
                FROM "ConversationSession" cs
                LEFT JOIN "ConversationMessage" cm ON cm."sessionId" = cs.id
                WHERE cs."createdAt" >= ${startDate} - interval '${days} days'
                AND cs."createdAt" < ${startDate}
                GROUP BY cs.id
            ),
            previous_data AS (
                SELECT 
                    COUNT(DISTINCT cs.id)::integer as prev_sessions,
                    COUNT(DISTINCT cm.id)::integer as prev_messages,
                    COUNT(DISTINCT CASE WHEN cs.resolved = true THEN cs.id END)::integer as prev_resolved,
                    AVG(psd.duration)::integer as prev_avg_duration
                FROM "ConversationSession" cs
                LEFT JOIN "ConversationMessage" cm ON cm."sessionId" = cs.id
                LEFT JOIN prev_session_durations psd ON psd.id = cs.id
                WHERE cs."createdAt" >= ${startDate} - interval '${days} days'
                AND cs."createdAt" < ${startDate}
            )
            SELECT 
                total_sessions,
                total_messages,
                CASE 
                    WHEN total_sessions > 0 THEN ROUND(CAST(total_messages AS DECIMAL) / total_sessions, 2)
                    ELSE 0 
                END as avg_msgs,
                CASE 
                    WHEN total_sessions > 0 THEN ROUND(CAST(total_resolved AS DECIMAL) / total_sessions * 100)
                    ELSE 0 
                END as resolved_rate,
                ROUND(total_avg_duration) as avg_duration,
                CASE 
                    WHEN prev_sessions > 0 THEN ROUND((CAST(total_sessions AS DECIMAL) - prev_sessions) / prev_sessions * 100)
                    ELSE 0 
                END as sessions_change,
                CASE 
                    WHEN prev_messages > 0 THEN ROUND((CAST(total_messages AS DECIMAL) - prev_messages) / prev_messages * 100)
                    ELSE 0 
                END as messages_change,
                CASE 
                    WHEN prev_sessions > 0 THEN ROUND(
                        ((CAST(total_resolved AS DECIMAL) / total_sessions) - 
                        (CAST(prev_resolved AS DECIMAL) / prev_sessions)) * 100
                    )
                    ELSE 0 
                END as resolved_change,
                CASE 
                    WHEN prev_avg_duration > 0 THEN ROUND((total_avg_duration - prev_avg_duration) / prev_avg_duration * 100)
                    ELSE 0 
                END as duration_change
            FROM total_data, previous_data;
        `;

        const [totals] = totalStats as any[];

        return {
            dailyStats,
            metrics: [
                {
                    label: 'Total Sessions',
                    value: totals.total_sessions,
                    change: totals.sessions_change,
                    key: 'sessions',
                },
                {
                    label: 'Total Messages',
                    value: totals.total_messages,
                    change: totals.messages_change,
                    key: 'messages',
                },
                {
                    label: 'Avg Msgs/Session',
                    value: totals.avg_msgs,
                    change: 0, // Not calculating this change as it's a derived metric
                    key: 'avgMsgs',
                },
                {
                    label: 'Resolved Rate',
                    value: `${totals.resolved_rate}%`,
                    change: totals.resolved_change,
                    key: 'resolved',
                },
                {
                    label: 'Avg Duration',
                    value: `${totals.avg_duration}s`,
                    change: totals.duration_change,
                    key: 'duration',
                },
            ],
        };
    }
} 