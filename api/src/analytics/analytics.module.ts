import { <PERSON><PERSON><PERSON> } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import { TenantsModule } from "../tenants/tenants.module";
import { AnalyticsController } from "./analytics.controller";
import { AnalyticsService } from "./analytics.service";

@Module({
    imports: [TenantsModule],
    controllers: [AnalyticsController],
    providers: [AnalyticsService, PrismaService],
})
export class AnalyticsModule {}
