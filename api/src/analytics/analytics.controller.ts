import { Controller, Get, Query, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Tenant } from "../tenants/tenant.decorator";
import { TenantGuard } from "../tenants/tenant.guard";
import { AnalyticsService } from "./analytics.service";
import { AnalyticsResponse } from "./dto/get-analytics.dto";

@Controller("analytics")
@ApiTags("analytics")
@UseGuards(TenantGuard)
export class AnalyticsController {
    constructor(private readonly analyticsService: AnalyticsService) {}

    @Get()
    @ApiOperation({ summary: "Get analytics data" })
    @ApiResponse({ status: 200, description: "Returns analytics data" })
    async getAnalytics(
        @Tenant("id") tenantId: string,
        @Query("days") days: string,
    ): Promise<AnalyticsResponse> {
        return this.analyticsService.getAnalytics(tenantId, parseInt(days, 10));
    }
}
