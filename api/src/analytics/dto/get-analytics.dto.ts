import { ConversationMessage, ConversationSession } from "@prisma/client";

export interface DailyStats {
    date: string;
    sessions: number;
    messages: number;
    avgMsgs: number;
    resolved: number;
    duration: number;
}

export interface Metric {
    label: string;
    value: string | number;
    change: number;
    key: string;
}

export interface AnalyticsResponse {
    dailyStats: DailyStats[];
    metrics: Metric[];
}

export interface SessionWithMessages extends ConversationSession {
    messages: ConversationMessage[];
}

export interface DailyStatsMap {
    sessions: number;
    messages: number;
    resolvedSessions: number;
    durations: number[];
}
