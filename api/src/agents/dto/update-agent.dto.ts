import { ApiProperty } from "@nestjs/swagger";

import { AgentActionType } from "@prisma/client";
import { Type } from "class-transformer";
import {
    IsArray,
    IsEnum,
    IsOptional,
    IsPhoneNumber,
    IsString,
    ValidateNested,
} from "class-validator";

class CreateActionDto {
    // eslint-disable-next-line @darraghor/nestjs-typed/all-properties-have-explicit-defined
    @IsEnum(AgentActionType)
    @ApiProperty({
        required: true,
    })
    type: AgentActionType;

    @IsString()
    @IsOptional()
    webhookUrl?: string;

    @IsString()
    @IsOptional()
    webhookBody?: string;

    @IsPhoneNumber()
    @IsOptional()
    forwardToNumber?: string;
}

class CreateEventDto {
    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    description: string;

    @IsArray()
    @ApiProperty({
        type: [CreateActionDto],
        isArray: true,
    })
    @Type(() => CreateActionDto)
    @ValidateNested({ each: true })
    actions: CreateActionDto[];
}

export class UpdateAgentDto {
    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    description: string;

    @IsPhoneNumber()
    @IsOptional()
    phoneNumber?: string;

    @IsArray()
    @ApiProperty({
        type: [CreateEventDto],
        isArray: true,
    })
    @Type(() => CreateEventDto)
    @ValidateNested({ each: true })
    @IsOptional()
    events: CreateEventDto[];
}
