import { AgentType } from "@prisma/client";
import { IsEnum, <PERSON>Optional, IsPhoneNumber, IsString } from "class-validator";

export class CreateAgentDto {
    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    description: string;

    // eslint-disable-next-line @darraghor/nestjs-typed/all-properties-have-explicit-defined
    @IsEnum(AgentType)
    type!: AgentType;

    @IsPhoneNumber()
    @IsOptional()
    phoneNumber?: string;
}

export class CreateAgentResponseDto {
    id: string;
    name: string;
    type: AgentType;
    createdAt: Date;
}
