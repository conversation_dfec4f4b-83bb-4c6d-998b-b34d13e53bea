import { ApiProperty } from "@nestjs/swagger";

import { AgentActionType } from "@prisma/client";
import { Type } from "class-transformer";
import {
    IsArray,
    IsEnum,
    IsJSON,
    IsOptional,
    IsPhoneNumber,
    IsString,
    IsUrl,
    ValidateNested,
} from "class-validator";

class CreateActionDto {
    // eslint-disable-next-line @darraghor/nestjs-typed/all-properties-have-explicit-defined
    @IsEnum(AgentActionType)
    @ApiProperty({
        required: true,
    })
    type: AgentActionType;

    @IsUrl()
    @IsOptional()
    webhookUrl?: string;

    @IsOptional()
    @IsJSON()
    webhookBody?: string;

    @IsPhoneNumber()
    @IsOptional()
    forwardToNumber?: string;
}

export class CreateEventDto {
    @IsString()
    name: string;

    @IsString()
    @IsOptional()
    description: string;

    @IsArray()
    @ApiProperty({
        type: [CreateActionDto],
        isArray: true,
    })
    @Type(() => CreateActionDto)
    @ValidateNested({ each: true })
    actions: CreateActionDto[];
}
