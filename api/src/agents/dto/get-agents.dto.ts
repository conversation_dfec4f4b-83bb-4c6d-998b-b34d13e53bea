import { ApiProperty } from "@nestjs/swagger";

import { AgentActionType } from "@prisma/client";
import { IsEnum, IsOptional, IsString, IsUrl } from "class-validator";

class ActionDto {
    // eslint-disable-next-line @darraghor/nestjs-typed/all-properties-have-explicit-defined
    @ApiProperty({
        enum: AgentActionType,
        required: true,
    })
    @IsEnum(AgentActionType)
    type: AgentActionType;

    @IsUrl()
    @IsOptional()
    webhookUrl?: string;

    @IsOptional()
    @IsString()
    webhookBody?: string;
}

class EventDto {
    name: string;
    description?: string;
    actions: ActionDto[];
}

class AgentDto {
    id: string;
    name: string;
    description?: string;
    phoneNumber?: string;
    events: EventDto[];
}

export class GetAgentsResponseDto {
    items: AgentDto[];
}
