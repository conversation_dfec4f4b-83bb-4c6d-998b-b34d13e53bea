// import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Req,
    Res,
    Query,
    UseGuards,
} from "@nestjs/common";
import {
    ApiCreatedResponse,
    ApiNoContentResponse,
    ApiOperation,
    ApiResponse,
    ApiTags,
} from "@nestjs/swagger";

import { Request, Response } from "express";

import { Tenant } from "../tenants/tenant.decorator";
import { TenantGuard } from "../tenants/tenant.guard";
import { AgentsService } from "./agents.service";
import { CreateAgentDto, CreateAgentResponseDto } from "./dto/create-agent.dto";
import { CreateEventDto } from "./dto/create-event.dto";
import { PostFilesRequestDto } from "./dto/files.dto";
import { GetAgentsResponseDto } from "./dto/get-agents.dto";
import { UpdateAgentDto } from "./dto/update-agent.dto";

@Controller("agents")
@ApiTags("agents")
@UseGuards(TenantGuard)
export class AgentsController {
    constructor(private readonly agentsService: AgentsService) {}

    @Post()
    @ApiResponse({ status: 201, type: CreateAgentResponseDto })
    async create(
        @Req() req: Request,
        @Tenant("id") tenantId: string,
        @Body() createAgentDto: CreateAgentDto,
    ): Promise<CreateAgentResponseDto> {
        return this.agentsService.create(
            req.user["userId"],
            tenantId,
            createAgentDto,
        );
    }

    @Get()
    @ApiResponse({ status: 200, type: GetAgentsResponseDto })
    async findAll(
        @Req() req: Request,
        @Tenant("id") tenantId: string,
    ): Promise<GetAgentsResponseDto> {
        const agents = await this.agentsService.findAll(
            req.user["userId"],
            tenantId,
        );

        return {
            items: agents,
        };
    }

    @Put(":id")
    @ApiNoContentResponse()
    async update(
        @Req() req: Request,
        @Tenant("id") tenantId: string,
        @Param("id") id: string,
        @Body() updateAgentDto: UpdateAgentDto,
        @Res() res: Response,
    ): Promise<void> {
        await this.agentsService.update(
            req.user["userId"],
            tenantId,
            id,
            updateAgentDto,
        );
        res.sendStatus(204);
    }

    @Post(":id/events")
    @ApiCreatedResponse()
    async createEvent(
        @Req() req: Request,
        @Tenant("id") tenantId: string,
        @Param("id") id: string,
        @Body() eventData: CreateEventDto,
        @Res() res: Response,
    ): Promise<void> {
        await this.agentsService.createEvent(
            req.user["userId"],
            tenantId,
            id,
            eventData,
        );
        res.sendStatus(204);
    }

    @Post(":id/files")
    @ApiCreatedResponse()
    @ApiOperation({
        description:
            "Links files to this agent. Files must already be uploaded and processed.",
    })
    async attachFiles(
        @Req() req: Request,
        @Param("id") id: string,
        @Body() postFilesRequestDto: PostFilesRequestDto,
    ): Promise<void> {
        await this.agentsService.attachFiles(id, postFilesRequestDto.files);
    }

    @Delete(":id")
    @ApiNoContentResponse()
    async remove(
        @Req() req: Request,
        @Tenant("id") tenantId: string,
        @Param("id") id: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.agentsService.remove(req.user["userId"], tenantId, id);
        res.sendStatus(204);
    }

    @Delete(":id/events/:eventId")
    @ApiNoContentResponse()
    async removeEvent(
        @Req() req: Request,
        @Tenant("id") tenantId: string,
        @Param("id") id: string,
        @Param("eventId") eventId: string,
        @Res() res: Response,
    ): Promise<void> {
        await this.agentsService.removeEvent(
            req.user["userId"],
            tenantId,
            id,
            eventId,
        );
        res.sendStatus(204);
    }

    @Get("searchable-entities")
    @ApiResponse({
        status: 200,
        description: "List of unique searchable entities with their metadata",
        schema: {
            type: "array",
            items: {
                type: "object",
                properties: {
                    file_name: { type: "string" },
                    pages: { type: "number" },
                    public: { type: "boolean" },
                    document_reference_count: { type: "number" },
                },
            },
        },
    })
    async getSearchableEntities(
        @Tenant("id") tenantId: string,
        @Query("sortField") sortField?: string,
        @Query("sortDirection") sortDirection?: "asc" | "desc",
    ): Promise<
        Array<{
            file_name: string;
            pages: number;
            public: boolean;
            document_reference_count: number;
        }>
    > {
        return this.agentsService.getSearchableEntities(
            tenantId,
            sortField,
            sortDirection,
        );
    }
}
