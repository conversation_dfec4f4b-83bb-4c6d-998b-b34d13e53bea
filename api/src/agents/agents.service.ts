import { Injectable, NotFoundException } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";
import { Embedding } from "openai/resources/embeddings";

import { CreateAgentDto } from "./dto/create-agent.dto";
import { CreateEventDto } from "./dto/create-event.dto";
import { UpdateAgentDto } from "./dto/update-agent.dto";

@Injectable()
export class AgentsService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        userId: string,
        tenantId: string,
        createAgentDto: CreateAgentDto,
    ) {
        const { name, description, type, phoneNumber, isPrivate } =
            createAgentDto;
        const agent = await this.prisma.agent.create({
            data: {
                name,
                description,
                type,
                phoneNumber: type === "PHONE" ? phoneNumber : null,
                userId,
                tenantId,
                isPrivate,
            },
        });
        return agent;
    }

    async findAll(userId: string, tenantId: string) {
        const agents = await this.prisma.agent.findMany({
            where: {
                tenantId,
            },
            include: {
                events: {
                    include: {
                        actions: {
                            include: {
                                agentAction: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: "desc",
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });

        const agentsWithActionsFlattened = agents.map(
            ({ events, ...agent }) => ({
                ...agent,
                events: events.map(({ actions, ...event }) => ({
                    ...event,
                    actions: actions.map((action) => action.agentAction),
                })),
            }),
        );

        return agentsWithActionsFlattened;
    }

    async update(
        userId: string,
        tenantId: string,
        id: string,
        updateAgentDto: UpdateAgentDto,
    ) {
        const agent = await this.prisma.agent.findUnique({
            select: {
                type: true,
            },
            where: {
                id,
                tenantId,
            },
        });

        // Prevent assigning a phone number to a non-phone agent
        if (agent.type !== "PHONE") {
            delete updateAgentDto.phoneNumber;
        }

        // Transaction to manage creating/updating/deleting nested entities within the agent
        await this.prisma.$transaction([
            // First delete all existing events on the agent
            this.prisma.agentEvent.deleteMany({ where: { agentId: id } }),

            // Update agent fields and create any new events
            this.prisma.agent.update({
                where: {
                    id,
                    userId,
                    tenantId,
                },
                data: {
                    name: updateAgentDto.name,
                    description: updateAgentDto.description,
                    phoneNumber: updateAgentDto.phoneNumber ?? null,
                    events: {
                        create: updateAgentDto.events?.map((newEvent) => ({
                            name: newEvent.name,
                            description: newEvent.description,
                            actions: {
                                create: newEvent.actions?.map((newAction) => ({
                                    agentAction: {
                                        create: {
                                            type: newAction.type,
                                            webhookUrl: newAction.webhookUrl,
                                            webhookBody: newAction.webhookBody,
                                            forwardToNumber:
                                                newAction.forwardToNumber,
                                        },
                                    },
                                })),
                            },
                        })),
                    },
                },
            }),
        ]);
    }

    async createEvent(
        userId: string,
        tenantId: string,
        agentId: string,
        event: CreateEventDto,
    ) {
        // Check that the user owns the agent
        await this.prisma.agent.findUniqueOrThrow({
            where: {
                userId,
                tenantId,
                id: agentId,
            },
        });

        const { name, description, actions } = event;

        await this.prisma.agentEvent.create({
            data: {
                agentId,
                name,
                description,
                actions: {
                    create: actions.map((newAction) => ({
                        agentAction: {
                            create: {
                                type: newAction.type,
                                webhookUrl: newAction.webhookUrl,
                                webhookBody: newAction.webhookBody,
                                forwardToNumber: newAction.forwardToNumber,
                            },
                        },
                    })),
                },
            },
        });
    }

    async remove(userId: string, tenantId: string, id: string) {
        await this.prisma.agent.delete({
            where: { tenantId, id },
        });
    }

    async removeEvent(
        userId: string,
        tenantId: string,
        agentId: string,
        eventId: string,
    ) {
        await this.prisma.agentEvent.delete({
            where: {
                agentId,
                id: eventId,
                agent: {
                    tenantId,
                },
            },
        });
    }

    async getPhoneAgent(phoneNumber: string, tenantId: string) {
        return this.prisma.agent.findFirst({
            where: {
                type: "PHONE",
                phoneNumber,
                tenantId,
            },
            include: {
                events: {
                    include: {
                        actions: {
                            include: {
                                agentAction: true,
                            },
                        },
                    },
                },
            },
        });
    }

    async getAgent(id: string, tenantId?: string) {
        const agent = await this.prisma.agent.findUnique({
            where: {
                id,
                ...(tenantId && { tenantId }),
            },
            include: {
                tenant: true,
                events: {
                    include: {
                        actions: {
                            include: {
                                agentAction: true,
                            },
                        },
                    },
                },
            },
        });

        if (!agent) {
            throw new NotFoundException(`Agent with ID ${id} not found`);
        }

        return agent;
    }

    async attachFiles(agentId: string, files: string[]) {
        // TODO verify integrity of file names here
        // is it possible to verify this with postgres constraints?
        // searchable_entity_file_name matches some value in searchable_entity
        await this.prisma.searchableEntitiesOnAgents.createMany({
            data: files.map((searchable_entity_file_name) => ({
                agentId,
                searchable_entity_file_name,
            })),
        });
    }

    async hasEmbeddings(agentId: string) {
        const embedding =
            await this.prisma.searchableEntitiesOnAgents.findFirst({
                where: {
                    agentId,
                },
            });

        return !!embedding;
    }

    async semanticSearch(
        agentId: string,
        embedding: Embedding,
        limit: number,
    ): Promise<
        {
            id;
            document_id;
            file_name;
            page_number;
            embedded_content;
            similarity;
        }[]
    > {
        return this.prisma.$queryRaw`
            SELECT
                se.id,
                se.document_id,
                se.file_name,
                se.page_number,
                se.embedded_content,
                se.embedding <=> ${embedding.embedding}::vector as similarity
            FROM searchable_entity se
            JOIN "SearchableEntitiesOnAgents" seoa ON seoa.searchable_entity_file_name = se.file_name
            JOIN "Agent" a ON a.id = seoa."agentId"
            WHERE seoa."agentId" = ${agentId}
            AND se.tenant_id = a."tenantId"
            ORDER BY se.embedding <=> ${embedding.embedding}::vector
            LIMIT ${limit}
        `;
    }

    async textSearch(
        agentId: string,
        query: string,
        limit: number,
    ): Promise<
        {
            id;
            document_id;
            file_name;
            page_number;
            embedded_content;
            similarity;
        }[]
    > {
        console.log(`Text search query: "${query}" for agent: ${agentId}`);

        // Create a more flexible query using OR instead of AND
        const words = query.split(/\s+/).filter((word) => word.length > 2);
        const orQuery = words.join(" | ");

        console.log(`OR query: ${orQuery}`);

        return this.prisma.$queryRaw`
            SELECT 
                se.id,
                se.document_id,
                se.file_name,
                se.page_number,
                se.embedded_content,
                ts_rank_cd(se.search_vector, to_tsquery('english', ${orQuery})) as similarity
            FROM searchable_entity se
            JOIN "SearchableEntitiesOnAgents" seoa ON seoa.searchable_entity_file_name = se.file_name
            JOIN "Agent" a ON a.id = seoa."agentId"
            WHERE seoa."agentId" = ${agentId}
            AND se.tenant_id = a."tenantId"
            AND se.search_vector @@ to_tsquery('english', ${orQuery})
            ORDER BY similarity DESC
            LIMIT ${limit}
        `;
    }

    async getSearchableEntities(
        tenantId: string,
        sortField: string = "file_name",
        sortDirection: "asc" | "desc" = "asc",
    ): Promise<
        Array<{
            file_name: string;
            pages: number;
            public: boolean;
            document_reference_count: number;
        }>
    > {
        // Validate sort field
        const validSortFields = [
            "file_name",
            "public",
            "pages",
            "document_reference_count",
        ];
        if (!validSortFields.includes(sortField)) {
            sortField = "file_name";
        }

        // Map frontend field names to database field names
        const fieldMapping: Record<string, string> = {
            file_name: "file_name",
            public: "public",
            pages: "MAX(page_number)",
            document_reference_count: "MAX(document_reference_count)",
        };

        const dbSortField = fieldMapping[sortField] || "file_name";
        const orderDirection = sortDirection === "asc" ? "ASC" : "DESC";

        // Use raw SQL for complete sorting control
        const query = `
            SELECT 
                file_name,
                public,
                MAX(page_number) as max_page_number,
                MAX(document_reference_count) as max_document_reference_count
            FROM searchable_entity
            WHERE tenant_id = $1
            GROUP BY file_name, public
            ORDER BY ${dbSortField} ${orderDirection}
        `;

        const results = (await this.prisma.$queryRawUnsafe(
            query,
            tenantId,
        )) as Array<{
            file_name: string;
            max_page_number: number;
            public: boolean;
            max_document_reference_count: number;
        }>;

        return results.map((result) => ({
            file_name: result.file_name,
            pages: result.max_page_number || 0,
            public: result.public,
            document_reference_count: result.max_document_reference_count,
        }));
    }

    async incrementReferenceCounts(entityIds: number[]): Promise<void> {
        if (entityIds.length === 0) return;

        // Get the entities to find their document_ids
        const entities = await this.prisma.searchable_entity.findMany({
            where: {
                id: { in: entityIds },
            },
            select: {
                id: true,
                document_id: true,
            },
        });

        if (entities.length === 0) return;

        // Get unique document_ids
        const documentIds = [
            ...new Set(entities.map((e) => e.document_id).filter(Boolean)),
        ];

        await this.prisma.$transaction([
            this.prisma.searchable_entity.updateMany({
                where: { id: { in: entityIds } },
                data: { chunk_reference_count: { increment: 1 } },
            }),
            this.prisma.searchable_entity.updateMany({
                where: {
                    document_id: { in: documentIds },
                },
                data: { document_reference_count: { increment: 1 } },
            }),
        ]);
    }
}
