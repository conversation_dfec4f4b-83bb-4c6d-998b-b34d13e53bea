import { <PERSON>du<PERSON> } from "@nestjs/common";

import { PrismaService } from "nestjs-prisma";

import { TenantsModule } from "../tenants/tenants.module";
import { AgentsController } from "./agents.controller";
import { AgentsService } from "./agents.service";

@Module({
    imports: [TenantsModule],
    controllers: [AgentsController],
    providers: [AgentsService, PrismaService],
    exports: [AgentsService],
})
export class AgentsModule {}
