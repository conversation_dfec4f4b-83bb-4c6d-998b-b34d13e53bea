import { Injectable, Logger } from "@nestjs/common";

import { Contact } from "./entity/contact";
import { HubspotClientServiceInterface } from "./hubspot-client.service.interface";

@Injectable()
export class HubspotClientService implements HubspotClientServiceInterface {
    private logger = new Logger("HubspotClientService");

    async searchContacts({
        query,
        limit,
        filterGroups,
    }: {
        query: string;
        limit: number;
        filterGroups: { propertyName: string; operator: "EQ"; value: string }[];
    }): Promise<{ results: { id: string }[] }> {
        this.logger.log("Searching contacts");
        try {
            const result = await fetch(
                "https://api.hubapi.com/crm/v3/objects/contacts/search",
                {
                    method: "POST",
                    body: JSON.stringify({ query, limit, filterGroups }),
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${process.env.HUBSPOT_ACCESS_TOKEN}`,
                    },
                },
            );
            if (!result.ok) {
                throw new Error(
                    `Failed to search contacts: ${result.statusText}`,
                );
            }
            const data = await result.json();
            return data;
        } catch (error) {
            this.logger.error(`Error searching contacts ${error}`);
            throw error;
        }
    }

    async createContact(contact: Contact): Promise<{ id: string }> {
        this.logger.log(`Creating contact "${JSON.stringify(contact)}"`);
        try {
            const result = await fetch(
                "https://api.hubapi.com/crm/v3/objects/contacts",
                {
                    method: "POST",
                    body: JSON.stringify({ properties: contact }),
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${process.env.HUBSPOT_ACCESS_TOKEN}`,
                    },
                },
            );
            if (!result.ok) {
                throw new Error(
                    `Failed to create contact: ${result.statusText}`,
                );
            }
            const data = await result.json();
            this.logger.log(`Created contact "${JSON.stringify(data)}"`);
            return data;
        } catch (error) {
            this.logger.error(
                `Error creating contact "${JSON.stringify(contact)}": ${error}`,
            );
            throw error;
        }
    }

    async createNote(
        body: string,
        { contactId }: { contactId: string },
    ): Promise<{ id: string }> {
        this.logger.log(`Creating note for contact ${contactId}`);

        try {
            const result = await fetch(
                "https://api.hubapi.com/crm/v3/objects/notes",
                {
                    method: "POST",
                    body: JSON.stringify({
                        properties: {
                            hs_timestamp: Date.now(),
                            hs_note_body: body,
                        },
                        associations: [
                            {
                                types: [
                                    {
                                        associationCategory: "HUBSPOT_DEFINED",
                                        associationTypeId: 202, // Note to contact
                                    },
                                ],
                                to: {
                                    id: contactId,
                                },
                            },
                        ],
                    }),
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${process.env.HUBSPOT_ACCESS_TOKEN}`,
                    },
                },
            );

            if (!result.ok) {
                throw new Error(
                    `Failed to create note: ${await result.text()}`,
                );
            }

            const data = await result.json();
            this.logger.log("Created note");
            return data;
        } catch (error) {
            this.logger.error(
                `Error creating note "${body.slice(0, 100)}": ${error}`,
            );
            throw error;
        }
    }
}
