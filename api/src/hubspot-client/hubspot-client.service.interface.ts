import { Contact } from "./entity/contact";

export interface HubspotClientServiceInterface {
    searchContacts({
        query,
        limit,
        filterGroups,
    }: {
        query: string;
        limit: number;
        filterGroups: {
            propertyName: string;
            operator: "EQ";
            value: string;
        }[];
    }): Promise<{ results: { id: string }[] }>;

    createContact(contact: Contact): Promise<{ id: string }>;

    createNote(
        body: string,
        { contactId }: { contactId: string },
    ): Promise<{ id: string }>;
}
