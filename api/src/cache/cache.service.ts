import { Injectable, Logger } from "@nestjs/common";

import { <PERSON>ache, CacheEntry } from "@epic-web/cachified";
import { PrismaService } from "nestjs-prisma";

@Injectable()
export class CacheService {
    private logger = new Logger("CacheService");

    constructor(private readonly prisma: PrismaService) {}

    cache: Cache = {
        get: async (key: string) => {
            const cacheRecord = await this.prisma.requestCache.findFirst({
                where: { key },
            });

            if (!cacheRecord) return;

            const result = {
                metadata: JSON.parse(cacheRecord.metadata),
                value: JSON.parse(cacheRecord.value),
            };

            this.logger.log(`Using cached value for "${key}"`);
            return result;
        },

        set: (key: string, { value, metadata }: CacheEntry) => {
            this.logger.log(`Caching "${key}"`);
            return this.prisma.requestCache.upsert({
                where: { key },
                update: {
                    value: JSON.stringify(value),
                    metadata: JSON.stringify(metadata),
                },
                create: {
                    key,
                    value: JSON.stringify(value),
                    metadata: JSON.stringify(metadata),
                },
            });
        },

        delete: (key: string) => {
            this.logger.log(`Removing "${key}" from cache`);
            return this.prisma.requestCache.delete({ where: { key } });
        },
    };
}
