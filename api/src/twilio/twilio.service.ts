import { Injectable } from "@nestjs/common";

import cachified from "@epic-web/cachified";
import * as VoiceResponse from "twilio/lib/twiml/VoiceResponse";

import { CacheService } from "../cache/cache.service";
import { TwilioApiService } from "../twilio-api/twilio-api.service";

const ACTIVE_PHONE_NUMBERS_TTL = 5 * 60 * 1000;
const ACTIVE_PHONE_NUMBERS_SWR = 1 * 60 * 1000;

@Injectable()
export class TwilioService {
    constructor(
        private readonly cacheService: CacheService,
        private readonly twilioApiService: TwilioApiService,
    ) {}

    async getActiveNumbers() {
        return cachified({
            key: "TWILIO_ACTIVE_NUMBERS",
            cache: this.cacheService.cache,
            getFreshValue: () => this.twilioApiService.getActiveNumbers(),
            ttl: ACTIVE_PHONE_NUMBERS_TTL,
            swr: ACTIVE_PHONE_NUMBERS_SWR,
        });
    }

    forwardCall(callSid: string, forwardToNumber: string) {
        const twiml = new VoiceResponse();
        twiml.dial(forwardToNumber);

        this.twilioApiService.updateCall(callSid, twiml.toString());
    }
}
