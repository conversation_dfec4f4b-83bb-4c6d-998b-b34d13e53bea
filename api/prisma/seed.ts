import { PrismaClient } from "@prisma/client";
import { hashSync } from "bcrypt";
import { parseArgs } from "util";
import { isStrongPassword } from "validator";

const prisma = new PrismaClient();

const options = {
    environment: { type: "string" as const },
};

async function main(args) {
    const {
        values: { environment },
    } = parseArgs({ args, options, allowPositionals: true });
    if (environment !== "test" && !!environment) {
        console.error(`Unrecognized seed environment option "${environment}"`);
        return;
    }

    // Create default tenant
    const defaultTenant = await prisma.tenant.upsert({
        where: {
            slug: "sifive",
        },
        update: {
            name: "SiFive",
            description: "SiFive tenant for the application",
        },
        create: {
            id: "sifive",
            name: "SiFive",
            slug: "sifive",
            description: "SiFive tenant for the application",
            systemInstructionTemplate: `You are '{{tenantName}} Support', {{tenantName}}'s AI assistant. {{tenantName}} is a leading company serving customers with innovative solutions.

Core identity:
- Role: {{agentDescription}}
- Speak as "we/our company" with internal knowledge
- When talking about {{tenantName}}, use "we/our company" instead of "they/their"
- Professional, friendly tone representing {{tenantName}}'s interests

Key context:
- Products: RISC-V cores (Performance/Intelligence/Essential), OpenFive custom SoCs, HiFive dev boards
- Founded 2015, CEO Patrick Little, pioneered commercial RISC-V
- Partners: Intel, Samsung, Renesas, Qualcomm

CRITICAL: Remember to use toolcalls to respond. Search company knowledge, check documentation`,
            settings: {},
        },
    });

    // Add admin role
    const adminRole = await prisma.role.upsert({
        where: {
            name_tenantId: {
                name: "admin",
                tenantId: defaultTenant.id,
            },
        },
        update: {
            name: "admin",
            tenantId: defaultTenant.id,
        },
        create: {
            name: "admin",
            tenantId: defaultTenant.id,
        },
    });

    // Create admin user
    if (!isStrongPassword(process.env["ADMIN_PASS"])) {
        throw new Error(`
            ADMIN_PASS must be a strong password with at least
                * 8 characters
                * 1 lowercase letter
                * 1 uppercase letter
                * 1 number
                * 1 symbol
            See "isStrongPassword" on the page https://www.npmjs.com/package/validator for details.
            `);
    }

    const adminUser = await prisma.user.upsert({
        where: {
            username: "admin",
        },
        update: {
            username: "admin",
            email: "<EMAIL>",
            passwordHash: hashSync(process.env["ADMIN_PASS"], 10),
            tenantId: defaultTenant.id,
        },
        create: {
            username: "admin",
            email: "<EMAIL>",
            passwordHash: hashSync(process.env["ADMIN_PASS"], 10),
            tenantId: defaultTenant.id,
        },
        omit: {
            passwordHash: true,
        },
    });
    console.log({ adminUser });

    // Create second tenant
    const safranTenant = await prisma.tenant.upsert({
        where: {
            slug: "safran",
        },
        update: {
            name: "Safran",
            description: "Safran tenant",
        },
        create: {
            id: "safran",
            name: "Safran",
            slug: "safran",
            description: "Safran tenant",
            settings: {},
        },
    });

    const safranRole = await prisma.role.upsert({
        where: {
            name_tenantId: {
                name: "safran-pilot",
                tenantId: safranTenant.id,
            },
        },
        update: {
            name: "safran-pilot",
            tenantId: safranTenant.id,
        },
        create: {
            name: "safran-pilot",
            tenantId: safranTenant.id,
        },
    });

    const safranUser = await prisma.user.upsert({
        where: {
            username: "safran-pilot",
        },
        update: {
            tenantId: safranTenant.id,
        },
        create: {
            username: "safran-pilot",
            email: "<EMAIL>",
            passwordHash: hashSync("changeME1@!", 10),
            tenantId: safranTenant.id,
        },
        omit: {
            passwordHash: true,
        },
    });

    await prisma.rolesOnUsers.upsert({
        where: {
            userId_roleId: {
                userId: safranUser.id,
                roleId: safranRole.id,
            },
        },
        update: {},
        create: {
            userId: safranUser.id,
            roleId: safranRole.id,
        },
    });

    const anthonyUser = await prisma.user.upsert({
        where: {
            username: "anthony",
        },
        update: {
            username: "anthony",
            email: "<EMAIL>",
            passwordHash: hashSync(process.env["ADMIN_PASS"], 10),
            tenantId: defaultTenant.id,
        },
        create: {
            username: "anthony",
            email: "<EMAIL>",
            passwordHash: hashSync(process.env["ADMIN_PASS"], 10),
            tenantId: defaultTenant.id,
        },
        omit: {
            passwordHash: true,
        },
    });
    console.log({ anthonyUser });

    // Attach admin role to admin users
    await prisma.rolesOnUsers.upsert({
        where: {
            userId_roleId: {
                userId: adminUser.id,
                roleId: adminRole.id,
            },
        },
        update: {},
        create: {
            userId: adminUser.id,
            roleId: adminRole.id,
        },
    });

    await prisma.rolesOnUsers.upsert({
        where: {
            userId_roleId: {
                userId: anthonyUser.id,
                roleId: adminRole.id,
            },
        },
        update: {},
        create: {
            userId: anthonyUser.id,
            roleId: adminRole.id,
        },
    });

    // Add SiFive pilot role
    const sifiveRole = await prisma.role.upsert({
        where: {
            name_tenantId: {
                name: "sifive-pilot",
                tenantId: defaultTenant.id,
            },
        },
        update: {
            name: "sifive-pilot",
            tenantId: defaultTenant.id,
        },
        create: {
            name: "sifive-pilot",
            tenantId: defaultTenant.id,
        },
    });

    // Create sifive user

    const sifiveUser = await prisma.user.upsert({
        where: {
            username: "sifive-pilot",
        },
        update: {
            tenantId: defaultTenant.id,
        },
        create: {
            username: "sifive-pilot",
            email: "<EMAIL>",
            passwordHash: hashSync("changeME1@!", 10),
            tenantId: defaultTenant.id,
        },
        omit: {
            passwordHash: true,
        },
    });

    await prisma.rolesOnUsers.upsert({
        where: {
            userId_roleId: {
                userId: sifiveUser.id,
                roleId: sifiveRole.id,
            },
        },
        update: {},
        create: {
            userId: sifiveUser.id,
            roleId: sifiveRole.id,
        },
    });

    console.log({ sifiveUser });

    // SiFive pilot chat Agents
    const sifiveInternalChatAgent = await prisma.agent.upsert({
        where: {
            id: "sifive-internal-chat-agent",
        },
        update: {
            tenantId: defaultTenant.id,
        },
        create: {
            id: "sifive-internal-chat-agent",
            name: "Internal Chat Agent",
            type: "CHAT",
            description: "Internal chat agent for SiFive pilot",
            userId: sifiveUser.id,
            tenantId: defaultTenant.id,
        },
    });

    const sifiveExternalChatAgent = await prisma.agent.upsert({
        where: {
            id: "sifive-external-chat-agent",
        },
        update: {
            tenantId: defaultTenant.id,
        },
        create: {
            id: "sifive-external-chat-agent",
            name: "External Chat Agent",
            type: "CHAT",
            description: "External chat agent for SiFive pilot",
            userId: sifiveUser.id,
            isPrivate: false,
            tenantId: defaultTenant.id,
            unresolvedUrl: "https://www.sifive.com/contact-sales",
        },
    });

    console.log(sifiveInternalChatAgent);
    console.log(sifiveExternalChatAgent);

    // Safran pilot chat Agents
    const safranInternalChatAgent = await prisma.agent.upsert({
        where: {
            id: "safran-internal-chat-agent",
        },
        update: {
            tenantId: safranTenant.id,
        },
        create: {
            id: "safran-internal-chat-agent",
            name: "Internal Chat Agent",
            type: "CHAT",
            description: "Internal chat agent for Safran pilot",
            userId: safranUser.id,
            tenantId: safranTenant.id,
        },
    });

    const safranExternalChatAgent = await prisma.agent.upsert({
        where: {
            id: "safran-external-chat-agent",
        },
        update: {
            tenantId: safranTenant.id,
        },
        create: {
            id: "safran-external-chat-agent",
            name: "External Chat Agent",
            type: "CHAT",
            description: "External chat agent for Safran pilot",
            userId: safranUser.id,
            isPrivate: false,
            tenantId: safranTenant.id,
            unresolvedUrl: "https://www.safran-group.com/contact",
        },
    });

    console.log(safranInternalChatAgent);
    console.log(safranExternalChatAgent);

    if (environment !== "test") return;

    console.log("\nAdding additional test data\n");

    const emailAgent = await prisma.agent.upsert({
        where: {
            id: "test-email-agent",
        },
        update: {
            userId: adminUser.id,
            tenantId: defaultTenant.id,
        },
        create: {
            id: "test-email-agent",
            name: "Customer Support Email Agent",
            type: "EMAIL",
            description: "Handles incoming customer support emails",
            userId: adminUser.id,
            tenantId: defaultTenant.id,
        },
    });

    console.log(emailAgent);

    const phoneAgent = await prisma.agent.upsert({
        where: {
            id: "test-phone-agent",
        },
        update: {
            userId: adminUser.id,
            phoneNumber: "+14165550011",
            tenantId: defaultTenant.id,
        },
        create: {
            id: "test-phone-agent",
            name: "Sales Inquiry Phone Agent",
            type: "PHONE",
            description: "Manages incoming sales calls",
            phoneNumber: "+14165550011",
            userId: adminUser.id,
            tenantId: defaultTenant.id,
        },
    });

    console.log(phoneAgent);

    const chatAgent = await prisma.agent.upsert({
        where: {
            id: "test-chat-agent",
        },
        update: {
            userId: adminUser.id,
            tenantId: defaultTenant.id,
        },
        create: {
            id: "test-chat-agent",
            name: "Live Chat Support",
            type: "CHAT",
            description: "Handles website live chat support",
            userId: adminUser.id,
            tenantId: defaultTenant.id,
        },
    });

    console.log(chatAgent);
}

main(process.argv)
    .then(async () => {
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
