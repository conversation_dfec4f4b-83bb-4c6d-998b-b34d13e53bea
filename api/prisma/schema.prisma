generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions"]
  binaryTargets   = ["native", "linux-arm64-openssl-1.1.x"]
}

datasource db {
  provider   = "postgres"
  url        = env("DATABASE_URL")
  extensions = [vector]
}

model Tenant {
  id                        String              @id @default(cuid(2))
  name                      String
  slug                      String              @unique
  description               String?
  settings                  Json?               @default("{}")
  createdAt                 DateTime            @default(now())
  updatedAt                 DateTime            @updatedAt
  systemInstructionTemplate String?
  agents                    Agent[]
  groups                    Group[]
  roles                     Role[]
  users                     User[]
  searchable_entity         searchable_entity[]
}

model DeletedEntity {
  id        Int      @id @default(autoincrement())
  model     String
  modelId   String
  deletedAt DateTime @default(now())
  data      Json
}

model User {
  id                String             @id @default(cuid(2))
  username          String             @unique
  email             String             @unique
  firstName         String             @default("")
  lastName          String             @default("")
  passwordHash      String
  onboardingStep    OnboardingStep     @default(COMPLETE)
  createdAt         DateTime           @default(now())
  tenantId          String
  agents            Agent[]
  groups            GroupsOnUsers[]
  roles             RolesOnUsers[]
  tenant            Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  sessions          UserSession[]
  UserVerifications UserVerification[]
}

model UserVerification {
  id        String    @id @default(cuid(2))
  userId    String
  code      String
  createdAt DateTime  @default(now())
  expiresAt DateTime
  usedAt    DateTime?
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, code])
}

model UserSession {
  id        String   @id @default(cuid(2))
  userId    String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Role {
  id       String         @id @default(cuid(2))
  name     String
  tenantId String
  tenant   Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  users    RolesOnUsers[]

  @@unique([name, tenantId])
}

model RolesOnUsers {
  userId     String
  roleId     String
  assignedAt DateTime @default(now())
  role       Role     @relation(fields: [roleId], references: [id])
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, roleId])
}

model Group {
  id          Int             @id @default(autoincrement())
  name        String
  description String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  tenantId    String
  tenant      Tenant          @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  users       GroupsOnUsers[]

  @@unique([name, tenantId])
}

model GroupsOnUsers {
  userId     String
  groupId    Int
  assignedAt DateTime @default(now())
  group      Group    @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, groupId])
}

model Agent {
  id                         String                       @id @default(cuid(2))
  type                       AgentType
  name                       String
  description                String?
  createdAt                  DateTime                     @default(now())
  updatedAt                  DateTime                     @updatedAt
  phoneNumber                String?                      @unique
  userId                     String?
  isPrivate                  Boolean                      @default(true)
  tenantId                   String
  tenant                     Tenant                       @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy                  User?                        @relation(fields: [userId], references: [id])
  unresolvedUrl              String?
  events                     AgentEvent[]
  ConversationSession        ConversationSession[]
  SearchableEntitiesOnAgents SearchableEntitiesOnAgents[]

  @@unique([phoneNumber, tenantId])
}

model AgentEvent {
  id          String            @id @default(cuid(2))
  name        String?
  description String?
  agentId     String
  createdAt   DateTime          @default(now())
  actions     ActionsOnEvents[]
  agent       Agent             @relation(fields: [agentId], references: [id], onDelete: Cascade)
}

model AgentAction {
  id              String            @id @default(cuid(2))
  type            AgentActionType
  webhookUrl      String?
  webhookBody     String?
  createdAt       DateTime          @default(now())
  forwardToNumber String?
  events          ActionsOnEvents[]
}

model ActionsOnEvents {
  id            String      @id @default(cuid(2))
  agentEventId  String
  agentActionId String
  createdAt     DateTime    @default(now())
  agentAction   AgentAction @relation(fields: [agentActionId], references: [id], onDelete: Cascade)
  agentEvent    AgentEvent  @relation(fields: [agentEventId], references: [id], onDelete: Cascade)
}

model RequestCache {
  key       String   @unique
  value     String
  metadata  String
  createdAt DateTime @default(now())
}

model searchable_entity {
  id                        Int                      @id @default(autoincrement())
  file_name                 String
  page_number               Int?
  chunk_number              Int
  total_chunks              Int
  embedded_content          String
  embedding                 Unsupported("vector")?
  created_at                DateTime                 @default(now())
  public                    Boolean                  @default(false)
  embedded_content_summary  String?
  embedding_content_summary Unsupported("vector")?
  document_id               String?
  search_vector             Unsupported("tsvector")?
  chunk_reference_count     Int                      @default(0)
  document_reference_count  Int                      @default(0)
  tenant_id                 String?
  tenant                    Tenant?                  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
}

model SearchableEntitiesOnAgents {
  agentId                     String
  searchable_entity_file_name String
  agent                       Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@id([agentId, searchable_entity_file_name])
}

model ConversationSession {
  id        String                @id @default(cuid())
  agentId   String
  createdAt DateTime              @default(now())
  endedAt   DateTime?
  resolved  Boolean?              @default(true)
  messages  ConversationMessage[]
  agent     Agent                 @relation(fields: [agentId], references: [id], onDelete: Cascade)
}

model ConversationMessage {
  id        String              @id @default(cuid())
  sessionId String
  sender    MessageSender
  content   String
  createdAt DateTime            @default(now())
  session   ConversationSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
}

enum OnboardingStep {
  PASSWORD_CREATION
  COMPLETE
}

enum AgentType {
  EMAIL
  PHONE
  CHAT
}

enum AgentActionType {
  FORWARD_EMAIL
  CREATE_TICKET
  EDIT_TICKET
  CUSTOM
  FORWARD_CALL
  FORWARD_TO_HUMAN
}

enum MessageSender {
  USER
  AI
}
