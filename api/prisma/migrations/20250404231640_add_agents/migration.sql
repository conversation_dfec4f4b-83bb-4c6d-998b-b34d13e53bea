-- Create<PERSON><PERSON>
CREATE TYPE "AgentType" AS ENUM ('EMAIL', 'PHON<PERSON>', 'CHAT');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "AgentActionType" AS ENUM ('FORWARD_EMAIL', 'CREATE_TICKET', 'EDIT_TICKET', 'CUSTOM');

-- CreateTable
CREATE TABLE "Agent" (
    "id" TEXT NOT NULL,
    "type" "AgentType" NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Agent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AgentEvent" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "agentId" TEXT NOT NULL,
    "data" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AgentEvent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AgentAction" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "AgentActionType" NOT NULL,
    "webhookUrl" TEXT,
    "webhookBody" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AgentAction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ActionsOnEvents" (
    "id" TEXT NOT NULL,
    "agentEventId" TEXT NOT NULL,
    "agentActionId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ActionsOnEvents_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "AgentEvent" ADD CONSTRAINT "AgentEvent_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "Agent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ActionsOnEvents" ADD CONSTRAINT "ActionsOnEvents_agentEventId_fkey" FOREIGN KEY ("agentEventId") REFERENCES "AgentEvent"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ActionsOnEvents" ADD CONSTRAINT "ActionsOnEvents_agentActionId_fkey" FOREIGN KEY ("agentActionId") REFERENCES "AgentAction"("id") ON DELETE CASCADE ON UPDATE CASCADE;
