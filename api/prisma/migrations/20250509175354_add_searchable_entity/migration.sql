-- CreateExtension
CREATE EXTENSION IF NOT EXISTS "vector";

-- CreateTable
CREATE TABLE "searchable_entity" (
    "id" SERIAL NOT NULL,
    "file_name" TEXT NOT NULL,
    "page_number" INTEGER,
    "chunk_number" INTEGER NOT NULL,
    "total_chunks" INTEGER NOT NULL,
    "embedded_content" TEXT NOT NULL,
    "embedding" vector(1536) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "searchable_entity_pkey" PRIMARY KEY ("id")
);

CREATE INDEX IF NOT EXISTS idx_searchable_entity_file_name ON searchable_entity(file_name);
CREATE INDEX IF NOT EXISTS idx_searchable_entity_page_number ON searchable_entity(page_number);