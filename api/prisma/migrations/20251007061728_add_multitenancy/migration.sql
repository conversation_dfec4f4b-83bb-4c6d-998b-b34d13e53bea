/*
  Warnings:

  - A unique constraint covering the columns `[phoneNumber,tenantId]` on the table `Agent` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name,tenantId]` on the table `Group` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name,tenantId]` on the table `Role` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `tenantId` to the `Agent` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenantId` to the `Group` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenantId` to the `Role` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenantId` to the `User` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "Group_name_key";

-- DropIndex
DROP INDEX "Role_name_key";

-- AlterTable
ALTER TABLE "Agent" ADD COLUMN     "tenantId" TEXT;

-- AlterTable
ALTER TABLE "Group" ADD COLUMN     "tenantId" TEXT;

-- AlterTable
ALTER TABLE "Role" ADD COLUMN     "tenantId" TEXT;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "tenantId" TEXT;

-- CreateTable
CREATE TABLE "Tenant" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "description" TEXT,
    "settings" JSONB DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Tenant_pkey" PRIMARY KEY ("id")
);

-- Create default tenant
INSERT INTO "Tenant" (id, name, slug, description, settings, "createdAt", "updatedAt")
VALUES (
    'sifive',
    'SiFive Tenant',
    'sifive',
    'SiFive tenant for existing data',
    '{}',
    NOW(),
    NOW()
);

-- Update all existing users to belong to the default tenant
UPDATE "User" 
SET "tenantId" = 'sifive'
WHERE "tenantId" IS NULL;

-- Update all existing agents to belong to the default tenant
UPDATE "Agent" 
SET "tenantId" = 'sifive'
WHERE "tenantId" IS NULL;

-- Update all existing groups to belong to the default tenant
UPDATE "Group" 
SET "tenantId" = 'sifive'
WHERE "tenantId" IS NULL;

-- Update all existing roles to belong to the default tenant
UPDATE "Role" 
SET "tenantId" = 'sifive'
WHERE "tenantId" IS NULL;

-- Make tenantId columns NOT NULL after populating them
ALTER TABLE "User" ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "Agent" ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "Group" ALTER COLUMN "tenantId" SET NOT NULL;
ALTER TABLE "Role" ALTER COLUMN "tenantId" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Tenant_slug_key" ON "Tenant"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "Agent_phoneNumber_tenantId_key" ON "Agent"("phoneNumber", "tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "Group_name_tenantId_key" ON "Group"("name", "tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "Role_name_tenantId_key" ON "Role"("name", "tenantId");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Role" ADD CONSTRAINT "Role_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Group" ADD CONSTRAINT "Group_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Agent" ADD CONSTRAINT "Agent_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;
