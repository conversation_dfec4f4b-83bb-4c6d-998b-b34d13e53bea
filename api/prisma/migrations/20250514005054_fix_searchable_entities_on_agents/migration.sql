/*
  Warnings:

  - You are about to drop the column `agent_id` on the `searchable_entity` table. All the data in the column will be lost.

*/
-- DropIndex
DROP INDEX "idx_searchable_entity_file_name";

-- DropIndex
DROP INDEX "idx_searchable_entity_page_number";

-- AlterTable
ALTER TABLE "searchable_entity" DROP COLUMN "agent_id";

-- CreateTable
CREATE TABLE "SearchableEntitiesOnAgents" (
    "agentId" TEXT NOT NULL,
    "searchable_entity_file_name" TEXT NOT NULL,

    CONSTRAINT "SearchableEntitiesOnAgents_pkey" PRIMARY KEY ("agentId","searchable_entity_file_name")
);

-- AddF<PERSON>ign<PERSON><PERSON>
ALTER TABLE "SearchableEntitiesOnAgents" ADD CONSTRAINT "SearchableEntitiesOnAgents_agentId_fkey" FOREIGN KEY ("agentId") REFERENCES "Agent"("id") ON DELETE CASCADE ON UPDATE CASCADE;
