import { getQueueToken } from "@nestjs/bullmq";
import { INestApplication } from "@nestjs/common";

import { Queue } from "bullmq";
import { createHmac } from "crypto";
import * as supertest from "supertest";

import { PRISMIC_SYNC_QUEUE } from "../../src/webhooks/types";
import { startApp, stopApp } from "../util";

describe("Prismic webhook", () => {
    let app: INestApplication;
    let request: ReturnType<typeof supertest.agent>;
    let queue: Queue;
    let addSpy: jest.SpyInstance;
    const secret = "super-secret";

    beforeAll(async () => {
        process.env["PRISMIC_WEBHOOK_SECRET"] = secret;
        process.env["PRISMIC_REPO"] = "example-repo";
        process.env["PRISMIC_ACCESS_TOKEN"] = "example-token";
        process.env["PRISMIC_TENANT_ID"] = "tenant-123";
        process.env["INGESTION_SERVICE_URL"] = "https://example.com/ingest";
        process.env["INGESTION_SERVICE_API_KEY"] = "api-key";

        app = await startApp();
        queue = app.get<Queue>(getQueueToken(PRISMIC_SYNC_QUEUE));
        addSpy = jest.spyOn(queue, "add").mockResolvedValue({} as never);
        request = supertest.agent(app.getHttpServer());
    });

    afterAll(async () => {
        await stopApp(app);
    });

    afterEach(() => {
        addSpy.mockClear();
    });

    const signPayload = (payload: object) => {
        const raw = JSON.stringify(payload);
        const signature = createHmac("sha256", secret)
            .update(raw)
            .digest("hex");
        return { raw, signature };
    };

    it("rejects requests with invalid signature", async () => {
        const payload = { type: "api-update", documents: [] };
        const { raw } = signPayload(payload);

        await request
            .post("/api/webhooks/prismic")
            .set("Content-Type", "application/json")
            .set("x-prismic-signature", "deadbeef")
            .send(raw)
            .expect(400);
    });

    it("acknowledges test-trigger without queuing", async () => {
        const payload = { type: "test-trigger" };
        const { raw, signature } = signPayload(payload);

        const response = await request
            .post("/api/webhooks/prismic")
            .set("Content-Type", "application/json")
            .set("x-prismic-signature", signature)
            .send(raw)
            .expect(200);

        expect(response.body.status).toBe("success");
        expect(addSpy).not.toHaveBeenCalled();
    });

    it("queues incremental sync for api-update", async () => {
        const payload = {
            type: "api-update",
            masterRef: "ref-123",
            documents: [
                {
                    id: "abc123",
                    type: "page",
                    lang: "en-us",
                    tags: [],
                },
                {
                    id: "def456",
                    type: "page",
                    lang: "en-us",
                    tags: [],
                },
            ],
        };
        const { raw, signature } = signPayload(payload);

        await request
            .post("/api/webhooks/prismic")
            .set("Content-Type", "application/json")
            .set("x-prismic-signature", signature)
            .send(raw)
            .expect(200);

        expect(addSpy).toHaveBeenCalledTimes(1);
        const [, jobData] = addSpy.mock.calls[0];
        expect(jobData.documentIds).toEqual(["abc123", "def456"]);
        expect(jobData.mode).toBe("incremental");
    });
});
