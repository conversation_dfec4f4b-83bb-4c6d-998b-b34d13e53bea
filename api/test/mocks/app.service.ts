import { AppService } from "../../src/app.service";

export class MockAppService extends AppService {
    private isDbHealthy = true;
    private isRedisHealthy = true;

    setIsDbHealthly(isHealthy: boolean) {
        this.isDbHealthy = isHealthy;
    }

    setIsRedisHealthly(isHealthy: boolean) {
        this.isRedisHealthy = isHealthy;
    }

    async isDatabaseConnectionHealthy(): Promise<boolean> {
        return this.isDbHealthy;
    }

    async isRedisConnectionHealthy(): Promise<boolean> {
        return this.isRedisHealthy;
    }
}
