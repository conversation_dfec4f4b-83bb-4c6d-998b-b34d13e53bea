import { IncomingPhoneNumberInstance } from "twilio/lib/rest/api/v2010/account/incomingPhoneNumber";

import { TwilioApiService } from "../../src/twilio-api/twilio-api.service";

export class MockTwilioApiService extends TwilioApiService {
    private activeNumbersResponse: IncomingPhoneNumberInstance[];
    private callUpdate: Parameters<MockTwilioApiService["updateCall"]>;

    setMockActiveNumbersResponse(activeNumbers: IncomingPhoneNumberInstance[]) {
        this.activeNumbersResponse = activeNumbers;
    }

    async getActiveNumbers(): Promise<IncomingPhoneNumberInstance[]> {
        return this.activeNumbersResponse;
    }

    async updateCall(streamSid: string, twiml: string) {
        this.callUpdate = [streamSid, twiml];
    }

    getCallUpdate() {
        return this.callUpdate;
    }
}
