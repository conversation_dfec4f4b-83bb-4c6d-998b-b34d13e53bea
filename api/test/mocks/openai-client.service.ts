import { OpenAIRealtimeWebSocket } from "openai/beta/realtime/websocket";
import {
    RealtimeClientEvent,
    RealtimeServerEvent,
} from "openai/resources/beta/realtime/realtime";
import { CreateEmbeddingResponse } from "openai/resources/index";

import { OpenaiClientService } from "../../src/openai-client/openai-client.service";

type CallbackMap = {
    [E in RealtimeServerEvent as E["type"]]?:
        | ((event: RealtimeServerEvent) => void)
        | null;
};

type Session = {
    callbacks: CallbackMap;
    isSocketOpen: boolean;
};
export class MockOpenaiClientService extends OpenaiClientService {
    private messages: RealtimeClientEvent[] = [];

    private sessions: Session[] = [];
    private activeSession: number = 0;
    private embedding: number[];

    reset() {
        this.messages = [];
        this.sessions = [];
        this.activeSession = 0;
        this.embedding = null;
    }

    setActiveSession(session: number) {
        this.activeSession = session;
    }

    openWebSocket() {
        this.sessions.push({
            callbacks: {},
            isSocketOpen: true,
        });

        return null;
    }

    closeWebSocket() {
        if (!this.sessions[this.activeSession]) return;
        this.sessions[this.activeSession].isSocketOpen = false;
    }

    isSocketOpen(session = 0) {
        return this.sessions[session].isSocketOpen;
    }

    sendMessage(socket: OpenAIRealtimeWebSocket, message: RealtimeClientEvent) {
        this.messages.push(message);
    }

    getMessages() {
        return this.messages;
    }

    sendMockRealtimeServerEvent<T extends RealtimeServerEvent>(
        eventType: T["type"],
        event: T,
        session = 0,
    ) {
        if (!this.sessions[session].callbacks[eventType]) {
            console.warn(
                `MockOpenaiClientService: No listener for ${eventType}`,
            );
            return;
        }
        this.sessions[session].callbacks[eventType](event);
    }

    listenForRealtimeServerEvent(
        socket: OpenAIRealtimeWebSocket,
        eventType: Parameters<OpenAIRealtimeWebSocket["on"]>[0],
        cb: Parameters<OpenAIRealtimeWebSocket["on"]>[1],
    ) {
        this.sessions[this.activeSession].callbacks[eventType] = cb;
    }

    setEmbedding(embedding: number[]) {
        this.embedding = embedding;
    }

    async getEmbedding(): Promise<CreateEmbeddingResponse> {
        return {
            data: [
                {
                    embedding: this.embedding,
                    index: 0,
                    object: "embedding",
                },
            ],
            model: "",
            usage: {
                prompt_tokens: 0,
                total_tokens: 0,
            },
            object: "list",
        };
    }
}
