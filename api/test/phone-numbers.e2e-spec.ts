import { INestApplication } from "@nestjs/common";

import { IncomingPhoneNumberInstance } from "twilio/lib/rest/api/v2010/account/incomingPhoneNumber";

import { TwilioApiService } from "../src/twilio-api/twilio-api.service";
import { URL_BASE } from "./constants";
import { MockTwilioApiService } from "./mocks/twilio-api.service";
import { loginAsAdmin, startApp, stopApp } from "./util";

describe("Phone numbers", () => {
    let app: INestApplication;
    let mockTwilioApiService: MockTwilioApiService;

    beforeAll(async () => {
        mockTwilioApiService = new MockTwilioApiService();
        app = await startApp({
            providerOverrides: [[TwilioApiService, mockTwilioApiService]],
        });
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("GET /phone-numbers", async () => {
        const request = await loginAsAdmin(URL_BASE);

        const fakePhoneNumbers = [
            {
                phoneNumber: "****** 555 5555",
                voiceUrl: `${URL_BASE}/calls/incoming-call`,
            } as IncomingPhoneNumberInstance,
            {
                phoneNumber: "****** 555 5555",
                voiceUrl: `https://example.com/calls/incoming-call`,
            } as IncomingPhoneNumberInstance,
            {
                phoneNumber: "****** 555 5555",
                voiceUrl: `https://subdomain.localhost.com/calls/incoming-call`,
            } as IncomingPhoneNumberInstance,
        ];
        mockTwilioApiService.setMockActiveNumbersResponse(fakePhoneNumbers);

        const phoneNumberResponse = await request
            .get("/phone-numbers")
            .expect(200);

        const phoneNumbers = phoneNumberResponse.body.items;
        expect(phoneNumbers.length).toEqual(1);
        expect(phoneNumbers[0].phoneNumber).toEqual("****** 555 5555");
        expect(phoneNumbers[0].isInUse).toEqual(false);
    });
});
