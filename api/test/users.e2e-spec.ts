import { INestApplication } from "@nestjs/common";

import * as supertest from "supertest";

import { URL_BASE } from "./constants";
import { startApp, stopApp } from "./util";

describe("Users", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await startApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("GET /users/me 401", async () => {
        const request = supertest.agent(URL_BASE);
        await request.get("/users/me").expect(401);
    });
});
