import { INestApplication, Logger } from "@nestjs/common";

import { Queue, QueueEvents } from "bullmq";
import {
    ConversationItemCreateEvent,
    ResponseOutputItemDoneEvent,
    SessionUpdateEvent,
} from "openai/resources/beta/realtime/realtime";
import WebSocket from "ws";

import { CreateAgentResponseDto } from "../../src/agents/dto/create-agent.dto";
import { OpenaiClientService } from "../../src/openai-client/openai-client.service";
import { MockOpenaiClientService } from "../mocks/openai-client.service";
import { startApp, stopApp, waitFor } from "../util";
import { createAgent, deleteAgent, startChat } from "./util";

describe("Chat", () => {
    let app: INestApplication;
    const mockOpenAiClientService = new MockOpenaiClientService();

    beforeAll(async () => {
        app = await startApp({
            providerOverrides: [[OpenaiClientService, mockOpenAiClientService]],
        });
        app.useLogger(new Logger());
    });

    afterEach(() => {
        mockOpenAiClientService.reset();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    describe("Chat flow", () => {
        let agent: CreateAgentResponseDto;
        let websocket: WebSocket;

        beforeAll(async () => {
            agent = await createAgent();
        });

        beforeEach(async () => {
            const chat = await startChat(agent.id, mockOpenAiClientService);
            websocket = chat.websocket;
        });

        afterEach(() => {
            mockOpenAiClientService.reset();
        });

        afterAll(async () => {
            await deleteAgent(agent);
        });

        it("Complete chat flow", async () => {
            // Check that the OpenAI session is initialized with system message, initial prompt, and command to create a response
            await waitFor(() => {
                expect(
                    mockOpenAiClientService.getMessages().length,
                ).toBeGreaterThanOrEqual(3);
                return true;
            });
            expect(mockOpenAiClientService.getMessages()[0]).toMatchObject({
                type: "session.update",
            });
            expect(mockOpenAiClientService.getMessages()[1]).toMatchObject({
                type: "conversation.item.create",
            });
            expect(mockOpenAiClientService.getMessages()[2]).toMatchObject({
                type: "response.create",
            });

            // Keep track of response messages
            const websocketResponseMessages: unknown[] = [];
            websocket.onmessage = (message) => {
                const data =
                    typeof message.data === "string"
                        ? message.data
                        : message.data.toString();
                websocketResponseMessages.push(JSON.parse(data));
            };

            // Send a message
            websocket.send(
                JSON.stringify({
                    event: "messages",
                    messages: [
                        {
                            role: "user",
                            text: "Hello",
                        },
                    ],
                }),
            );

            // Check that the message is passed through to OpenAI
            await waitFor(() => mockOpenAiClientService.getMessages()[3]);
            const expectedMessage: ConversationItemCreateEvent = {
                type: "conversation.item.create",
                item: {
                    role: "user",
                    type: "message",
                    content: [
                        {
                            type: "input_text",
                            text: "Hello",
                        },
                    ],
                },
            };
            expect(mockOpenAiClientService.getMessages()[3]).toEqual(
                expectedMessage,
            );

            // Send response like OpenAI would
            mockOpenAiClientService.sendMockRealtimeServerEvent(
                "response.text.delta",
                {
                    type: "response.text.delta",
                    delta: "Response to hello",
                    response_id: "test-response-id",
                    content_index: 0,
                    event_id: "test-event-id",
                    item_id: "test-item-id",
                    output_index: 0,
                },
            );
            mockOpenAiClientService.sendMockRealtimeServerEvent(
                "response.text.done",
                {
                    type: "response.text.done",
                    text: "Response to hello",
                    response_id: "test-response-id",
                    content_index: 0,
                    event_id: "test-event-id",
                    item_id: "test-item-id",
                    output_index: 0,
                },
            );

            // Check response
            await waitFor(() => websocketResponseMessages[0]);
            const expectedResponse = {
                text: "Response to hello",
            };
            expect(websocketResponseMessages[0]).toEqual(expectedResponse);

            // End chat
            websocket.close();

            // Check that OpenAI socket connection is closed too
            await waitFor(() => {
                expect(mockOpenAiClientService.isSocketOpen()).toBeFalsy();
                return true;
            });
        });

        it("Chat uses agent context", async () => {
            const sessionUpdateMessage =
                mockOpenAiClientService.getMessages()[0];
            expect(sessionUpdateMessage).toMatchObject({
                type: "session.update",
                session: {
                    modalities: ["text"],
                    temperature: 0.8,
                },
            });

            const systemInstruction =
                sessionUpdateMessage["session"].instructions;
            expect(systemInstruction).toMatch(/Test agent description/);
        });

        describe("Events", () => {
            let queue: Queue;
            let queueEvents: QueueEvents;

            beforeAll(async () => {
                queue = new Queue("Chat agent events", {
                    connection: {},
                    prefix: "{BULLMQ}",
                });
                queueEvents = new QueueEvents("Chat agent events", {
                    connection: {},
                    prefix: "{BULLMQ}",
                });
                await queueEvents.waitUntilReady();
            });

            afterAll(async () => {
                await queue.close();
                await queueEvents.close();
            });

            it("triggers events and creates BullMQ jobs", async () => {
                const jobs = [];
                queueEvents.on("added", async ({ jobId }) => {
                    const job = await queue.getJob(jobId);
                    jobs.push(job);
                });

                // Check that function calling tools are provided to OpenAI
                const sessionUpdateMessage =
                    mockOpenAiClientService.getMessages()[0];
                const expctedTools: SessionUpdateEvent.Session.Tool[] = [
                    {
                        type: "function",
                        name: "support_request",
                        description: "Customer needs support",
                    },
                ];
                expect(sessionUpdateMessage).toMatchObject({
                    type: "session.update",
                    session: {
                        tools: expect.arrayContaining(expctedTools),
                    },
                });

                // Send function call like OpenAI would
                const functionCall: ResponseOutputItemDoneEvent = {
                    type: "response.output_item.done",
                    item: {
                        name: "support_request",
                        type: "function_call",
                    },
                    event_id: "test-event",
                    output_index: 0,
                    response_id: "test-response",
                };
                mockOpenAiClientService.sendMockRealtimeServerEvent(
                    "response.output_item.done",
                    functionCall,
                );

                // Check that event is triggered
                await waitFor(() => {
                    expect(jobs).toMatchObject([
                        {
                            name: "Support request",
                            data: {
                                agent: agent.name,
                            },
                        },
                    ]);
                    return true;
                });

                // Close connection
                websocket.close();
            });
        });
    });
});
