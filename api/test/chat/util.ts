import { randomUUID } from "crypto";
import { SessionCreatedEvent } from "openai/resources/beta/realtime/realtime";
import { MockOpenaiClientService } from "test/mocks/openai-client.service";

import { CreateAgentResponseDto } from "../../src/agents/dto/create-agent.dto";
import { loginAsAdmin, waitFor } from "../util";

export async function createAgent(): Promise<CreateAgentResponseDto> {
    const adminSession = await loginAsAdmin();

    // Create agent
    const agentName = `new-agent-${randomUUID()}`;
    const agentDescription = "Test agent description";
    const createAgentResponse = await adminSession
        .post("/agents")
        .send({
            name: agentName,
            type: "CHAT",
            description: agentDescription,
        })
        .expect(201);

    const agent = createAgentResponse.body;

    // Add an event to agent
    await adminSession
        .put(`/agents/${agent.id}`)
        .send({
            ...agent,
            events: [
                {
                    name: "Support request",
                    description: "Customer needs support",
                    actions: [
                        {
                            type: "CREATE_TICKET",
                        },
                    ],
                },
            ],
        })
        .expect(204);

    return agent;
}

export async function deleteAgent(agent: CreateAgentResponseDto) {
    const adminSession = await loginAsAdmin();
    await adminSession.delete(`/agents/${agent.id}`).expect(204);
}

export async function startChat(
    agentId: string,
    mockOpenAiClientService: MockOpenaiClientService,
) {
    const websocketUrl = `ws://localhost:${process.env["PORT"]}/api/chat?agentId=${agentId}`;

    const websocket = new WebSocket(websocketUrl);

    await new Promise((res) => {
        websocket.onopen = res;
    });

    // Start the session like OpenAI would
    const sessionStartedEvent: SessionCreatedEvent = {
        type: "session.created",
        event_id: "1",
        session: {},
    };

    // Wait for our app to attach its listener for session.created, then send the event
    await waitFor(() => {
        mockOpenAiClientService.sendMockRealtimeServerEvent(
            "session.created",
            sessionStartedEvent,
        );
        return true;
    });

    return {
        websocket,
    };
}

export function endChat(websocket: WebSocket) {
    websocket.close();
}
