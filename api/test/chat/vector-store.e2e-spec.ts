import { INestApplication, Logger } from "@nestjs/common";

import { SessionUpdateEvent } from "openai/resources/beta/realtime/realtime";

import { CreateAgentResponseDto } from "../../src/agents/dto/create-agent.dto";
import { OpenaiClientService } from "../../src/openai-client/openai-client.service";
import { MockOpenaiClientService } from "../mocks/openai-client.service";
import { startApp, stopApp, waitFor } from "../util";
import { createAgent, endChat, startChat } from "./util";

describe("Chat", () => {
    let app: INestApplication;
    const mockOpenAiClientService = new MockOpenaiClientService();

    beforeAll(async () => {
        app = await startApp({
            providerOverrides: [[OpenaiClientService, mockOpenAiClientService]],
        });
        app.useLogger(new Logger());
    });

    afterEach(() => {
        mockOpenAiClientService.reset();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    describe("Vector store lookup tool", () => {
        let agent: CreateAgentResponseDto;

        beforeAll(async () => {
            agent = await createAgent();
        });

        describe("Agent with no embeddings", () => {
            it("Is not provided if agent has no available embeddings", async () => {
                const call = await startChat(agent.id, mockOpenAiClientService);

                // Session update was sent
                await waitFor(() => {
                    expect(
                        mockOpenAiClientService.getMessages().length,
                    ).toBeGreaterThan(0);
                    return true;
                });

                // Check that no lookup function was present
                const sessionUpdateEvent: SessionUpdateEvent =
                    mockOpenAiClientService.getMessages()[0] as SessionUpdateEvent;
                expect(sessionUpdateEvent.session.tools).not.toEqual(
                    expect.arrayContaining([
                        expect.objectContaining({ name: "lookup" }),
                    ]),
                );

                endChat(call.websocket);
            });
        });
    });
});
