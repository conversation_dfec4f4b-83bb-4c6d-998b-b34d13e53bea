import { INestApplication, Logger } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";
import {
    ResponseOutputItemDoneEvent,
    SessionUpdateEvent,
} from "openai/resources/beta/realtime/realtime";

import { CreateAgentResponseDto } from "../../src/agents/dto/create-agent.dto";
import { OpenaiClientService } from "../../src/openai-client/openai-client.service";
import { MockOpenaiClientService } from "../mocks/openai-client.service";
import { loginAsAdmin, startApp, stopApp, waitFor } from "../util";
import { createAgent, endChat, startChat } from "./util";

describe("Chat", () => {
    let app: INestApplication;
    const mockOpenAiClientService = new MockOpenaiClientService();

    beforeAll(async () => {
        app = await startApp({
            providerOverrides: [[OpenaiClientService, mockOpenAiClientService]],
        });
        app.useLogger(new Logger());
    });

    afterEach(() => {
        mockOpenAiClientService.reset();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    describe("Vector store lookup tool", () => {
        let agent: CreateAgentResponseDto;
        let embeddedContent: string;
        let embedding: number[];

        beforeAll(async () => {
            agent = await createAgent();
        });

        describe("Agent with no embeddings", () => {
            it("Is not provided if agent has no available embeddings", async () => {
                const call = await startChat(agent.id, mockOpenAiClientService);

                // Session update was sent
                await waitFor(() => {
                    expect(
                        mockOpenAiClientService.getMessages().length,
                    ).toBeGreaterThan(0);
                    return true;
                });

                // Check that no lookup function was present
                const sessionUpdateEvent: SessionUpdateEvent =
                    mockOpenAiClientService.getMessages()[0] as SessionUpdateEvent;
                expect(sessionUpdateEvent.session.tools).not.toEqual(
                    expect.arrayContaining([
                        expect.objectContaining({ name: "lookup" }),
                    ]),
                );

                endChat(call.websocket);
            });
        });

        describe("Agent with embeddings", () => {
            beforeAll(async () => {
                // Insert some dummy vector embeddings
                const fileName = `${randomUUID()}.pdf`;
                embeddedContent = `embedded content ${randomUUID()}`;
                embedding = Array.from({ length: 1536 }).map(Math.random);
                const prisma = new PrismaClient();
                await prisma.$executeRaw`
                    INSERT INTO searchable_entity (file_name, chunk_number, total_chunks, embedded_content, embedding)
                    VALUES (${fileName}, 0, 1, ${embeddedContent}, ${embedding}::vector)
                `;

                // Attach embeddings to this agent
                const adminSession = await loginAsAdmin();
                await adminSession
                    .post(`/agents/${agent.id}/files`)
                    .send({
                        files: [fileName],
                    })
                    .expect(201);

                // Make OpenAI embed queries with the exact embedding we generated
                mockOpenAiClientService.setEmbedding(embedding);
            });

            it("Lookup tool is provided", async () => {
                const call = await startChat(agent.id, mockOpenAiClientService);

                // Session update was sent
                await waitFor(() => {
                    expect(
                        mockOpenAiClientService.getMessages().length,
                    ).toBeGreaterThan(0);
                    return true;
                });

                // Check that lookup function was present
                const sessionUpdateEvent: SessionUpdateEvent =
                    mockOpenAiClientService.getMessages()[0] as SessionUpdateEvent;
                expect(sessionUpdateEvent.session.tools).toEqual(
                    expect.arrayContaining([
                        expect.objectContaining({ name: "lookup" }),
                    ]),
                );

                // Send the lookup tool call like OpenAI would
                const functionCall: ResponseOutputItemDoneEvent = {
                    type: "response.output_item.done",
                    item: {
                        name: "lookup",
                        type: "function_call",
                        arguments: JSON.stringify({
                            question: `Tell me about ${randomUUID()}`,
                        }),
                    },
                    event_id: "test-event",
                    output_index: 0,
                    response_id: "test-response",
                };
                mockOpenAiClientService.sendMockRealtimeServerEvent(
                    "response.output_item.done",
                    functionCall,
                );

                // Check that OpenAI got the embedded content as the result of the lookup
                await waitFor(async () => {
                    expect(
                        mockOpenAiClientService.getMessages()[3],
                    ).toMatchObject({
                        item: {
                            type: "function_call_output",
                            output: expect.stringContaining(embeddedContent),
                        },
                    });
                    return true;
                });

                // Check that OpenAI was prompted to say something
                await waitFor(async () => {
                    expect(
                        mockOpenAiClientService.getMessages()[4],
                    ).toMatchObject({
                        type: "response.create",
                    });
                    return true;
                });

                endChat(call.websocket);
            });
        });
    });
});
