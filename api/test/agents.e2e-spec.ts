import { INestApplication } from "@nestjs/common";

import { PrismaClient } from "@prisma/client";
import { randomUUID } from "crypto";
import * as supertest from "supertest";

import { URL_BASE } from "./constants";
import { loginAsAdmin, startApp, stopApp } from "./util";

describe("Agents", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await startApp();

        // Delete agent to free up phone number
        const prisma = new PrismaClient();
        await prisma.agent.deleteMany({
            where: { phoneNumber: "+12185035025" },
        });
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("POST /agents 401", async () => {
        const request = supertest.agent(URL_BASE);
        await request.post("/agents").expect(401);
    });

    it("POST /agents 400", async () => {
        const request = await loginAsAdmin(URL_BASE);
        await request.post("/agents").send({}).expect(400);
    });

    it("POST /agents 201", async () => {
        const request = await loginAsAdmin(URL_BASE);
        await request
            .post("/agents")
            .send({
                name: "Test agent",
                type: "PHONE",
            })
            .expect(201);
    });

    it("GET /agents 401", async () => {
        const request = supertest.agent(URL_BASE);
        await request.get("/agents").expect(401);
    });

    it("GET /agents 200", async () => {
        const request = await loginAsAdmin(URL_BASE);

        // Create an agent
        const name = `agent-${randomUUID()}`;
        const type = "PHONE";
        const phoneNumber = "+12185035025";
        await request
            .post("/agents")
            .send({ name, type, phoneNumber })
            .expect(201);

        // Check that it is returned
        const agentsResponse = await request.get("/agents").expect(200);
        const agents = agentsResponse.body.items;

        expect(agents.find((agent) => agent.name === name)).toMatchObject({
            name,
            type,
            phoneNumber,
        });
    });

    it("DELETE /agents 404", async () => {
        const request = await loginAsAdmin(URL_BASE);

        await request.delete("/agents/does-not-exist").expect(404);
    });

    it("DELETE /agents 204", async () => {
        const request = await loginAsAdmin(URL_BASE);

        // Create an agent
        const name = `agent-${randomUUID()}`;
        const type = "PHONE";
        const createAgentResponse = await request
            .post("/agents")
            .send({ name, type })
            .expect(201);
        const agentId = createAgentResponse.body.id;

        // Delete the agent
        await request.delete(`/agents/${agentId}`).expect(204);

        // Check that it is no longer present
        const agentsResponse = await request.get("/agents").expect(200);
        const agents = agentsResponse.body.items;

        expect(agents.find((agent) => agent.name === name)).not.toBeDefined();
    });

    it("PUT /agents 204", async () => {
        const request = await loginAsAdmin(URL_BASE);

        // Create an agent
        const name = `agent-${randomUUID()}`;
        const type = "PHONE";
        const createAgentResponse = await request
            .post("/agents")
            .send({ name, type })
            .expect(201);
        const agentId = createAgentResponse.body.id;

        // Change the agent name, description, phone number, and add an event
        const newName = `new-agent-${randomUUID()}`;
        await request
            .put(`/agents/${agentId}`)
            .send({
                name: newName,
                description: "New description",
                phoneNumber: "+14385550123",
                events: [
                    {
                        name: "New Event",
                        description: "Event description",
                        actions: [
                            {
                                type: "FORWARD_EMAIL",
                            },
                            {
                                type: "CREATE_TICKET",
                            },
                            {
                                type: "CUSTOM",
                                webhookUrl: "https://example.com",
                                webhookBody: "{}",
                            },
                        ],
                    },
                ],
            })
            .expect(204);

        // Check that the agent is persisted
        {
            const agentsResponse = await request.get("/agents").expect(200);
            const agents = agentsResponse.body.items;

            const newAgent = agents.find((agent) => agent.name === newName);
            expect(newAgent).toBeDefined();
            expect(newAgent.description).toEqual("New description");
            expect(newAgent.phoneNumber).toEqual("+14385550123");
            expect(newAgent.events.length).toEqual(1);
            expect(newAgent.events[0]).toMatchObject({
                name: "New Event",
                description: "Event description",
            });
            expect(newAgent.events[0].actions.length).toEqual(3);
        }

        // Update the agents events by changing an action
        await request
            .put(`/agents/${agentId}`)
            .send({
                name: newName,
                description: "New description",
                events: [
                    {
                        name: "New Event",
                        description: "Event description",
                        actions: [
                            {
                                type: "EDIT_TICKET",
                            },
                        ],
                    },
                ],
            })
            .expect(204);

        // Check that the event now has the EDIT_TICKET action only
        {
            const agentsResponse = await request.get("/agents").expect(200);
            const agents = agentsResponse.body.items;

            const newAgent = agents.find((agent) => agent.name === newName);
            expect(newAgent).toBeDefined();
            expect(newAgent.events.length).toEqual(1);
            expect(newAgent.events[0].actions.length).toEqual(1);
            expect(newAgent.events[0].actions[0]).toMatchObject({
                type: "EDIT_TICKET",
            });
        }

        // Update the agent by removing an event
        await request
            .put(`/agents/${agentId}`)
            .send({
                name: newName,
                description: "New description",
                events: [],
            })
            .expect(204);

        // Check that the event is no longer returned on the agent
        {
            const agentsResponse = await request.get("/agents").expect(200);
            const agents = agentsResponse.body.items;

            const newAgent = agents.find((agent) => agent.name === newName);
            expect(newAgent).toBeDefined();
            expect(newAgent.events.length).toEqual(0);
        }
    });
});
