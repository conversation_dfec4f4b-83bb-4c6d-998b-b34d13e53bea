process.env["ENVIRONMENT"] = process.env["ENVIRONMENT"] ?? "test";
process.env["S3_INGEST"] = process.env["S3_INGEST"] ?? "test-ingest-bucket";
process.env["AWS_ACCESS_KEY"] =
    process.env["AWS_ACCESS_KEY"] ?? "test-access-key";
process.env["AWS_SECRET_KEY"] =
    process.env["AWS_SECRET_KEY"] ?? "test-secret-key";
process.env["INGESTION_SERVICE_URL"] =
    process.env["INGESTION_SERVICE_URL"] ?? "http://localhost:4000";
