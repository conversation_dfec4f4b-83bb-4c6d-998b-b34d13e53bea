import "./env-defaults";

import { INestApplication } from "@nestjs/common";
import { NestExpressApplication } from "@nestjs/platform-express";
import { Test } from "@nestjs/testing";

import * as supertest from "supertest";

import { setupApp } from "../src/app-config";
import { URL_BASE } from "./constants";

type Class = { new (): object };

type ProviderOverride = [provider: Class, value: object];
type ProviderOverrides = ProviderOverride[];

export async function startApp(options?: {
    providerOverrides?: ProviderOverrides;
}) {
    process.env["ENVIRONMENT"] = process.env["ENVIRONMENT"] ?? "test";
    process.env["S3_INGEST"] = process.env["S3_INGEST"] ?? "test-ingest-bucket";
    process.env["AWS_ACCESS_KEY"] =
        process.env["AWS_ACCESS_KEY"] ?? "test-access-key";
    process.env["AWS_SECRET_KEY"] =
        process.env["AWS_SECRET_KEY"] ?? "test-secret-key";
    process.env["INGESTION_SERVICE_URL"] =
        process.env["INGESTION_SERVICE_URL"] ?? "http://localhost:4000";

    const { AppModule } = await import("../src/app.module");
    const { providerOverrides } = options || {};
    const moduleBuilder = await Test.createTestingModule({
        imports: [AppModule],
    });

    const moduleRef = await (providerOverrides || [])
        .reduce(
            (builder, [provider, value]) =>
                builder.overrideProvider(provider).useValue(value),
            moduleBuilder,
        )
        .compile();

    const app: NestExpressApplication = moduleRef.createNestApplication();

    await setupApp(app);

    return app;
}

export async function stopApp(app: INestApplication) {
    await app.close();
}

export async function loginAsAdmin(host: string = URL_BASE) {
    const request = supertest.agent(host);

    const username = process.env["ADMIN_USER"];
    const password = process.env["ADMIN_PASS"];

    await request.post("/login").send({ username, password }).expect(204);

    return request;
}

export async function waitFor<ReturnValue>(
    cb: () => ReturnValue | Promise<ReturnValue>,
    timeout = 2500,
) {
    const endTime = Date.now() + timeout;
    let lastError;
    while (Date.now() < endTime) {
        try {
            const response = await cb();
            if (response) return response;
        } catch (e: unknown) {
            lastError = e;
        }
        await new Promise((r) => setTimeout(r, 100));
    }
    throw lastError;
}
