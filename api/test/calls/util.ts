import { randomUUID } from "crypto";
import { SessionCreatedEvent } from "openai/resources/beta/realtime/realtime";
import * as supertest from "supertest";

import { CreateAgentResponseDto } from "../../src/agents/dto/create-agent.dto";
import { URL_BASE } from "../constants";
import { MockOpenaiClientService } from "../mocks/openai-client.service";
import { loginAsAdmin, waitFor } from "../util";

export async function createAgent(): Promise<CreateAgentResponseDto> {
    const adminSession = await loginAsAdmin(URL_BASE);

    // Create agent
    const agentName = `new-agent-${randomUUID()}`;
    const agentDescription = "Test agent description";
    const createAgentResponse = await adminSession
        .post("/agents")
        .send({
            name: agentName,
            type: "PHONE",
            description: agentDescription,
            phoneNumber: "+***********",
        })
        .expect(201);

    const agent = createAgentResponse.body;

    // Add an event to agent
    await adminSession
        .put(`/agents/${agent.id}`)
        .send({
            ...agent,
            events: [
                {
                    name: "Support request",
                    description: "Customer needs support",
                    actions: [
                        {
                            type: "CREATE_TICKET",
                        },
                    ],
                },
            ],
        })
        .expect(204);

    return agent;
}

export async function deleteAgent(agent: CreateAgentResponseDto) {
    const adminSession = await loginAsAdmin(URL_BASE);
    await adminSession.delete(`/agents/${agent.id}`).expect(204);
}

export async function startCall(
    mockOpenAiClientService: MockOpenaiClientService,
) {
    // Start call
    const request = supertest.agent(URL_BASE);

    const CallSid = randomUUID();
    const body = new URLSearchParams({
        AccountSid: randomUUID(),
        CallSid,
        To: "+***********",
        From: "+***********",
    });

    const response = await request
        .set("Content-Type", "application/x-www-form-urlencoded")
        .post("/calls/incoming-call")
        .send(body.toString())
        .expect(201);

    const websocketUrl = `ws://localhost:${process.env["PORT"]}/api/media-stream`;
    const expectedTwiml = `<?xml version="1.0" encoding="UTF-8"?><Response><Connect><Stream url="${websocketUrl}"><Parameter name="To" value="+***********"/><Parameter name="From" value="+***********"/></Stream></Connect></Response>`;
    expect(response.text).toEqual(expectedTwiml);

    // Open socket connection to our backend like Twilio would
    const websocket = new WebSocket(websocketUrl);

    // Wait for websocket to open
    await new Promise((res) => {
        websocket.onopen = res;
    });

    // Send "start" message like Twilio would
    const streamSid = `stream-${randomUUID()}`;
    const startEvent = {
        event: "start",
        sequenceNumber: 1,
        streamSid,
        start: {
            callSid: CallSid,
            customParameters: {
                To: "+***********",
                From: "+***********",
            },
        },
    };
    websocket.send(JSON.stringify(startEvent));

    // Start the session like OpenAI would
    const sessionStartedEvent: SessionCreatedEvent = {
        type: "session.created",
        event_id: "1",
        session: {},
    };

    // Wait for our app to attach its listener for session.created, then send the event
    await waitFor(() => {
        mockOpenAiClientService.sendMockRealtimeServerEvent(
            "session.created",
            sessionStartedEvent,
        );
        return true;
    });

    return { websocket, streamSid };
}

export function endCall(websocket: WebSocket, streamSid: string) {
    // Send "stop" event like Twilio would
    websocket.send(
        JSON.stringify({
            event: "stop",
            sequenceNumber: 4,
            streamSid,
        }),
    );
    websocket.close();
}
