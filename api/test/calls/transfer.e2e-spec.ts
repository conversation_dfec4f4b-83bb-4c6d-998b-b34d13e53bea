import { INestApplication, Logger } from "@nestjs/common";

import {
    ResponseOutputItemDoneEvent,
    SessionUpdateEvent,
} from "openai/resources/beta/realtime/realtime";

import { CreateAgentResponseDto } from "../../src/agents/dto/create-agent.dto";
import { OpenaiClientService } from "../../src/openai-client/openai-client.service";
import { TwilioApiService } from "../../src/twilio-api/twilio-api.service";
import { MockOpenaiClientService } from "../mocks/openai-client.service";
import { MockTwilioApiService } from "../mocks/twilio-api.service";
import { loginAsAdmin, startApp, stopApp, waitFor } from "../util";
import { createAgent, deleteAgent, endCall, startCall } from "./util";

describe("Calls", () => {
    let app: INestApplication;
    const mockOpenAiClientService = new MockOpenaiClientService();
    const mockTwilioApiService = new MockTwilioApiService();

    beforeAll(async () => {
        app = await startApp({
            providerOverrides: [
                [OpenaiClientService, mockOpenAiClientService],
                [TwilioApiService, mockTwilioApiService],
            ],
        });
        app.useLogger(new Logger());
    });

    afterEach(() => {
        mockOpenAiClientService.reset();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    describe("Forward call", () => {
        let agent: CreateAgentResponseDto;

        beforeAll(async () => {
            agent = await createAgent();

            // Ensure agent has a call forward event/action
            const adminSession = await loginAsAdmin();
            await adminSession
                .put(`/agents/${agent.id}`)
                .send({
                    ...agent,
                    events: [
                        {
                            name: "Forward call to a human",
                            description: "Customer needs support",
                            actions: [
                                {
                                    type: "FORWARD_CALL",
                                    forwardToNumber: "+19055554156",
                                },
                            ],
                        },
                    ],
                })
                .expect(204);
        });

        afterAll(async () => {
            // Delete the agent to free up its phone number
            await deleteAgent(agent);
        });

        it("Handles call forward tool call", async () => {
            const call = await startCall(mockOpenAiClientService);

            // Session update was sent
            await waitFor(() => {
                expect(
                    mockOpenAiClientService.getMessages().length,
                ).toBeGreaterThan(0);
                return true;
            });

            // Check that FORWARD_CALL function was present
            const sessionUpdateEvent: SessionUpdateEvent =
                mockOpenAiClientService.getMessages()[0] as SessionUpdateEvent;

            expect(sessionUpdateEvent.session.tools[1]).toMatchObject({
                name: "forward_to_19055554156",
            });

            // Send the FORWARD_CALL tool call like OpenAI would
            const functionCall: ResponseOutputItemDoneEvent = {
                type: "response.output_item.done",
                item: {
                    name: "forward_to_19055554156",
                    type: "function_call",
                },
                event_id: "test-event",
                output_index: 0,
                response_id: "test-response",
            };

            mockOpenAiClientService.sendMockRealtimeServerEvent(
                "response.output_item.done",
                functionCall,
            );

            // Check that OpenAI got the result of the tool call
            await waitFor(async () => {
                expect(mockOpenAiClientService.getMessages()[3]).toMatchObject({
                    item: {
                        type: "function_call_output",
                        output: "success",
                    },
                });
                return true;
            });

            // Check that OpenAI was prompted to say something
            await waitFor(async () => {
                expect(mockOpenAiClientService.getMessages()[4]).toMatchObject({
                    type: "response.create",
                });
                return true;
            });

            // Check that the call was transferred with Twilio
            await waitFor(async () => {
                const twilioCallUpdate = mockTwilioApiService.getCallUpdate();
                expect(twilioCallUpdate).toMatchObject([
                    expect.any(String),
                    `<?xml version="1.0" encoding="UTF-8"?><Response><Dial>+19055554156</Dial></Response>`,
                ]);
                return true;
            });

            endCall(call.websocket, call.streamSid);
        });
    });
});
