import { INestApplication } from "@nestjs/common";

import { randomUUID } from "crypto";
import * as supertest from "supertest";
import * as WebSocket from "ws";

import { URL_BASE } from "../constants";
import { startApp, stopApp } from "../util";

describe("Calls", () => {
    let app: INestApplication;

    beforeAll(async () => {
        app = await startApp();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it(`/calls/incoming-call "To" number is validated`, async () => {
        const request = supertest.agent(URL_BASE);

        const body = new URLSearchParams({
            To: "+1 647 324 1234",
            From: "+1 416 555 0101",
        });

        await request
            .set("Content-Type", "application/x-www-form-urlencoded")
            .post("/calls/incoming-call")
            .send(body.toString())
            .expect(404);
    });

    it(`ws /media-stream "To" number is validated`, async () => {
        const websocketUrl = `ws://localhost:${process.env["PORT"]}/api/media-stream`;

        // Open socket connection to our backend like Twilio would
        const websocket = new WebSocket(websocketUrl);

        // Wait for websocket to open
        await new Promise((res) => {
            websocket.onopen = res;
        });

        // Send "start" message like Twilio would but with a bad phone number
        const streamSid = `stream-${randomUUID()}`;
        const startEvent = {
            event: "start",
            sequenceNumber: 1,
            streamSid,
            start: {
                customParameters: {
                    To: "bad-phone-number",
                },
            },
        };
        websocket.send(JSON.stringify(startEvent));

        // Wait for websocket to close
        await new Promise((res) => {
            websocket.onclose = res;
        });
    });
});
