import { INestApplication, Logger } from "@nestjs/common";

import { Queue, QueueEvents } from "bullmq";
import {
    InputAudioBufferSpeechStartedEvent,
    ResponseAudioDeltaEvent,
    ResponseOutputItemDoneEvent,
    SessionUpdateEvent,
} from "openai/resources/beta/realtime/realtime";
import WebSocket from "ws";

import { CreateAgentResponseDto } from "../../src/agents/dto/create-agent.dto";
import { OpenaiClientService } from "../../src/openai-client/openai-client.service";
import { MockOpenaiClientService } from "../mocks/openai-client.service";
import { startApp, stopApp, waitFor } from "../util";
import { createAgent, deleteAgent, endCall, startCall } from "./util";

describe("Calls", () => {
    let app: INestApplication;
    const mockOpenAiClientService = new MockOpenaiClientService();

    beforeAll(async () => {
        app = await startApp({
            providerOverrides: [[OpenaiClientService, mockOpenAiClientService]],
        });
        app.useLogger(new Logger());
    });

    afterEach(() => {
        mockOpenAiClientService.reset();
    });

    afterAll(async () => {
        await stopApp(app);
    });

    describe("Call flow", () => {
        let agent: CreateAgentResponseDto;
        let websocket: WebSocket;
        let streamSid;

        beforeAll(async () => {
            agent = await createAgent();
        });

        beforeEach(async () => {
            const call = await startCall(mockOpenAiClientService);
            websocket = call.websocket;
            streamSid = call.streamSid;
        });

        afterEach(() => {
            endCall(websocket, streamSid);
        });

        afterAll(async () => {
            // Free up the phone number used by our agent
            await deleteAgent(agent);
        });

        it("Complete call flow", async () => {
            // Listen for messages back from our server
            const websocketResponseMessages: unknown[] = [];
            websocket.onmessage = (message) => {
                const data =
                    typeof message.data === "string"
                        ? message.data
                        : message.data.toString();
                websocketResponseMessages.push(JSON.parse(data));
            };

            // Check that the OpenAI session is initialized with system message, initial prompt, and command to create a response
            await waitFor(() => {
                expect(
                    mockOpenAiClientService.getMessages().length,
                ).toBeGreaterThanOrEqual(3);
                return true;
            });
            expect(mockOpenAiClientService.getMessages()[0]).toMatchObject({
                type: "session.update",
            });
            expect(mockOpenAiClientService.getMessages()[1]).toMatchObject({
                type: "conversation.item.create",
            });
            expect(mockOpenAiClientService.getMessages()[2]).toMatchObject({
                type: "response.create",
            });

            // Send an audio media event like Twilio would
            websocket.send(
                JSON.stringify({
                    event: "media",
                    streamSid,
                    sequenceNumber: 2,
                    media: {
                        timestamp: 5,
                        payload: "hello",
                    },
                }),
            );

            // Check that the message is passed through to OpenAI
            await waitFor(() => {
                expect(
                    mockOpenAiClientService.getMessages().length,
                ).toBeGreaterThanOrEqual(4);
                return true;
            });
            expect(mockOpenAiClientService.getMessages()[3]).toEqual({
                type: "input_audio_buffer.append",
                audio: "hello",
            });

            // Send audio delta event like OpenAI would
            const audioDelta: ResponseAudioDeltaEvent = {
                type: "response.audio.delta",
                content_index: 0,
                event_id: "1",
                item_id: "test-audio-delta-item",
                output_index: 0,
                response_id: "1",
                delta: "audio payload",
            };
            mockOpenAiClientService.sendMockRealtimeServerEvent(
                "response.audio.delta",
                audioDelta,
            );

            // Check for media event
            await waitFor(() => websocketResponseMessages.length >= 1);
            expect(websocketResponseMessages[0]).toMatchObject({
                event: "media",
                streamSid,
                media: { payload: "audio payload" },
            });

            // Check that a mark event follows the media event
            await waitFor(() => websocketResponseMessages.length >= 2);
            expect(websocketResponseMessages[1]).toMatchObject({
                event: "mark",
                streamSid,
                mark: {
                    name: "responsePart",
                },
            });

            // Check that assistant speech gets trunacted correctly
            websocket.send(
                JSON.stringify({
                    event: "media",
                    streamSid,
                    sequenceNumber: 3,
                    media: {
                        timestamp: 15,
                        payload: "now let me stop you right there",
                    },
                }),
            );

            await waitFor(() => {
                expect(
                    mockOpenAiClientService.getMessages().length,
                ).toBeGreaterThanOrEqual(5);
                return true;
            });
            expect(mockOpenAiClientService.getMessages()[4]).toEqual({
                type: "input_audio_buffer.append",
                audio: "now let me stop you right there",
            });

            // Send back an "input_audio_buffer.speech_started" like OpenAI would
            const speechStartedEvent: InputAudioBufferSpeechStartedEvent = {
                type: "input_audio_buffer.speech_started",
                audio_start_ms: 0,
                event_id: "1",
                item_id: "2",
            };

            // Wait for our app to attach its listener for input_audio_buffer.speech_started, then send the event
            await waitFor(() => {
                mockOpenAiClientService.sendMockRealtimeServerEvent(
                    "input_audio_buffer.speech_started",
                    speechStartedEvent,
                );
                return true;
            });

            // Check that Twilio gets the message to stop playing the assistant audio
            await waitFor(() => websocketResponseMessages.length == 3);
            expect(websocketResponseMessages[2]).toMatchObject({
                event: "clear",
                streamSid,
            });

            // Check that OpenAI gets a message to tell it we truncated the audio it sent us
            await waitFor(() => {
                expect(
                    mockOpenAiClientService.getMessages().length,
                ).toBeGreaterThanOrEqual(6);
                return true;
            });

            expect(mockOpenAiClientService.getMessages()[5]).toMatchObject({
                type: "conversation.item.truncate",
                content_index: 0,
                item_id: "test-audio-delta-item",
                audio_end_ms: 10,
            });

            // Send "stop" event like Twilio would
            websocket.send(
                JSON.stringify({
                    event: "stop",
                    sequenceNumber: 4,
                    streamSid,
                }),
            );
            websocket.close();

            // Check that OpenAI socket connection is closed too
            await waitFor(() => {
                expect(mockOpenAiClientService.isSocketOpen()).toBeFalsy();
                return true;
            });
        });

        it("Call uses agent context", async () => {
            const sessionUpdateMessage =
                mockOpenAiClientService.getMessages()[0];
            expect(sessionUpdateMessage).toMatchObject({
                type: "session.update",
                session: {
                    turn_detection: { type: "semantic_vad" },
                    input_audio_format: "g711_ulaw",
                    output_audio_format: "g711_ulaw",
                    voice: "alloy",
                    modalities: ["text", "audio"],
                    temperature: 0.8,
                },
            });

            const systemInstruction =
                sessionUpdateMessage["session"].instructions;
            expect(systemInstruction).toMatch(/Test agent description/);
        });

        it("Concurrent calls", async () => {
            // Open a second websocket
            const websockets = [
                websocket,
                new WebSocket(
                    `ws://localhost:${process.env["PORT"]}/api/media-stream`,
                ),
            ];

            // Listen for messages back from each websocket
            const websocketResponseMessages: unknown[][] = [[], []];
            websockets[0].onmessage = (message) => {
                const data =
                    typeof message.data === "string"
                        ? message.data
                        : message.data.toString();
                websocketResponseMessages[0].push(JSON.parse(data));
            };
            websockets[1].onmessage = (message) => {
                const data =
                    typeof message.data === "string"
                        ? message.data
                        : message.data.toString();
                websocketResponseMessages[1].push(JSON.parse(data));
            };

            // Wait for second websocket to open
            await new Promise((res) => {
                websockets[1].onopen = res;
            });

            // Send "start" message like Twilio would to second websocket
            mockOpenAiClientService.setActiveSession(1);
            websockets[1].send(
                JSON.stringify({
                    event: "start",
                    sequenceNumber: "1",
                    streamSid: "67890",
                    start: {
                        customParameters: {
                            To: "+16473318231",
                        },
                    },
                }),
            );

            // Send audio delta events like OpenAI would
            const audioDelta: ResponseAudioDeltaEvent = {
                type: "response.audio.delta",
                content_index: 0,
                event_id: "1",
                item_id: "test-audio-delta-item",
                output_index: 0,
                response_id: "1",
                delta: "audio payload",
            };

            await waitFor(() => {
                mockOpenAiClientService.sendMockRealtimeServerEvent(
                    "response.audio.delta",
                    audioDelta,
                    0,
                );
                return true;
            });

            await waitFor(() => {
                mockOpenAiClientService.sendMockRealtimeServerEvent(
                    "response.audio.delta",
                    audioDelta,
                    1,
                );
                return true;
            });

            // Check for media events
            await waitFor(() => {
                expect(
                    websocketResponseMessages[0].length,
                ).toBeGreaterThanOrEqual(1);
                return true;
            });
            expect(websocketResponseMessages[0][0]).toMatchObject({
                event: "media",
                streamSid,
                media: { payload: "audio payload" },
            });

            await waitFor(() => {
                expect(
                    websocketResponseMessages[1].length,
                ).toBeGreaterThanOrEqual(1);
                return true;
            });
            expect(websocketResponseMessages[1][0]).toMatchObject({
                event: "media",
                streamSid: "67890",
                media: { payload: "audio payload" },
            });
        });

        it("Agent can hang up", async () => {
            // Send function call like OpenAI would
            const functionCall: ResponseOutputItemDoneEvent = {
                type: "response.output_item.done",
                item: {
                    call_id: "test-call-id",
                    name: "hang_up",
                    type: "function_call",
                },
                event_id: "test-event",
                output_index: 0,
                response_id: "test-response",
            };
            mockOpenAiClientService.sendMockRealtimeServerEvent(
                "response.output_item.done",
                functionCall,
            );

            // Check that OpenAI is prompted to say goodbye
            await waitFor(async () => {
                expect(mockOpenAiClientService.getMessages()[3]).toMatchObject({
                    type: "response.create",
                });
                return true;
            });

            // Check that twilio socket connection is closed
            await waitFor(() => {
                expect(websocket.readyState).toEqual(WebSocket.CLOSED);
                return true;
            });

            // Check that OpenAI socket connection is closed too
            await waitFor(() => {
                expect(mockOpenAiClientService.isSocketOpen()).toBeFalsy();
                return true;
            });
        });

        describe("events", () => {
            let queue: Queue;
            let queueEvents: QueueEvents;

            beforeAll(async () => {
                queue = new Queue("Phone agent events", {
                    connection: {},
                    prefix: "{BULLMQ}",
                });
                queueEvents = new QueueEvents("Phone agent events", {
                    connection: {},
                    prefix: "{BULLMQ}",
                });
                await queueEvents.waitUntilReady();
            });

            afterAll(async () => {
                await queue.close();
                await queueEvents.close();
            });

            it("triggers events and creates BullMQ jobs", async () => {
                const jobs = [];
                queueEvents.on("added", async ({ jobId }) => {
                    const job = await queue.getJob(jobId);
                    jobs.push(job);
                });

                // Check that function calling tools are provided to OpenAI
                const sessionUpdateMessage =
                    mockOpenAiClientService.getMessages()[0];
                const expctedTools: SessionUpdateEvent.Session.Tool[] = [
                    {
                        type: "function",
                        name: "support_request",
                        description: "Customer needs support",
                    },
                ];
                expect(sessionUpdateMessage).toMatchObject({
                    type: "session.update",
                    session: {
                        tools: expect.arrayContaining(expctedTools),
                    },
                });

                // Send function call like OpenAI would
                const functionCall: ResponseOutputItemDoneEvent = {
                    type: "response.output_item.done",
                    item: {
                        name: "support_request",
                        type: "function_call",
                    },
                    event_id: "test-event",
                    output_index: 0,
                    response_id: "test-response",
                };
                mockOpenAiClientService.sendMockRealtimeServerEvent(
                    "response.output_item.done",
                    functionCall,
                );

                // Check that event is triggered
                await waitFor(() => {
                    expect(jobs).toMatchObject([
                        {
                            name: "Support request",
                            data: {
                                agent: agent.name,
                                caller: "+14165550101",
                            },
                        },
                    ]);
                    return true;
                });

                // Close connection
                websocket.close();
            });
        });
    });
});
