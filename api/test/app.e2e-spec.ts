import { INestApplication } from "@nestjs/common";

import * as supertest from "supertest";

import { AppService } from "../src/app.service";
import { URL_BASE } from "./constants";
import { MockAppService } from "./mocks/app.service";
import { startApp, stopApp } from "./util";

describe("App", () => {
    let app: INestApplication;
    const mockAppService = new MockAppService();

    beforeAll(async () => {
        app = await startApp({
            providerOverrides: [[AppService, mockAppService]],
        });
    });

    beforeEach(() => {
        mockAppService.setIsDbHealthly(true);
        mockAppService.setIsRedisHealthly(true);
    });

    afterAll(async () => {
        await stopApp(app);
    });

    it("GET /healthcheck 503 (bad db connection)", async () => {
        mockAppService.setIsDbHealthly(false);

        const request = supertest.agent(URL_BASE);
        await request.get("/healthcheck").expect(503);
    });

    it("GET /healthcheck 503 (bad redis connection)", async () => {
        mockAppService.setIsRedisHealthly(false);

        const request = supertest.agent(URL_BASE);
        await request.get("/healthcheck").expect(503);
    });

    it("GET /healthcheck 200", async () => {
        const request = supertest.agent(URL_BASE);
        await request.get("/healthcheck").expect(200);
    });
});
